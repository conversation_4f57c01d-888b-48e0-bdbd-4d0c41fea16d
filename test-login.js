const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

// デバッグ出力を有効化
console.log('Script started');

async function testLogin(email, password) {
  try {
    console.log(`Testing login for email: ${email}`);

    // ユーザーを検索
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
    });

    if (!user) {
      console.log('User not found with this email');
      return false;
    }

    console.log(`User found: ${user.username} (${user.id})`);

    // パスワードを検証
    const isPasswordValid = await bcrypt.compare(
      password,
      user.password
    );

    if (!isPasswordValid) {
      console.log('Password is invalid');
      return false;
    }

    console.log('Password is valid');
    return true;
  } catch (error) {
    console.error('Error during login test:', error);
    return false;
  }
}

async function main() {
  try {
    // テストするログイン情報
    const testCredentials = [
      { email: '<EMAIL>', password: 'Sugisan9898' },
      { email: '<EMAIL>', password: 'Sugisan9898' },
    ];

    for (const cred of testCredentials) {
      console.log('\n-----------------------------------');
      const result = await testLogin(cred.email, cred.password);
      console.log(`Login test for ${cred.email}: ${result ? 'SUCCESS' : 'FAILED'}`);
    }

  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
