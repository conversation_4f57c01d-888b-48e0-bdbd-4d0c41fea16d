# 造物者の空気感 (<PERSON><PERSON><PERSON><PERSON><PERSON>) - デプロイメントガイド

このガイドでは、「造物者の空気感」ウェブサイトをNginxリバースプロキシを使用して本番環境にデプロイする方法を説明します。

## 前提条件

- Node.js v18以上
- npm v8以上
- Nginx
- PM2（Node.jsプロセス管理ツール）
- （オプション）Let's Encrypt（SSL証明書用）

## デプロイ手順

### 1. アプリケーションのビルド

```bash
# リポジトリをクローン
git clone https://github.com/TKS1352/zobustusha.git
cd zobustusha

# 依存パッケージをインストール
npm install

# 環境変数ファイルを設定
cp .env.example .env
# .envファイルを編集して必要な環境変数を設定

# Prismaクライアントを生成
npx prisma generate

# アプリケーションをビルド
npm run build
```

### 2. PM2を使用したアプリケーションの実行

```bash
# PM2をグローバルにインストール
npm install -g pm2

# ecosystem.config.jsを使用してアプリケーションを起動
pm2 start ecosystem.config.js

# 起動スクリプトを生成（システム再起動時に自動起動）
pm2 startup

# 現在の設定を保存
pm2 save
```

### 3. Nginxのインストールと設定

#### macOSの場合

```bash
# Homebrewを使用してNginxをインストール
brew install nginx

# Nginxの設定ディレクトリに移動
cd /usr/local/etc/nginx/servers/

# 設定ファイルを作成
sudo nano zobutsusha.conf
# nginx.conf.exampleの内容をコピーして貼り付け、必要に応じて編集

# Nginxを起動
brew services start nginx
```

#### Ubuntu/Debianの場合

```bash
# Nginxをインストール
sudo apt update
sudo apt install nginx

# 設定ファイルを作成
sudo nano /etc/nginx/sites-available/zobutsusha

# シンボリックリンクを作成
sudo ln -s /etc/nginx/sites-available/zobutsusha /etc/nginx/sites-enabled/

# 設定をテスト
sudo nginx -t

# Nginxを再起動
sudo systemctl restart nginx

# 自動起動を有効化
sudo systemctl enable nginx
```

### 4. HTTPSの設定（オプション）

#### Let's Encryptを使用したSSL証明書の取得

##### macOSの場合

```bash
# certbotをインストール
brew install certbot

# 証明書を取得
sudo certbot certonly --standalone -d your-domain.com
```

##### Ubuntu/Debianの場合

```bash
# certbotをインストール
sudo apt install certbot python3-certbot-nginx

# Nginxプラグインを使用して証明書を取得と設定
sudo certbot --nginx -d your-domain.com
```

### 5. ファイアウォールの設定（必要な場合）

#### Ubuntu/Debianの場合

```bash
# UFWを有効化
sudo ufw enable

# HTTP/HTTPSトラフィックを許可
sudo ufw allow 'Nginx Full'
```

## トラブルシューティング

### ログの確認

```bash
# Nginxのエラーログを確認
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/zobutsusha.error.log

# PM2のログを確認
pm2 logs zobutsusha
```

### 一般的な問題

1. **ポートが既に使用されている**
   - `lsof -i :3000` を実行して、ポート3000を使用しているプロセスを確認
   - 必要に応じてプロセスを終了するか、別のポートを使用

2. **Nginxが起動しない**
   - `nginx -t` を実行して設定ファイルの構文エラーを確認
   - ログファイルを確認

3. **アプリケーションにアクセスできない**
   - PM2でアプリケーションが実行されていることを確認: `pm2 status`
   - Nginxが実行されていることを確認: `systemctl status nginx` または `brew services list`
   - ファイアウォールの設定を確認

## メンテナンス

### アプリケーションの更新

```bash
# リポジトリの最新コードを取得
git pull

# 依存パッケージを更新
npm install

# Prismaクライアントを再生成
npx prisma generate

# アプリケーションを再ビルド
npm run build

# PM2でアプリケーションを再起動
pm2 restart zobutsusha
```

### SSL証明書の更新

Let's Encryptの証明書は90日ごとに更新する必要があります。自動更新を設定するには：

```bash
# 自動更新のcronジョブを設定
sudo crontab -e
```

以下の行を追加：

```
0 3 * * * certbot renew --quiet && systemctl reload nginx
```

これにより、毎日午前3時に証明書の更新チェックが行われます。
