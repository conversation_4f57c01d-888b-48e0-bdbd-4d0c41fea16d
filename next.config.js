/** @type {import('next').NextConfig} */
const nextConfig = {
  // ビルド時のデータベース接続エラーを無視
  serverExternalPackages: ['@prisma/client', 'bcrypt'],
  reactStrictMode: true,
  images: {
    domains: [],
    formats: ['image/avif', 'image/webp'],
  },
  // 静的エクスポートを無効化
  output: 'standalone',
  // ベースURLの設定 - 開発環境でも絶対URLを使用しないように変更
  assetPrefix: undefined,
  basePath: '',
  // リバースプロキシ設定
  poweredByHeader: false,
  // サーバーとクライアントの設定
  serverRuntimeConfig: {
    // サーバーサイドのみで利用可能な設定
  },
  publicRuntimeConfig: {
    // クライアントとサーバーサイドの両方で利用可能な設定
    baseUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  },
  // サンプルソースコードを除外
  outputFileTracingExcludes: {
    '*': ['**/developmentdocs/**'],
  },
  typescript: {
    // ビルド時にTypeScriptエラーを無視（プロダクション用設定）
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  // seed.tsファイルをビルドから除外
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('./prisma/seed.ts');
    }
    return config;
  },
};

module.exports = nextConfig;
