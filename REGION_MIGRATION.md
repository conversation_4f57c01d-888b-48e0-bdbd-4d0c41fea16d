# Cloud Run リージョン移行ガイド

## 概要
このプロジェクトは、Cloud Runのデプロイ先リージョンを `us-central1` から `asia-northeast1`（東京）に移行しました。

## 変更内容

### 1. cloudbuild.yaml の変更
- Cloud Runデプロイ先リージョンを `us-central1` から `asia-northeast1` に変更

### 2. パフォーマンス向上の期待値
- **ネットワークレイテンシー**: 日本からのアクセスで大幅な改善
- **レスポンス時間**: 20-30%の高速化が期待される
- **ユーザー体験**: Web アプリケーションの体感速度向上

### 3. 移行手順

#### ステップ1: コードの更新
```bash
git add .
git commit -m "feat: Cloud Runデプロイ先をasia-northeast1（東京）に変更"
git push origin main
```

#### ステップ2: Cloud Buildトリガーの実行
- GitHubにプッシュ後、Cloud Buildが自動実行される
- 新しいリージョン（asia-northeast1）にサービスがデプロイされる

#### ステップ3: DNSの更新（必要に応じて）
- カスタムドメインを使用している場合、新しいCloud RunのURLに向ける必要がある場合があります
- 移行後のURLを確認して、必要に応じてDNS設定を更新してください

#### ステップ4: 旧リージョンのクリーンアップ
- 移行が完了し、正常動作を確認後、us-central1の古いサービスを削除することを推奨

## 注意事項

### データベース接続
- NeonDB（ap-southeast-1）への接続は変更なし
- asia-northeast1からap-southeast-1への接続は地理的に近いため、レイテンシーはほぼ同等

### カスタムドメイン
- www.zobutsusha.com のマッピングが影響を受ける可能性があります
- 移行後にドメインマッピングを確認し、必要に応じて再設定してください

### 監視とログ
- Cloud Runのログとメトリクスの場所がasia-northeast1に変更されます
- モニタリングダッシュボードでリージョンの設定を確認してください

## トラブルシューティング

### よくある問題
1. **カスタムドメインが機能しない**
   - Cloud Run管理画面でカスタムドメインのマッピング状況を確認
   - 必要に応じてドメインマッピングを再設定

2. **デプロイエラー**
   - Cloud Buildのログを確認
   - asia-northeast1でのサービス作成権限を確認

3. **パフォーマンスが期待より向上しない**
   - データベース接続先（NeonDB）のリージョンも考慮
   - CDNやキャッシュ設定の確認

## 確認方法

### デプロイ確認
```bash
gcloud run services list --region=asia-northeast1
```

### サービスの詳細確認
```bash
gcloud run services describe zobutsusha --region=asia-northeast1
```

### パフォーマンステスト
- 移行前後でのレスポンス時間比較
- 日本国内からのアクセステスト実施 