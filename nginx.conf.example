server {
    listen 80;
    server_name your-domain.com;  # 実際のドメイン名に変更してください

    # ログの設定
    access_log /var/log/nginx/zobutsusha.access.log;
    error_log /var/log/nginx/zobutsusha.error.log;

    # プロキシヘッダーの設定
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;

    # Next.jsアプリケーションへのリバースプロキシ
    location / {
        proxy_pass http://localhost:3000;
    }

    # 静的ファイルの処理（オプション）
    location /_next/static/ {
        alias /Users/<USER>/Projects/zobutsusha2/.next/static/;
        expires 365d;
        access_log off;
    }

    # 画像などの静的ファイル（オプション）
    location /images/ {
        alias /Users/<USER>/Projects/zobutsusha2/public/images/;
        expires 7d;
        access_log off;
    }
}

# HTTPS設定の例（Let's Encryptを使用した場合）
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#
#     ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
#     ssl_trusted_certificate /etc/letsencrypt/live/your-domain.com/chain.pem;
#
#     # SSL設定
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_prefer_server_ciphers on;
#     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
#     ssl_session_timeout 1d;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_tickets off;
#
#     # HSTS設定
#     add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
#
#     # ログの設定
#     access_log /var/log/nginx/zobutsusha.access.log;
#     error_log /var/log/nginx/zobutsusha.error.log;
#
#     # プロキシヘッダーの設定
#     proxy_http_version 1.1;
#     proxy_set_header Upgrade $http_upgrade;
#     proxy_set_header Connection 'upgrade';
#     proxy_set_header Host $host;
#     proxy_set_header X-Real-IP $remote_addr;
#     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     proxy_set_header X-Forwarded-Proto $scheme;
#     proxy_cache_bypass $http_upgrade;
#
#     # Next.jsアプリケーションへのリバースプロキシ
#     location / {
#         proxy_pass http://localhost:3000;
#     }
#
#     # 静的ファイルの処理（オプション）
#     location /_next/static/ {
#         alias /Users/<USER>/Projects/zobutsusha2/.next/static/;
#         expires 365d;
#         access_log off;
#     }
#
#     # 画像などの静的ファイル（オプション）
#     location /images/ {
#         alias /Users/<USER>/Projects/zobutsusha2/public/images/;
#         expires 7d;
#         access_log off;
#     }
# }
#
# # HTTPからHTTPSへのリダイレクト
# server {
#     listen 80;
#     server_name your-domain.com;
#     return 301 https://$host$request_uri;
# }
