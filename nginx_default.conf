# client_max_body_size 10M; # vite.confで設定済み
client_body_buffer_size 10M;
client_body_temp_path /tmp/nginx_client_body_temp;

# upstream vite_backend { # vite.confで設定済み
#     server 127.0.0.1:3000;
#     server 127.0.0.1:3001 backup;
# }

server {
    listen 80;
    server_name localhost;

    # ブラウザキャッシュの無効化（開発環境向け）
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";

    location / {
        proxy_pass http://vite_backend;
        proxy_buffering off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocketサポート（Viteの開発サーバーではHMRに必要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # タイムアウト設定
        proxy_connect_timeout 10s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # バッファ設定
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;

        # エラーインターセプトを無効化
        proxy_intercept_errors off;

        # リクエストボディサイズの制限を増やす（ロケーション固有）
        client_max_body_size 10M;
    }

    # 静的ファイルのキャッシュ設定
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://vite_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1h;
        add_header Cache-Control "public";
    }

    # エラーページ
    error_page 502 /502.html;
    location = /502.html {
        root /opt/homebrew/var/www;
    }
}
