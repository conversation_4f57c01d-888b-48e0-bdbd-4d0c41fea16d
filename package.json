{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p ${PORT:-3000}", "lint": "next lint", "postinstall": "prisma generate", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.10.2", "@types/jsonwebtoken": "^9.0.9", "browser-image-compression": "^2.0.2", "jsonwebtoken": "^9.0.2", "next": "^15.3.2", "next-auth": "^4.24.11", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "sharp": "^0.33.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "bcrypt": "^6.0.0", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prisma": "^5.10.2", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}