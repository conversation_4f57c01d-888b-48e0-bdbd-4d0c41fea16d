#!/bin/bash

# NeonDB マイグレーション自動実行スクリプト
# 使用方法: ./quick-migration.sh [OLD_DATABASE_URL] [NEW_DATABASE_URL]

set -e  # エラーで停止

# 色付きの出力用
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ログ関数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 引数チェック
if [ $# -eq 0 ]; then
    log_info "NeonDB マイグレーションスクリプト"
    echo ""
    echo "使用方法:"
    echo "  $0 [OLD_DATABASE_URL] [NEW_DATABASE_URL]"
    echo ""
    echo "引数なしの場合は対話モードで実行されます。"
    echo ""
    
    read -p "現在のDATABASE_URL を入力してください: " OLD_DATABASE_URL
    read -p "新しいDATABASE_URL を入力してください: " NEW_DATABASE_URL
else
    OLD_DATABASE_URL=$1
    NEW_DATABASE_URL=$2
fi

# 新しいDATAベースURLのデフォルト値
if [ -z "$NEW_DATABASE_URL" ]; then
    NEW_DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
    log_info "新しいDATABASE_URLにデフォルト値を使用します"
fi

log_info "マイグレーション開始"
log_info "旧DB: ${OLD_DATABASE_URL:0:50}..."
log_info "新DB: ${NEW_DATABASE_URL:0:50}..."

# バックアップディレクトリ作成
BACKUP_DIR="migration_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
log_info "バックアップディレクトリ作成: $BACKUP_DIR"

# ステップ1: 現在のデータをエクスポート
log_info "ステップ1: データエクスポート開始"
export DATABASE_URL="$OLD_DATABASE_URL"

# Prismaクライアント生成
npx prisma generate

# データエクスポート実行
node migration-script.js export

# バックアップファイル移動
if [ -f "data-export.json" ]; then
    cp data-export.json "$BACKUP_DIR/"
    log_success "データエクスポート完了: $(wc -l < data-export.json) 行"
else
    log_error "エクスポートファイルが見つかりません"
    exit 1
fi

# pg_dumpでの追加バックアップ（オプション）
log_info "pg_dumpでの追加バックアップを実行中..."
if command -v pg_dump &> /dev/null; then
    pg_dump "$OLD_DATABASE_URL" > "$BACKUP_DIR/complete_backup.sql" 2>/dev/null || log_warning "pg_dumpでのバックアップに失敗しました（継続します）"
    log_success "完全バックアップ完了"
else
    log_warning "pg_dumpが見つかりません。Prismaエクスポートのみ実行します"
fi

# ステップ2: 新しいデータベースの準備
log_info "ステップ2: 新しいデータベースの準備"
export DATABASE_URL="$NEW_DATABASE_URL"

# Prismaクライアント再生成
npx prisma generate

# 新しいデータベースの接続確認
log_info "新しいデータベースへの接続確認..."
if npx prisma db execute --stdin <<< "SELECT 1;" &> /dev/null; then
    log_success "新しいデータベースに接続成功"
else
    log_error "新しいデータベースに接続できません"
    exit 1
fi

# マイグレーション実行
log_info "マイグレーション実行中..."
npx prisma migrate deploy

log_success "マイグレーション完了"

# ステップ3: データインポート
log_info "ステップ3: データインポート開始"
node migration-script.js import

log_success "データインポート完了"

# ステップ4: 動作確認
log_info "ステップ4: 動作確認"

# スキーマ確認
npx prisma db pull > /dev/null 2>&1 && log_success "スキーマ同期確認完了" || log_warning "スキーマ同期で警告が発生しました"

# データ件数確認
log_info "データ件数確認中..."
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
(async () => {
  try {
    const users = await prisma.user.count();
    const posts = await prisma.post.count();
    const categories = await prisma.category.count();
    console.log('✓ ユーザー:', users, '件');
    console.log('✓ 投稿:', posts, '件');
    console.log('✓ カテゴリ:', categories, '件');
  } catch (error) {
    console.error('データ確認エラー:', error.message);
    process.exit(1);
  } finally {
    await prisma.\$disconnect();
  }
})();
"

# 完了メッセージ
echo ""
log_success "🎉 マイグレーション完了！"
echo ""
log_info "次のステップ:"
echo "1. 本番環境のDATABASE_URL環境変数を更新"
echo "2. アプリケーションを再デプロイ"
echo "3. 動作確認後、古いデータベースを削除"
echo ""
log_info "バックアップファイル場所: $BACKUP_DIR/"
echo ""

# 環境変数更新スクリプト生成
cat > "$BACKUP_DIR/update_env.sh" << EOF
#!/bin/bash
# 本番環境の環境変数更新用スクリプト

# Cloud Run (Secret Manager使用)の場合:
echo "$NEW_DATABASE_URL" | gcloud secrets versions add DATABASE_URL --data-file=-

# Cloud Run直接更新の場合は、コンソールで以下の環境変数を設定:
echo "DATABASE_URL=$NEW_DATABASE_URL"
EOF

chmod +x "$BACKUP_DIR/update_env.sh"
log_info "環境変数更新スクリプトを生成: $BACKUP_DIR/update_env.sh"

echo "マイグレーション作業が完了しました！" 