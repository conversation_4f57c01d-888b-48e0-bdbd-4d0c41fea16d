const { PrismaClient } = require('@prisma/client');
const fs = require('fs');

// 既存のデータベースからデータをエクスポート
async function exportData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('データのエクスポートを開始します...');
    
    // 全テーブルのデータを取得
    const users = await prisma.user.findMany();
    const categories = await prisma.category.findMany();
    const subCategories = await prisma.subCategory.findMany();
    const posts = await prisma.post.findMany();
    const images = await prisma.image.findMany();
    const siteSettings = await prisma.siteSettings.findMany();
    const contactMessages = await prisma.contactMessage.findMany();
    
    const exportData = {
      users,
      categories,
      subCategories,
      posts,
      images,
      siteSettings,
      contactMessages,
      exportDate: new Date().toISOString()
    };
    
    // データをJSONファイルに保存
    fs.writeFileSync('data-export.json', JSON.stringify(exportData, null, 2));
    
    console.log('データのエクスポートが完了しました');
    console.log(`- ユーザー: ${users.length}件`);
    console.log(`- カテゴリ: ${categories.length}件`);
    console.log(`- サブカテゴリ: ${subCategories.length}件`);
    console.log(`- 投稿: ${posts.length}件`);
    console.log(`- 画像: ${images.length}件`);
    console.log(`- サイト設定: ${siteSettings.length}件`);
    console.log(`- お問い合わせ: ${contactMessages.length}件`);
    
    return exportData;
  } catch (error) {
    console.error('エクスポート中にエラーが発生しました:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 新しいデータベースにデータをインポート
async function importData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('データのインポートを開始します...');
    
    // エクスポートファイルを読み込み
    const data = JSON.parse(fs.readFileSync('data-export.json', 'utf8'));
    
    // トランザクションでデータをインポート
    await prisma.$transaction(async (tx) => {
      // 順序に注意してインポート（外部キー制約のため）
      
      // 1. ユーザー
      for (const user of data.users) {
        await tx.user.create({ data: user });
      }
      
      // 2. カテゴリ
      for (const category of data.categories) {
        await tx.category.create({ data: category });
      }
      
      // 3. サブカテゴリ
      for (const subCategory of data.subCategories) {
        await tx.subCategory.create({ data: subCategory });
      }
      
      // 4. 投稿
      for (const post of data.posts) {
        await tx.post.create({ data: post });
      }
      
      // 5. 画像
      for (const image of data.images) {
        await tx.image.create({ data: image });
      }
      
      // 6. サイト設定
      for (const setting of data.siteSettings) {
        await tx.siteSettings.create({ data: setting });
      }
      
      // 7. お問い合わせメッセージ
      for (const message of data.contactMessages) {
        await tx.contactMessage.create({ data: message });
      }
    });
    
    console.log('データのインポートが完了しました');
  } catch (error) {
    console.error('インポート中にエラーが発生しました:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// コマンドライン引数で実行モードを決定
const mode = process.argv[2];

if (mode === 'export') {
  exportData().catch(console.error);
} else if (mode === 'import') {
  importData().catch(console.error);
} else {
  console.log('使用方法:');
  console.log('  node migration-script.js export  - データをエクスポート');
  console.log('  node migration-script.js import  - データをインポート');
} 