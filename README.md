# 造物者の空気感 (<PERSON><PERSON><PERSON><PERSON><PERSON>)

「造物者の空気感」

## 技術スタック

- **フロントエンド**: Next.js (App Router), TypeScript
- **バックエンド**: Next.js API Routes, Prisma ORM
- **データベース**: NeonDB (PostgreSQL)
- **認証**: NextAuth.js
- **画像処理**: Base64エンコード
  - アップロード時にサイズ自動調整（元画像とサムネイル）

### 1. 管理者機能
- **認証システム**: 管理者ログイン機能
- **コンテンツ管理**: 
  - 新規投稿の作成・編集・削除
  - カテゴリー/サブカテゴリーの作成・編集・削除
  - 過去の投稿日を含む日付設定
  - Base64でのエンコードと保存

### 3. タイポグラフィとアイコン
- **フォント**: 読みやすさと美しさを兼ね備えたフォントの組み合わせ
- **アイコン**: ユニークでカスタムデザインのアイコン
- **文字サイズ**: 階層に応じた適切なサイズ設定

## 開発環境セットアップ

### 前提条件

- Node.js v18以上
- npm v8以上

### データベース
- **種類**: NeonDB (PostgreSQL)
- **接続文字列**: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require




### インストール手順

1. リポジトリをクローン
   ```bash
   git clone https://github.com/TKS1352/zobustusha.git
   cd zobustusha
   ```

2. 依存パッケージをインストール
   ```bash
   npm install
   ```

3. 環境変数ファイルを作成
   ```bash
   cp .env.example .env
   ```

4. .envファイルを編集し、以下の項目を設定
   - `DATABASE_URL` - NeonDBの接続文字列
   - `NEXTAUTH_SECRET` - NextAuth用のシークレットキー
   - `NEXTAUTH_URL` - アプリケーションのURL
   - `SETUP_SECRET_KEY` - 初期セットアップ用のシークレットキー

5. Prismaクライアントを生成
   ```bash
   npx prisma generate
   ```

6. 開発サーバーを起動
   ```bash
   npm run dev
   ```

7. ブラウザで http://localhost:3000 にアクセス

### 初期管理者ユーザーの作成

1. 以下のAPIエンドポイントにPOSTリクエストを送信
   ```
   POST /api/admin/setup
   ```

2. リクエストボディの例:
   ```json
   {
     "username": "admin",
     "email": "<EMAIL>",
     "password": "securepassword",
     "key": "SETUP_SECRET_KEYの値"
   }
   ```

3. 成功すると管理者アカウントが作成され、`/admin/login`でログインできるようになります

## プロジェクト構造

```
/app
  /(routes)
    /(admin)     # 管理者エリア
    /(public)    # 公開エリア
  /api           # APIエンドポイント
  /components    # 共通コンポーネント
    /admin       # 管理用コンポーネント
    /ui          # UIコンポーネント
  /hooks         # カスタムフック
  /lib           # ユーティリティ
  /styles        # スタイル関連
/prisma          # Prismaスキーマ
/public          # 静的ファイル
```

## 主要機能

- 管理者ログイン/ログアウト
- カテゴリー・サブカテゴリー管理
- ブログ投稿の作成・編集・削除
- 画像アップロードとBase64保存

