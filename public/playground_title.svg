<svg viewBox="0 0 800 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with subtle gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    
    <!-- Neon glow effect -->
    <filter id="neonGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Drop shadow -->
    <filter id="dropShadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="800" height="200" fill="url(#bgGradient)"/>
  
  <!-- Decorative game elements -->
  
  <!-- Snake segments (top left) -->
  <g transform="translate(50, 30)">
    <rect x="0" y="0" width="12" height="12" fill="#22c55e" rx="2"/>
    <rect x="14" y="0" width="12" height="12" fill="#16a34a" rx="2"/>
    <rect x="28" y="0" width="12" height="12" fill="#15803d" rx="2"/>
    <rect x="28" y="14" width="12" height="12" fill="#15803d" rx="2"/>
    <rect x="28" y="28" width="12" height="12" fill="#16a34a" rx="2"/>
  </g>
  
  <!-- Tetris blocks (top right) -->
  <g transform="translate(650, 25)">
    <!-- L-piece -->
    <rect x="0" y="0" width="10" height="10" fill="#3b82f6" stroke="#1e40af" stroke-width="1"/>
    <rect x="0" y="12" width="10" height="10" fill="#3b82f6" stroke="#1e40af" stroke-width="1"/>
    <rect x="0" y="24" width="10" height="10" fill="#3b82f6" stroke="#1e40af" stroke-width="1"/>
    <rect x="12" y="24" width="10" height="10" fill="#3b82f6" stroke="#1e40af" stroke-width="1"/>
    
    <!-- T-piece -->
    <rect x="30" y="0" width="10" height="10" fill="#f59e0b" stroke="#d97706" stroke-width="1"/>
    <rect x="18" y="12" width="10" height="10" fill="#f59e0b" stroke="#d97706" stroke-width="1"/>
    <rect x="30" y="12" width="10" height="10" fill="#f59e0b" stroke="#d97706" stroke-width="1"/>
    <rect x="42" y="12" width="10" height="10" fill="#f59e0b" stroke="#d97706" stroke-width="1"/>
  </g>
  
  <!-- Pac-Man (bottom left) -->
  <g transform="translate(80, 140)">
    <circle cx="20" cy="20" r="18" fill="#fbbf24"/>
    <path d="M 20 20 L 35 10 A 18 18 0 0 1 35 30 Z" fill="#f8fafc"/>
    <circle cx="15" cy="12" r="3" fill="#1f2937"/>
    <!-- Dots -->
    <circle cx="50" cy="20" r="3" fill="#fbbf24"/>
    <circle cx="65" cy="20" r="3" fill="#fbbf24"/>
    <circle cx="80" cy="20" r="3" fill="#fbbf24"/>
  </g>
  
  <!-- Space Invader (bottom right) -->
  <g transform="translate(670, 140)">
    <rect x="8" y="0" width="4" height="4" fill="#8b5cf6"/>
    <rect x="20" y="0" width="4" height="4" fill="#8b5cf6"/>
    <rect x="12" y="4" width="12" height="4" fill="#8b5cf6"/>
    <rect x="8" y="8" width="20" height="4" fill="#8b5cf6"/>
    <rect x="4" y="12" width="28" height="4" fill="#8b5cf6"/>
    <rect x="0" y="16" width="36" height="4" fill="#8b5cf6"/>
    <rect x="0" y="20" width="8" height="4" fill="#8b5cf6"/>
    <rect x="12" y="20" width="12" height="4" fill="#8b5cf6"/>
    <rect x="28" y="20" width="8" height="4" fill="#8b5cf6"/>
    <rect x="4" y="24" width="8" height="4" fill="#8b5cf6"/>
    <rect x="24" y="24" width="8" height="4" fill="#8b5cf6"/>
  </g>
  
  <!-- Main Title -->
  <text x="400" y="120" font-family="Arial, sans-serif" font-size="72" font-weight="bold" 
        text-anchor="middle" fill="#1e293b" filter="url(#dropShadow)">
    PLAYGROUND
  </text>
  
  <!-- Decorative underline with gaming elements -->
  <g transform="translate(200, 140)">
    <line x1="0" y1="0" x2="400" y2="0" stroke="#64748b" stroke-width="3" opacity="0.6"/>
    
    <!-- Game controller buttons -->
    <circle cx="100" cy="0" r="6" fill="#ef4444" opacity="0.8"/>
    <circle cx="120" cy="0" r="6" fill="#22c55e" opacity="0.8"/>
    <circle cx="140" cy="0" r="6" fill="#3b82f6" opacity="0.8"/>
    <circle cx="160" cy="0" r="6" fill="#f59e0b" opacity="0.8"/>
    
    <!-- Joystick -->
    <circle cx="300" cy="0" r="8" fill="#64748b" opacity="0.6"/>
    <circle cx="300" cy="0" r="4" fill="#1e293b"/>
  </g>
  
  <!-- Pixelated decorative elements -->
  <g opacity="0.3">
    <!-- Left side pixels -->
    <rect x="20" y="80" width="8" height="8" fill="#06b6d4"/>
    <rect x="30" y="85" width="8" height="8" fill="#8b5cf6"/>
    <rect x="25" y="95" width="8" height="8" fill="#f59e0b"/>
    
    <!-- Right side pixels -->
    <rect x="740" y="75" width="8" height="8" fill="#ef4444"/>
    <rect x="750" y="90" width="8" height="8" fill="#22c55e"/>
    <rect x="760" y="105" width="8" height="8" fill="#3b82f6"/>
  </g>
  
  <!-- Subtitle -->
  <text x="400" y="165" font-family="Arial, sans-serif" font-size="16" font-weight="normal" 
        text-anchor="middle" fill="#64748b" opacity="0.8">
    Simple Games Collection
  </text>
</svg>