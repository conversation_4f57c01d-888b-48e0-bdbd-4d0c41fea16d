LLMを使ったVibeCodingテストでよく作成されるシンプルなゲームには以下のようなものがあります：

**クラシックゲーム系**
- **Snake Game** - 最も人気。移動、衝突判定、スコア管理など基本要素が含まれる
- **Tetris** - ブロック操作、ライン消去、回転処理などのロジックテスト
- **Pong** - ボール物理、パドル操作、AI対戦の実装
- **Pac-Man風ゲーム** - マップ移動、アイテム収集、敵AI

**シンプルなアクション系**
- **Flappy Bird クローン** - 重力、障害物、タイミングゲーム
- **Space Invaders** - 射撃、敵の移動パターン、衝突判定
- **Frogger風** - タイミング、多レーン移動、障害物回避

**パズル・論理系**
- **2048** - 数字合成、スライド操作、ゲーム終了判定
- **Memory Card Game** - カードめくり、マッチング、記憶ゲーム
- **Tic-Tac-Toe** - 基本的なゲームロジック、勝敗判定

**シンプルなアーケード系**
- **Whack-a-Mole** - ランダム出現、クリック判定、タイマー
- **Catch the Falling Objects** - 物理シミュレーション、キャッチ判定

これらのゲームは、Canvas APIやDOM操作、イベントハンドリング、基本的なゲームループなど、JavaScript/HTMLの重要な概念を組み合わせて学習できるため、コーディングテストの題材として非常に適しています。特にSnakeとTetrisは実装の幅が広く、スキルレベルに応じて複雑さを調整できるのが特徴です。