# Stage 1: Dependencies and build
FROM node:18-alpine AS builder

# Install dependencies required for Prisma and Sharp
RUN apk add --no-cache libc6-compat python3 make g++ curl
# Install OpenSSL for Prisma
RUN apk add --no-cache openssl

WORKDIR /app

# Copy package files and Prisma schema
COPY package.json package-lock.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci

# Copy application code
COPY . .

# Generate Prisma client with dummy DATABASE_URL for build
ENV DATABASE_URL="***********************************/dummy?schema=public"
RUN npx prisma generate

# Build application with dummy DATABASE_URL
RUN npm run build

# Stage 2: Production image
FROM node:18-alpine AS runner

WORKDIR /app

# Install production dependencies only
RUN apk add --no-cache libc6-compat
# Install OpenSSL for Prisma
RUN apk add --no-cache openssl

# Set environment variables
ENV NODE_ENV production
ENV PORT 8080

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy necessary files from builder stage
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Copy the standalone output from Next.js
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy Prisma schema and migrations for potential runtime use
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# Switch to non-root user
USER nextjs

# Expose the port the app will run on
EXPOSE 8080

# Command to run the application
CMD ["node", "server.js"]
