import Link from 'next/link';
import Image from 'next/image';
import { formatDate } from '@/lib/utils';

type PostCardProps = {
  post: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    featuredImage?: string | null;
    publishedAt?: string | Date | null;
    category: {
      name: string;
      slug: string;
    };
  };
  imageIndex?: number;
};

export default function PostCard({ post, imageIndex = 0 }: PostCardProps) {
  // サンプル画像の配列
  const sampleImages = [
    '/images/dogu-figure.jpg',
    '/images/bird-icon.png',
    '/images/cat-icon.png',
    '/images/owl-icon.png'
  ];

  // インデックスに基づいて画像を選択（循環させる）
  const imageSrc = sampleImages[imageIndex % sampleImages.length];

  return (
    <div className="card flex flex-col md:flex-row h-full">
      <div className="relative h-48 md:h-auto md:w-1/3">
        {post.featuredImage ? (
          <div className="relative w-full h-full">
            <Image
              src={post.featuredImage}
              alt={post.title}
              fill
              sizes="(max-width: 768px) 100vw, 33vw"
              className="object-cover"
            />
          </div>
        ) : (
          <div className="relative w-full h-full">
            <Image
              src={imageSrc}
              alt={post.title}
              fill
              sizes="(max-width: 768px) 100vw, 33vw"
              className="object-cover"
            />
          </div>
        )}
      </div>

      <div className="p-6 flex-grow md:w-2/3">
        <div className="flex items-center mb-2 text-sm">
          <Link
            href={`/categories/${post.category.slug}`}
            className="font-medium text-accent-600 hover:text-accent-800 mr-3"
          >
            {post.category.name}
          </Link>

          {post.publishedAt && (
            <span className="text-primary-500">
              {formatDate(post.publishedAt)}
            </span>
          )}
        </div>

        <h3 className="text-xl font-serif font-medium mb-3">
          <Link href={`/posts/${post.slug}`} className="text-primary-900 hover:text-accent-700">
            {post.title}
          </Link>
        </h3>

        {post.excerpt && (
          <p className="text-primary-600 mb-4">
            {post.excerpt.length > 100 ? `${post.excerpt.substring(0, 100)}...` : post.excerpt}
          </p>
        )}

        <Link
          href={`/posts/${post.slug}`}
          className="inline-flex items-center text-accent-600 hover:text-accent-800"
        >
          続きを読む
          <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
          </svg>
        </Link>
      </div>
    </div>
  );
}
