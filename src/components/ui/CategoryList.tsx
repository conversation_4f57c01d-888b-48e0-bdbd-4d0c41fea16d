import Link from 'next/link';

type Category = {
  id: string;
  name: string;
  slug: string;
  subCategories?: {
    id: string;
    name: string;
    slug: string;
  }[];
};

type CategoryListProps = {
  categories: Category[];
};

export default function CategoryList({ categories }: CategoryListProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">カテゴリー</h2>
      
      <ul className="space-y-4">
        {categories.map((category) => (
          <li key={category.id}>
            <Link 
              href={`/categories/${category.slug}`}
              className="text-primary-800 hover:text-accent-600 font-medium"
            >
              {category.name}
            </Link>
            
            {category.subCategories && category.subCategories.length > 0 && (
              <ul className="ml-4 mt-2 space-y-1">
                {category.subCategories.map((subCategory) => (
                  <li key={subCategory.id}>
                    <Link 
                      href={`/categories/${category.slug}/${subCategory.slug}`}
                      className="text-primary-600 hover:text-accent-600 text-sm"
                    >
                      {subCategory.name}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}
