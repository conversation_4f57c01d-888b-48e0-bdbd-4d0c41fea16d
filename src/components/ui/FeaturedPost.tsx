import Link from 'next/link';
import Image from 'next/image';

type FeaturedPostProps = {
  post: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    featuredImage?: string | null;
    publishedAt?: Date | null;
    category: {
      name: string;
      slug: string;
    };
  };
};

export default function FeaturedPost({ post }: FeaturedPostProps) {
  return (
    <div className="card h-full flex flex-col">
      <div className="relative h-64 w-full">
        {post.featuredImage ? (
          <div className="relative w-full h-full">
            <Image
              src={post.featuredImage}
              alt={post.title}
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="relative w-full h-full">
            <Image
              src="/images/dogu-figure.jpg"
              alt={post.title}
              fill
              className="object-cover"
            />
          </div>
        )}
      </div>

      <div className="p-6 flex-grow flex flex-col">
        <div className="mb-2">
          <Link
            href={`/categories/${post.category.slug}`}
            className="text-sm font-medium text-accent-600 hover:text-accent-800"
          >
            {post.category.name}
          </Link>
        </div>

        <h3 className="text-xl font-serif font-medium mb-3">
          <Link href={`/posts/${post.slug}`} className="text-primary-900 hover:text-accent-700">
            {post.title}
          </Link>
        </h3>

        {post.excerpt && (
          <p className="text-primary-600 mb-4 flex-grow">
            {post.excerpt.length > 120 ? `${post.excerpt.substring(0, 120)}...` : post.excerpt}
          </p>
        )}

        <div className="mt-auto">
          <Link
            href={`/posts/${post.slug}`}
            className="inline-flex items-center text-accent-600 hover:text-accent-800"
          >
            続きを読む
            <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
}
