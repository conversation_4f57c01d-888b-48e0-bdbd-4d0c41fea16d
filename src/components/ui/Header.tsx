import Link from 'next/link';
import Image from 'next/image';
import { FaSearch } from 'react-icons/fa';
import { getSettings, getCategories } from '@/lib/api';

export default async function Header() {
  // 設定とカテゴリーを取得
  const settings = await getSettings([
    'site_title',
    'site_logo',
  ]);

  // カテゴリーを取得
  const categories = await getCategories();

  return (
    <header className="bg-white shadow-sm">
      <div className="container-custom py-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center mb-4 md:mb-0">
            <Link href="/" className="flex items-center">
              <div className="relative w-12 h-12 mr-3">
                <Image
                  src={settings.site_logo || '/images/dogu-logo.png'}
                  alt={settings.site_title || '造物者の空気感'}
                  width={48}
                  height={48}
                  className="object-contain"
                />
              </div>
              <h1 className="text-2xl font-serif font-medium text-primary-900">{settings.site_title || '造物者の空気感'}</h1>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="検索..."
                className="pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <FaSearch />
              </div>
            </div>

            <nav className="hidden md:flex space-x-6">
              <div className="relative group">
                <Link href="/categories" className="text-primary-800 hover:text-accent-600">
                  カテゴリー
                </Link>
                {categories.length > 0 && (
                  <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                    {categories.map(category => (
                      <Link
                        key={category.id}
                        href={`/categories/${category.slug}`}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        {category.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
              <Link href="/about" className="text-primary-800 hover:text-accent-600">
                プロフィール
              </Link>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}
