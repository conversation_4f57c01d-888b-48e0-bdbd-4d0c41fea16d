'use client';

import Link from 'next/link';
import Image from 'next/image';
import { FaSignOutAlt, FaUser, FaBars } from 'react-icons/fa';
import { useSidebar } from '@/contexts/SidebarContext';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

export default function AdminHeader() {
  const { user, logout } = useAdminAuth();
  const { open, isOpen } = useSidebar();

  const handleMenuClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('ハンバーガーアイコンがクリックされました');
    open();
    // 非同期の状態更新なので、ここでのisOpenはまだ更新前の値
    console.log('サイドバーの状態（更新前）:', isOpen);

    // 少し遅延させて状態を確認
    setTimeout(() => {
      console.log('サイドバーの状態（更新後）:', isOpen);
    }, 100);
  };

  const handleSignOut = () => {
    // 新しい認証システムのログアウト関数を使用
    logout();
  };

  return (
    <header className="bg-white shadow-sm sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            {/* モバイル表示時のサイドバー開閉ボタン */}
            <button
              className="md:hidden p-2 mr-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              onClick={handleMenuClick}
              aria-label="メニューを開く"
            >
              <FaBars />
            </button>

            <Link href="/admin/dashboard" className="flex items-center">
              <div className="relative w-10 h-10 mr-2">
                <Image
                  src="/images/dogu-logo.png"
                  alt="造物者の空気感"
                  width={40}
                  height={40}
                  className="object-contain"
                />
              </div>
              <span className="text-xl font-serif font-medium text-primary-900 hidden sm:inline">
                管理者ページ
              </span>
            </Link>
          </div>

          {user && (
            <div className="flex items-center">
              <div className="flex items-center mr-4">
                <FaUser className="text-gray-500 mr-2" />
                <span className="text-sm text-gray-700 hidden sm:inline">
                  {user.username || user.email || 'ユーザー'}
                </span>
              </div>
              <button
                onClick={handleSignOut}
                className="flex items-center text-sm text-gray-700 hover:text-primary-600"
                aria-label="ログアウト"
              >
                <FaSignOutAlt className="mr-1" />
                <span className="hidden sm:inline">ログアウト</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
