'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  FaHome,
  FaNewspaper,
  FaFolderOpen,
  FaUsers,
  FaCog,
  FaEnvelope,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';
import { useSidebar } from '@/contexts/SidebarContext';
import { useEffect } from 'react';

export default function AdminSidebar() {
  const pathname = usePathname();
  const { isOpen, toggle, close } = useSidebar();

  // サイドバーの状態をコンソールに出力
  console.log('AdminSidebar レンダリング - サイドバーの状態:', isOpen);

  // ウィンドウサイズが変更されたときにサイドバーを閉じる（モバイル表示時）
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        close();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [close]);

  // 画面遷移時にモバイル表示でサイドバーを閉じる
  useEffect(() => {
    if (window.innerWidth < 768) {
      close();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const isActive = (path: string) => {
    return pathname?.startsWith(path) ? 'bg-primary-100 text-primary-800' : 'text-gray-700 hover:bg-gray-100';
  };

  const menuItems = [
    {
      name: 'ダッシュボード',
      path: '/admin/dashboard',
      icon: <FaHome className="mr-3" />,
    },
    {
      name: '記事管理',
      path: '/admin/posts',
      icon: <FaNewspaper className="mr-3" />,
    },
    {
      name: 'カテゴリー管理',
      path: '/admin/categories',
      icon: <FaFolderOpen className="mr-3" />,
    },
    {
      name: 'ユーザー管理',
      path: '/admin/users',
      icon: <FaUsers className="mr-3" />,
    },
    {
      name: 'お問い合わせ',
      path: '/admin/messages',
      icon: <FaEnvelope className="mr-3" />,
    },
    {
      name: '設定',
      path: '/admin/settings',
      icon: <FaCog className="mr-3" />,
    },
  ];

  return (
    <>
      {/* オーバーレイ（モバイル表示時のみ） */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={close}
          aria-hidden="true"
        />
      )}

      {/* サイドバー */}
      <aside
        className={`
          fixed md:static inset-y-0 left-0 z-50
          transform ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          transition-transform duration-300 ease-in-out
          w-64 bg-white shadow-sm min-h-screen
        `}
        // サイドバーの状態に応じたクラスを確認
        data-is-open={isOpen}
      >
        <div className="flex justify-end p-2 md:hidden">
          <button
            onClick={close}
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100"
            aria-label="サイドバーを閉じる"
          >
            <FaChevronLeft />
          </button>
        </div>

        <nav className="mt-2 px-2">
          <div className="space-y-1">
            {menuItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${isActive(
                  item.path
                )}`}
              >
                {item.icon}
                {item.name}
              </Link>
            ))}
          </div>
        </nav>

        {/* 折りたたみボタン（デスクトップ表示時のみ） */}
        <div className="hidden md:flex justify-end p-2 absolute bottom-4 right-2">
          <button
            onClick={toggle}
            className="p-2 rounded-full bg-primary-100 text-primary-800 hover:bg-primary-200"
            aria-label={isOpen ? 'サイドバーを折りたたむ' : 'サイドバーを展開する'}
          >
            {isOpen ? <FaChevronLeft /> : <FaChevronRight />}
          </button>
        </div>
      </aside>
    </>
  );
}
