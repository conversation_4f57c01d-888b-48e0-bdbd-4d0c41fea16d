'use client';

import { useState, useRef } from 'react';
import {
  FaBold,
  FaItalic,
  FaUnderline,
  FaListUl,
  FaListOl,
  FaQuoteRight,
  FaLink,
  FaImage,
  FaHeading,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight
} from 'react-icons/fa';
import ImageUploader from './ImageUploader';

interface RichTextEditorProps {
  initialValue?: string;
  onChange: (content: string) => void;
}

export default function RichTextEditor({ initialValue = '', onChange }: RichTextEditorProps) {
  // Use a ref to track if this is the first render
  const isFirstRender = useRef(true);
  const [content, setContent] = useState(initialValue);
  const [showImageUploader, setShowImageUploader] = useState(false);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setContent(newValue);

    // Only notify parent if this is not the first render
    if (isFirstRender.current) {
      isFirstRender.current = false;
    } else {
      onChange(newValue);
    }
  };

  // Simple toolbar button click handler
  const handleToolbarClick = (command: string, value?: string) => {
    // For now, we'll just insert markdown syntax at the cursor position
    // In a real implementation, you might want to use a proper rich text editor library
    const textarea = document.getElementById('editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    let newText = '';

    switch (command) {
      case 'bold':
        newText = `**${selectedText}**`;
        break;
      case 'italic':
        newText = `*${selectedText}*`;
        break;
      case 'underline':
        newText = `<u>${selectedText}</u>`;
        break;
      case 'heading':
        newText = `## ${selectedText}`;
        break;
      case 'ul':
        newText = `- ${selectedText}`;
        break;
      case 'ol':
        newText = `1. ${selectedText}`;
        break;
      case 'quote':
        newText = `> ${selectedText}`;
        break;
      case 'link':
        const url = prompt('リンク先URLを入力してください:', 'https://');
        if (url) {
          newText = `[${selectedText || 'リンクテキスト'}](${url})`;
        } else {
          return;
        }
        break;
      case 'image':
        setShowImageUploader(true);
        return;
      case 'align':
        if (value === 'left') {
          newText = `<div style="text-align: left">${selectedText}</div>`;
        } else if (value === 'center') {
          newText = `<div style="text-align: center">${selectedText}</div>`;
        } else if (value === 'right') {
          newText = `<div style="text-align: right">${selectedText}</div>`;
        }
        break;
      default:
        return;
    }

    const newContent = content.substring(0, start) + newText + content.substring(end);
    setContent(newContent);

    // Notify parent of the change
    onChange(newContent);

    // Set focus back to textarea and update cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + newText.length, start + newText.length);
    }, 0);
  };

  const handleImageUpload = (imageData: string | null) => {
    if (!imageData) {
      setShowImageUploader(false);
      return;
    }

    const textarea = document.getElementById('editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    // Insert image as HTML instead of markdown
    const imageHtml = `<img src="${imageData}" alt="画像" style="max-width: 100%; height: auto;" />`;
    const newContent = content.substring(0, start) + imageHtml + content.substring(end);
    setContent(newContent);

    // Notify parent of the change
    onChange(newContent);

    // Close image uploader
    setShowImageUploader(false);

    // Set focus back to textarea
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + imageHtml.length, start + imageHtml.length);
    }, 0);
  };

  return (
    <div className="border border-gray-300 rounded-md overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap gap-1">
        <button
          type="button"
          onClick={() => handleToolbarClick('bold')}
          className="p-2 rounded hover:bg-gray-200"
          title="太字"
        >
          <FaBold />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('italic')}
          className="p-2 rounded hover:bg-gray-200"
          title="斜体"
        >
          <FaItalic />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('underline')}
          className="p-2 rounded hover:bg-gray-200"
          title="下線"
        >
          <FaUnderline />
        </button>
        <div className="border-r border-gray-300 mx-1 h-6 my-auto"></div>
        <button
          type="button"
          onClick={() => handleToolbarClick('heading')}
          className="p-2 rounded hover:bg-gray-200"
          title="見出し"
        >
          <FaHeading />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('ul')}
          className="p-2 rounded hover:bg-gray-200"
          title="箇条書き"
        >
          <FaListUl />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('ol')}
          className="p-2 rounded hover:bg-gray-200"
          title="番号付きリスト"
        >
          <FaListOl />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('quote')}
          className="p-2 rounded hover:bg-gray-200"
          title="引用"
        >
          <FaQuoteRight />
        </button>
        <div className="border-r border-gray-300 mx-1 h-6 my-auto"></div>
        <button
          type="button"
          onClick={() => handleToolbarClick('align', 'left')}
          className="p-2 rounded hover:bg-gray-200"
          title="左揃え"
        >
          <FaAlignLeft />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('align', 'center')}
          className="p-2 rounded hover:bg-gray-200"
          title="中央揃え"
        >
          <FaAlignCenter />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('align', 'right')}
          className="p-2 rounded hover:bg-gray-200"
          title="右揃え"
        >
          <FaAlignRight />
        </button>
        <div className="border-r border-gray-300 mx-1 h-6 my-auto"></div>
        <button
          type="button"
          onClick={() => handleToolbarClick('link')}
          className="p-2 rounded hover:bg-gray-200"
          title="リンク"
        >
          <FaLink />
        </button>
        <button
          type="button"
          onClick={() => handleToolbarClick('image')}
          className="p-2 rounded hover:bg-gray-200"
          title="画像"
        >
          <FaImage />
        </button>
      </div>

      {/* Editor */}
      <textarea
        id="editor"
        value={content}
        onChange={handleContentChange}
        className="w-full p-3 min-h-[300px] focus:outline-none focus:ring-2 focus:ring-primary-500"
        placeholder="ここに記事の本文を入力してください..."
      />

      {/* Image uploader modal */}
      {showImageUploader && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">画像をアップロード</h3>
            <ImageUploader
              onImageChange={handleImageUpload}
              label="画像を選択"
            />
            <div className="mt-4 flex justify-end">
              <button
                type="button"
                onClick={() => setShowImageUploader(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                キャンセル
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
