'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import ImageUploader from '@/components/admin/ImageUploader';
import RichTextEditor from '@/components/admin/RichTextEditor';

// Validation schema
const postSchema = z.object({
  title: z.string().min(1, 'タイトルは必須です').max(200),
  slug: z.string().min(1, 'スラッグは必須です').max(200)
    .regex(/^[a-z0-9-]+$/, 'スラッグは小文字のアルファベット、数字、ハイフンのみ使用できます'),
  content: z.string().min(1, '本文は必須です'),
  excerpt: z.string().max(500).optional().nullable(),
  featuredImage: z.string().optional().nullable(),
  published: z.boolean().optional(),
  featured: z.boolean().optional(),
  publishedAt: z.string().optional().nullable(),
  categoryId: z.string().min(1, 'カテゴリーは必須です'),
  subCategoryId: z.string().optional().nullable(),
});

interface Category {
  id: string;
  name: string;
  slug: string;
  subCategories: {
    id: string;
    name: string;
    slug: string;
  }[];
}

interface PostFormProps {
  post?: {
    id: string;
    title: string;
    slug: string;
    content: string;
    excerpt?: string | null;
    featuredImage?: string | null;
    published: boolean;
    featured?: boolean;
    publishedAt?: string | null;
    categoryId: string;
    subCategoryId?: string | null;
    category?: {
      id: string;
      name: string;
      slug: string;
    };
    subCategory?: {
      id: string;
      name: string;
      slug: string;
    } | null;
  };
  isEditing?: boolean;
}

export default function PostForm({ post, isEditing = false }: PostFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: post?.title || '',
    slug: post?.slug || '',
    content: post?.content || '',
    excerpt: post?.excerpt || '',
    featuredImage: post?.featuredImage || null,
    published: post?.published || false,
    featured: post?.featured || false,
    publishedAt: post?.publishedAt ? new Date(post.publishedAt).toISOString().split('T')[0] : '',
    categoryId: post?.categoryId || '',
    subCategoryId: post?.subCategoryId || '',
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [subCategories, setSubCategories] = useState<{ id: string; name: string }[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  // 初期値として設定されたサブカテゴリーIDを保存する状態変数
  const [initialSubCategoryId, setInitialSubCategoryId] = useState<string>(post?.subCategoryId || '');

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setSubmitError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/admin/categories', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            setSubmitError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('カテゴリーの取得に失敗しました');
        }

        const data = await response.json();
        console.log('Fetched categories:', data);
        setCategories(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching categories:', error);
        setSubmitError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Update subcategories when category changes
  useEffect(() => {
    if (formData.categoryId) {
      const selectedCategory = categories.find(cat => cat.id === formData.categoryId);
      console.log('Selected category:', selectedCategory);

      if (selectedCategory && selectedCategory.subCategories) {
        console.log('Subcategories:', selectedCategory.subCategories);
        setSubCategories(selectedCategory.subCategories || []);

        // 初期値として設定されたサブカテゴリーIDがリストに存在するか確認
        if (initialSubCategoryId) {
          console.log('Checking initial subCategoryId:', initialSubCategoryId);
          const subCategoryExists = selectedCategory.subCategories.some(
            sub => sub.id === initialSubCategoryId
          );

          if (subCategoryExists) {
            console.log('Initial subCategory exists, restoring selection');
            // 存在する場合は選択状態を復元
            setFormData(prev => ({ ...prev, subCategoryId: initialSubCategoryId }));
            // 初期値をクリア（一度だけ復元するため）
            setInitialSubCategoryId('');
          } else {
            console.log('Initial subCategory does not exist in this category');
          }
        }

        // 現在選択されているサブカテゴリーが選択されたカテゴリーに属していない場合はクリア
        if (formData.subCategoryId && !initialSubCategoryId) {
          const subCategoryExists = selectedCategory.subCategories.some(
            sub => sub.id === formData.subCategoryId
          );

          if (!subCategoryExists) {
            console.log('Current subCategory does not exist in this category, clearing selection');
            setFormData(prev => ({ ...prev, subCategoryId: '' }));
          }
        }
      } else {
        console.log('No subcategories found for category:', formData.categoryId);
        setSubCategories([]);
      }
    } else {
      setSubCategories([]);
    }
  }, [formData.categoryId, categories, initialSubCategoryId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle rich text editor content change
  const handleContentChange = (content: string) => {
    setFormData(prev => ({ ...prev, content }));

    // Clear error when content is edited
    if (errors.content) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.content;
        return newErrors;
      });
    }
  };

  // Handle image upload
  const handleImageChange = (imageData: string | null) => {
    setFormData(prev => ({
      ...prev,
      featuredImage: imageData
    }));
  };

  // Generate slug based on title, category, and subcategory
  const generateSlug = (title: string, categoryId: string, subCategoryId: string) => {
    if (!title) return '';

    // Convert title to slug format
    let slug = title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    // Add category and subcategory prefixes if available
    if (categoryId) {
      const selectedCategory = categories.find(cat => cat.id === categoryId);
      console.log('Generating slug with category:', selectedCategory);

      if (selectedCategory && selectedCategory.slug) {
        // If subcategory is selected, use it in the slug
        if (subCategoryId && selectedCategory.subCategories) {
          const selectedSubCategory = selectedCategory.subCategories.find(sub => sub.id === subCategoryId);
          console.log('Selected subcategory for slug:', selectedSubCategory);

          if (selectedSubCategory && selectedSubCategory.slug) {
            return `${selectedCategory.slug}-${selectedSubCategory.slug}-${slug}`;
          }
        }
        // Otherwise just use the category
        return `${selectedCategory.slug}-${slug}`;
      }
    }

    return slug;
  };

  // Auto-generate slug from title
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    // Only auto-generate slug if it's empty or matches the previous auto-generated slug
    const shouldUpdateSlug = formData.slug === '' ||
      formData.slug === generateSlug(formData.title, formData.categoryId, formData.subCategoryId);

    setFormData(prev => ({
      ...prev,
      title: value,
      slug: shouldUpdateSlug ? generateSlug(value, prev.categoryId, prev.subCategoryId) : prev.slug
    }));

    // Clear error when field is edited
    if (errors.title) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.title;
        return newErrors;
      });
    }
  };

  // Update slug when category or subcategory changes
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.target;

    // Only auto-generate slug if it's empty or matches the previous auto-generated slug
    const shouldUpdateSlug = formData.slug === '' ||
      formData.slug === generateSlug(formData.title, formData.categoryId, formData.subCategoryId);

    setFormData(prev => ({
      ...prev,
      categoryId: value,
      subCategoryId: '', // Reset subcategory when category changes
      slug: shouldUpdateSlug ? generateSlug(prev.title, value, '') : prev.slug
    }));

    // Clear error when field is edited
    if (errors.categoryId) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.categoryId;
        return newErrors;
      });
    }
  };

  // Update slug when subcategory changes
  const handleSubCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.target;

    // Only auto-generate slug if it's empty or matches the previous auto-generated slug
    const shouldUpdateSlug = formData.slug === '' ||
      formData.slug === generateSlug(formData.title, formData.categoryId, formData.subCategoryId);

    setFormData(prev => ({
      ...prev,
      subCategoryId: value,
      slug: shouldUpdateSlug ? generateSlug(prev.title, prev.categoryId, value) : prev.slug
    }));

    // Clear error when field is edited
    if (errors.subCategoryId) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.subCategoryId;
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    try {
      postSchema.parse(formData);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);
    setSubmitSuccess(null);

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const url = isEditing
        ? `/api/admin/posts/${post?.id}`
        : '/api/admin/posts';

      const method = isEditing ? 'PUT' : 'POST';

      // Prepare data for submission
      const submitData = {
        ...formData,
        // Set publishedAt to current date if published is true and publishedAt is empty
        publishedAt: formData.published && !formData.publishedAt
          ? new Date().toISOString()
          : formData.publishedAt || null,
        // Convert empty string to null for optional fields
        excerpt: formData.excerpt || null,
        subCategoryId: formData.subCategoryId || null,
      };

      // 送信前に画像サイズをチェック
      if (submitData.featuredImage) {
        const base64Data = submitData.featuredImage.replace(/^data:image\/\w+;base64,/, '');
        const sizeInBytes = (base64Data.length * 3) / 4;
        const sizeInKB = Math.round(sizeInBytes / 1024);

        // サイズ制限を緩和（2MB）
        const MAX_ALLOWED_SIZE_KB = 2048; // 2MB

        if (sizeInKB > MAX_ALLOWED_SIZE_KB) {
          setSubmitError(`画像サイズが大きすぎます（${sizeInKB}KB）。${MAX_ALLOWED_SIZE_KB}KB以下の画像を選択してください。画像は自動的に圧縮されますが、この画像は大きすぎます。`);
          setIsSubmitting(false);
          return;
        }

        console.log(`送信前の画像サイズ: ${sizeInKB}KB`);
      }

      // タイムアウト対策として fetch のタイムアウトを設定（60秒に延長）
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒のタイムアウト

      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          throw new Error('認証情報が見つかりません。再ログインしてください。');
        }

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(submitData),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // レスポンスがJSONかどうかをチェック（緩和版）
        const contentType = response.headers.get('content-type');
        if (contentType && !contentType.includes('application/json')) {
          console.warn('Content-Typeが不正です:', contentType);
          // エラーをスローせず、続行を試みる
        }

        // レスポンスが空かどうかをチェック
        const responseText = await response.text();
        if (!responseText || responseText.trim() === '') {
          throw new Error('サーバーからの応答が空です。アイキャッチ画像のサイズを小さくするか、削除して再試行してください。');
        }

        // JSONとして解析を試みる
        let data;
        try {
          data = JSON.parse(responseText);

          // レスポンスが成功でない場合はエラーをスロー
          if (!response.ok) {
            throw new Error(data.error || data.message || 'エラーが発生しました');
          }
        } catch (jsonError) {
          console.error('JSONの解析に失敗しました:', jsonError, 'レスポンステキスト:', responseText);
          throw new Error('サーバーからの応答が不正です。アイキャッチ画像のサイズを小さくするか、削除して再試行してください。');
        }

        setSubmitSuccess(isEditing ? '記事が正常に更新されました。数秒後に記事一覧ページにリダイレクトします。' : '記事が正常に投稿されました。数秒後に記事一覧ページにリダイレクトします。');
        setTimeout(() => {
          router.push('/admin/posts');
          router.refresh();
        }, 3000);
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);

        if (fetchError instanceof DOMException && fetchError.name === 'AbortError') {
          throw new Error('リクエストがタイムアウトしました（60秒）。サーバーの負荷が高い可能性があります。しばらく待ってから再試行するか、アイキャッチ画像を削除して再試行してください。');
        }

        throw fetchError;
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div className="text-center py-10">読み込み中...</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-full">
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700">
            タイトル
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleTitleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            required
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.title}</p>
          )}
        </div>

        <div>
          <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
            スラッグ
          </label>
          <input
            type="text"
            id="slug"
            name="slug"
            value={formData.slug}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            URLに使用される識別子です。小文字のアルファベット、数字、ハイフンのみ使用できます。
          </p>
          {errors.slug && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.slug}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div>
            <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
              カテゴリー
            </label>
            <select
              id="categoryId"
              name="categoryId"
              value={formData.categoryId}
              onChange={handleCategoryChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              required
            >
              <option value="">カテゴリーを選択</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.categoryId && (
              <p className="mt-1 text-sm text-red-600 break-words">{errors.categoryId}</p>
            )}
          </div>

          <div>
            <label htmlFor="subCategoryId" className="block text-sm font-medium text-gray-700">
              サブカテゴリー
            </label>
            <select
              id="subCategoryId"
              name="subCategoryId"
              value={formData.subCategoryId}
              onChange={handleSubCategoryChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              disabled={!formData.categoryId || subCategories.length === 0}
            >
              <option value="">サブカテゴリーを選択 (任意)</option>
              {subCategories.map(subCategory => (
                <option key={subCategory.id} value={subCategory.id}>
                  {subCategory.name}
                </option>
              ))}
            </select>
            {errors.subCategoryId && (
              <p className="mt-1 text-sm text-red-600 break-words">{errors.subCategoryId}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700">
            抜粋
          </label>
          <textarea
            id="excerpt"
            name="excerpt"
            rows={3}
            value={formData.excerpt || ''}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            placeholder="記事の簡単な説明（任意）"
          />
          <p className="mt-1 text-xs text-gray-500">
            記事の簡単な説明を入力してください。一覧ページなどで表示されます。
          </p>
          {errors.excerpt && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.excerpt}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            アイキャッチ画像
          </label>
          <ImageUploader
            initialImage={formData.featuredImage || undefined}
            onImageChange={handleImageChange}
            label="アイキャッチ画像をアップロード（2MB以下）"
          />
          <p className="mt-1 text-xs text-gray-500">
            画像サイズは2MB以下である必要があります。大きい画像は自動的に圧縮されますので、スマホで撮影した写真なども利用できます。
          </p>
        </div>

        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
            本文
          </label>
          <RichTextEditor
            initialValue={formData.content}
            onChange={handleContentChange}
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.content}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="published"
                  name="published"
                  checked={formData.published}
                  onChange={e => setFormData(prev => ({ ...prev, published: e.target.checked }))}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="published" className="ml-2 block text-sm text-gray-700">
                  公開する
                </label>
              </div>
              <p className="text-xs text-gray-500">
                チェックを入れると記事が公開されます。
              </p>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={formData.featured}
                  onChange={e => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                  注目記事に設定する
                </label>
              </div>
              <p className="text-xs text-gray-500">
                チェックを入れるとホーム画面の「注目の記事」セクションに表示されます。
              </p>
            </div>
          </div>

          <div>
            <label htmlFor="publishedAt" className="block text-sm font-medium text-gray-700">
              公開日
            </label>
            <input
              type="date"
              id="publishedAt"
              name="publishedAt"
              value={formData.publishedAt || ''}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              空欄の場合、公開時に現在の日時が設定されます。
            </p>
          </div>
        </div>
      </div>

      {submitError && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">エラー</h3>
              <div className="mt-2 text-sm text-red-700">
                <p className="break-words">{submitError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {submitSuccess && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">成功</h3>
              <div className="mt-2 text-sm text-green-700">
                <p className="break-words">{submitSuccess}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row sm:justify-end sm:space-x-3 space-y-2 sm:space-y-0">
        <button
          type="button"
          onClick={() => router.back()}
          disabled={isSubmitting || !!submitSuccess}
          className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          キャンセル
        </button>
        <button
          type="submit"
          disabled={isSubmitting || !!submitSuccess}
          className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {isSubmitting ? '送信中...' : !!submitSuccess ? 'リダイレクト中...' : isEditing ? '更新' : '作成'}
        </button>
      </div>
    </form>
  );
}
