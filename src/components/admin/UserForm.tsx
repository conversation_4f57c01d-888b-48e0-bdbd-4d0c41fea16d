'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';

// Validation schema
const userSchema = z.object({
  username: z.string().min(3, 'ユーザー名は3文字以上で入力してください').max(50),
  email: z.string().email('有効なメールアドレスを入力してください'),
  password: z.string().min(8, 'パスワードは8文字以上で入力してください').optional(),
  bio: z.string().max(500, '紹介文は500文字以内で入力してください').optional(),
  profileImage: z.string().optional(),
  detailedProfile: z.string().max(2000, '詳細プロフィールは2000文字以内で入力してください').optional(),
  isAdmin: z.boolean().optional(),
});

interface UserFormProps {
  user?: {
    id: string;
    username: string;
    email: string;
    bio?: string;
    profileImage?: string;
    detailedProfile?: string;
    isAdmin?: boolean;
  };
  isEditing?: boolean;
}

export default function UserForm({ user, isEditing = false }: UserFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    password: '',
    bio: user?.bio || '',
    profileImage: user?.profileImage || '',
    detailedProfile: user?.detailedProfile || '',
    isAdmin: user?.isAdmin || false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle checkbox inputs separately
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    try {
      // For editing, make password optional or empty
      const schema = isEditing
        ? userSchema.extend({
            password: z.string().min(8, 'パスワードは8文字以上で入力してください').optional().or(z.string().max(0)),
          })
        : userSchema.extend({
            password: z.string().min(8, 'パスワードは8文字以上で入力してください'),
          });

      schema.parse(formData);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const url = isEditing
        ? `/api/admin/users/${user?.id}`
        : '/api/admin/users';

      const method = isEditing ? 'PUT' : 'POST';

      // Only include password if it's provided or it's a new user
      let body;
      if (isEditing && !formData.password) {
        // Exclude password field when editing and no new password provided
        const { password, ...rest } = formData;
        body = rest;
      } else {
        body = formData;
      }

      // ローカルストレージからトークンを取得
      const token = localStorage.getItem('adminToken');
      if (!token) {
        throw new Error('認証情報が見つかりません。再ログインしてください。');
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'エラーが発生しました');
      }

      router.push('/admin/users');
      router.refresh();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow">
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700">
          ユーザー名
        </label>
        <input
          type="text"
          id="username"
          name="username"
          value={formData.username}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          required
        />
        {errors.username && (
          <p className="mt-1 text-sm text-red-600">{errors.username}</p>
        )}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          メールアドレス
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          required
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email}</p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
          パスワード {isEditing && <span className="text-gray-500 text-xs">(変更する場合のみ入力)</span>}
        </label>
        <input
          type="password"
          id="password"
          name="password"
          value={formData.password}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          required={!isEditing}
        />
        {errors.password && (
          <p className="mt-1 text-sm text-red-600">{errors.password}</p>
        )}
      </div>

      <div>
        <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
          紹介文 <span className="text-gray-500 text-xs">(記事詳細ページの「著者について」に表示されます)</span>
        </label>
        <textarea
          id="bio"
          name="bio"
          value={formData.bio}
          onChange={handleChange}
          rows={4}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
        {errors.bio && (
          <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
        )}
      </div>

      <div>
        <label htmlFor="profileImage" className="block text-sm font-medium text-gray-700">
          プロフィール画像 <span className="text-gray-500 text-xs">(URL形式で入力してください)</span>
        </label>
        <input
          type="text"
          id="profileImage"
          name="profileImage"
          value={formData.profileImage}
          onChange={handleChange}
          placeholder="https://example.com/image.jpg"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
        {errors.profileImage && (
          <p className="mt-1 text-sm text-red-600">{errors.profileImage}</p>
        )}
      </div>

      <div>
        <label htmlFor="detailedProfile" className="block text-sm font-medium text-gray-700">
          詳細プロフィール <span className="text-gray-500 text-xs">(プロフィールページに表示される詳細な自己紹介)</span>
        </label>
        <textarea
          id="detailedProfile"
          name="detailedProfile"
          value={formData.detailedProfile}
          onChange={handleChange}
          rows={6}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
        {errors.detailedProfile && (
          <p className="mt-1 text-sm text-red-600">{errors.detailedProfile}</p>
        )}
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="isAdmin"
            name="isAdmin"
            type="checkbox"
            checked={formData.isAdmin}
            onChange={handleChange}
            className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
          />
        </div>
        <div className="ml-3 text-sm">
          <label htmlFor="isAdmin" className="font-medium text-gray-700">サイト管理者</label>
          <p className="text-gray-500">サイト管理者に設定すると、トップページのプロフィール欄に表示されます</p>
        </div>
      </div>

      {submitError && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">エラー</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{submitError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => router.back()}
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          キャンセル
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {isSubmitting ? '送信中...' : isEditing ? '更新' : '作成'}
        </button>
      </div>
    </form>
  );
}
