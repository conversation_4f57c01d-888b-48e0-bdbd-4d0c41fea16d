'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';

export default function SessionDebugger() {
  const { data: clientSession, status } = useSession();
  const [serverSession, setServerSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fixResult, setFixResult] = useState<any>(null);
  const [isFixing, setIsFixing] = useState(false);

  useEffect(() => {
    async function fetchServerSession() {
      try {
        const response = await fetch('/api/auth/session');
        if (!response.ok) {
          throw new Error('Failed to fetch session');
        }
        const data = await response.json();
        setServerSession(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    fetchServerSession();
  }, [fixResult]);

  const handleFixSession = async () => {
    try {
      setIsFixing(true);
      setError(null);
      setFixResult(null);

      const response = await fetch('/api/auth/fix-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      setFixResult(data);

      if (data.success) {
        // Session is valid, refresh the page
        window.location.reload();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred while fixing session');
    } finally {
      setIsFixing(false);
    }
  };

  const handleLogout = () => {
    signOut({ callbackUrl: '/admin/login' });
  };

  if (loading) {
    return <div className="p-4 bg-gray-100 rounded-lg">Loading session information...</div>;
  }

  if (error) {
    return <div className="p-4 bg-red-100 text-red-800 rounded-lg">Error: {error}</div>;
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg space-y-4">
      <h3 className="text-lg font-medium">Session Debugger</h3>

      <div>
        <h4 className="font-medium">Client Session (useSession):</h4>
        <pre className="bg-white p-2 rounded overflow-auto text-xs">
          {JSON.stringify(clientSession, null, 2)}
        </pre>
        <p className="text-sm mt-1">Status: {status}</p>
      </div>

      <div>
        <h4 className="font-medium">Server Session (API):</h4>
        <pre className="bg-white p-2 rounded overflow-auto text-xs">
          {JSON.stringify(serverSession, null, 2)}
        </pre>
      </div>

      {serverSession && !serverSession.validSession && (
        <div className="bg-yellow-100 p-3 rounded-lg text-yellow-800">
          <p className="font-medium">Session Issue Detected</p>
          <p className="text-sm">
            Your session contains a user ID that doesn't exist in the database.
            This will cause errors when creating or updating posts.
          </p>
          <div className="mt-3 flex space-x-3">
            <button
              onClick={handleLogout}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
            >
              ログアウト
            </button>
            <button
              onClick={handleFixSession}
              disabled={isFixing}
              className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-sm disabled:opacity-50"
            >
              {isFixing ? '処理中...' : 'セッション修復を試みる'}
            </button>
          </div>
        </div>
      )}

      {fixResult && (
        <div className={`p-3 rounded-lg ${fixResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          <p className="font-medium">{fixResult.success ? '成功' : 'エラー'}</p>
          <p className="text-sm">{fixResult.message}</p>
          {fixResult.suggestedUser && (
            <div className="mt-2 text-sm">
              <p>推奨ユーザー:</p>
              <ul className="list-disc list-inside">
                <li>ユーザー名: {fixResult.suggestedUser.username}</li>
                <li>メール: {fixResult.suggestedUser.email}</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
