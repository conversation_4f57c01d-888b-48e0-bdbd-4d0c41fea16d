'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

export default function AdminProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading, checkAuth } = useAdminAuth();
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    const verifyAuth = async () => {
      console.log('AdminProtectedRoute: 認証状態を確認中...');

      try {
        // 認証状態を確認
        const isAuthed = await checkAuth();
        console.log('認証確認結果:', isAuthed);

        // 認証確認完了フラグを設定
        setAuthChecked(true);

        // 認証されていない場合はログインページにリダイレクト
        if (!isAuthed && !isLoading) {
          console.log('認証されていません。ログインページにリダイレクトします。');

          try {
            // Next.jsのルーターを使用
            router.push('/admin/login');

            // フォールバックとしてwindow.locationも使用
            setTimeout(() => {
              if (window.location.pathname.startsWith('/admin') &&
                  window.location.pathname !== '/admin/login') {
                console.log('タイムアウト後のリダイレクト実行');
                window.location.href = '/admin/login';
              }
            }, 500);
          } catch (redirectError) {
            console.error('リダイレクト中にエラーが発生:', redirectError);
            // 最終手段としてのリダイレクト
            window.location.href = '/admin/login';
          }
        }
      } catch (error) {
        console.error('認証確認中にエラーが発生:', error);
        setAuthChecked(true);
      }
    };

    if (!authChecked) {
      verifyAuth();
    }
  }, [checkAuth, isLoading, router, authChecked, isAuthenticated]);

  // ローディング中は読み込み表示
  if (isLoading || !authChecked) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // 認証されていない場合は何も表示しない（リダイレクト中）
  if (!isAuthenticated) {
    console.log('認証されていません。コンテンツを表示しません。');
    return (
      <div className="flex flex-col justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mb-4"></div>
        <p className="text-gray-600">認証情報を確認中...</p>
      </div>
    );
  }

  // 認証されている場合は子コンポーネントを表示
  console.log('認証されています。コンテンツを表示します。');
  return <>{children}</>;
}
