'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import RichTextEditor from '@/components/admin/RichTextEditor';

// About page sections
const ABOUT_SECTIONS = [
  {
    key: 'about_section_intro',
    title: 'はじめまして',
    description: 'About ページの「はじめまして」セクションの内容',
  },
  {
    key: 'about_section_bio',
    title: '経歴',
    description: 'About ページの「経歴」セクションの内容',
  },
  {
    key: 'about_section_blog',
    title: 'ブログについて',
    description: 'About ページの「ブログについて」セクションの内容',
  },
  {
    key: 'about_section_contact',
    title: '連絡先',
    description: 'About ページの「連絡先」セクションの内容',
  },
];

export default function AboutPageSettings() {
  const [settings, setSettings] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 設定を読み込む
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/admin/settings');
        if (!response.ok) {
          throw new Error('設定の取得に失敗しました');
        }

        const data = await response.json();
        
        // 設定をオブジェクトに変換
        const settingsObj: Record<string, string> = {};
        data.forEach((setting: any) => {
          settingsObj[setting.key] = setting.value;
        });

        // 設定が存在しない場合はデフォルト値を設定
        ABOUT_SECTIONS.forEach(section => {
          if (!settingsObj[section.key]) {
            settingsObj[section.key] = '';
          }
        });

        setSettings(settingsObj);
      } catch (error) {
        console.error('Error fetching settings:', error);
        toast.error('設定の取得に失敗しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // 設定を保存する
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // 保存用のデータを作成
      const settingsToSave = ABOUT_SECTIONS.map(section => ({
        key: section.key,
        value: settings[section.key] || '',
        description: section.description,
      }));

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsToSave),
      });

      if (!response.ok) {
        throw new Error('設定の保存に失敗しました');
      }

      toast.success('設定を保存しました');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('設定の保存に失敗しました');
    } finally {
      setIsSaving(false);
    }
  };

  // 設定値の変更
  const handleContentChange = (key: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  if (isLoading) {
    return <div className="text-center py-4">読み込み中...</div>;
  }

  return (
    <div className="space-y-6">
      {ABOUT_SECTIONS.map(section => (
        <div key={section.key} className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium mb-4">{section.title}</h3>
          <p className="text-sm text-gray-500 mb-2">{section.description}</p>
          <RichTextEditor
            initialValue={settings[section.key] || ''}
            onChange={(content) => handleContentChange(section.key, content)}
          />
        </div>
      ))}

      <div className="flex justify-end mt-6">
        <button
          type="button"
          onClick={handleSave}
          disabled={isSaving}
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50"
        >
          {isSaving ? '保存中...' : '保存'}
        </button>
      </div>
    </div>
  );
}
