'use client';

import { useState, useRef, ChangeEvent } from 'react';
import Image from 'next/image';
import { FaUpload, FaTrash } from 'react-icons/fa';
import { compressImage, compressImageWithCanvas, fileToBase64 } from '@/utils/imageCompression';

interface ImageUploaderProps {
  initialImage?: string;
  onImageChange: (imageData: string | null) => void;
  label?: string;
  maxSizeKB?: number;
}

// 最大サイズを2MBに緩和
const defaultMaxSize = 2048; // 2MB

export default function ImageUploader({
  initialImage,
  onImageChange,
  label = '画像をアップロード',
  maxSizeKB = defaultMaxSize,
}: ImageUploaderProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialImage || null);
  const [error, setError] = useState<string | null>(null);
  const [warning, setWarning] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 詳細なファイル情報をログに記録
    console.log('選択されたファイル情報:', {
      name: file.name,
      type: file.type,
      size: `${Math.round(file.size / 1024)}KB`,
      lastModified: new Date(file.lastModified).toISOString()
    });

    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('画像ファイルのみアップロードできます。');
      return;
    }

    // サポートされているファイル形式をチェック
    const supportedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      setError(`サポートされていないファイル形式です（${file.type}）。JPEG、PNG、GIF、WEBPのみサポートしています。`);
      return;
    }

    // ファイルサイズをチェック
    const fileSizeKB = Math.round(file.size / 1024);
    console.log(`元のファイルサイズ: ${fileSizeKB}KB`);

    // 極端に小さいファイルのチェック (異常な可能性)
    if (fileSizeKB < 1) {
      console.warn('警告: ファイルサイズが極端に小さい (1KB未満)');
    }

    // サイズ制限を適用
    if (fileSizeKB > maxSizeKB) {
      setError(`ファイルサイズが大きすぎます（${fileSizeKB}KB）。${maxSizeKB}KB以下の画像を選択してください。`);
      return;
    }

    setIsLoading(true);
    setError(null);
    setWarning(null);

    try {
      // プレビュー用にファイルを読み込む
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);

      // 画像を圧縮する
      setIsCompressing(true);
      let compressedFile: File;
      try {
        compressedFile = await compressImage(file,
          () => setIsCompressing(true),
          () => setIsCompressing(false)
        );
        console.log(`圧縮後のファイルサイズ: ${Math.round(compressedFile.size / 1024)}KB`);
      } catch (compressError) {
        console.error('画像圧縮エラー:', compressError);
        // 圧縮に失敗した場合は元のファイルを使用
        compressedFile = file;
      }

      // サーバーにアップロード
      setIsUploading(true);
      try {
        const formData = new FormData();
        formData.append('file', compressedFile);

        console.log('アップロード開始:', {
          fileName: compressedFile.name,
          fileType: compressedFile.type,
          fileSize: `${Math.round(compressedFile.size / 1024)}KB`,
        });

        const res = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        // レスポンスの詳細をログに出力
        console.log('サーバーレスポンス:', {
          status: res.status,
          statusText: res.statusText,
          headers: {
            'content-type': res.headers.get('content-type'),
          }
        });

        if (!res.ok) {
          // エラーレスポンスの内容を取得
          let errorText = '';
          try {
            errorText = await res.text();
          } catch (e) {
            errorText = 'レスポンステキストの取得に失敗';
          }
          console.error('サーバーエラーレスポンス:', errorText);
          throw new Error(`Failed to upload image: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        console.log('アップロード成功:', data);

        // プレビューと親コンポーネントに通知
        setPreviewUrl(data.imageData);
        onImageChange(data.imageData);
      } catch (uploadError) {
        console.error('アップロードエラー:', uploadError);

        // アップロードに失敗した場合はCanvas APIを使用してクライアント側で圧縮
        console.log('アップロードに失敗しました。Canvas APIを使用してクライアント側で圧縮します。');
        try {
          // Canvas APIを使用して圧縮
          const canvasBase64 = await compressImageWithCanvas(compressedFile);
          console.log('Canvas圧縮完了。データ長:', canvasBase64.length);

          setPreviewUrl(canvasBase64);
          onImageChange(canvasBase64);

          // 警告メッセージを表示（エラーではなく警告として）
          console.warn('サーバー側の処理に失敗しましたが、クライアント側で画像を圧縮して使用しています。');
          // エラーはクリアして警告スタイルで表示
          setError(null);
          setWarning('注意: サーバー側の処理に失敗しましたが、クライアント側で画像を圧縮して使用しています。');
        } catch (canvasError) {
          console.error('Canvas圧縮エラー:', canvasError);
          // Canvas圧縮にも失敗した場合は単純にBase64変換
          const base64 = await fileToBase64(compressedFile);
          setPreviewUrl(base64);
          onImageChange(base64);

          // エラーメッセージを設定
          setError(`画像の処理に失敗しました: ${canvasError instanceof Error ? canvasError.message : String(canvasError)}`);
        }
      } finally {
        setIsUploading(false);
      }
    } catch (err) {
      const error = err as Error;
      console.error('Image processing error:', error);
      // エラースタックも記録（開発時のみ）
      if (error.stack) {
        console.error('Error stack:', error.stack);
      }
      setError(`画像の処理中にエラーが発生しました: ${error.message || '不明なエラー'}。別の画像を試してください。`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    onImageChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">{label}</label>

      <div className="mt-1 flex flex-col items-center">
        {previewUrl ? (
          <div className="relative w-full h-48 mb-4">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              sizes="100%"
              className="object-contain"
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
              title="画像を削除"
            >
              <FaTrash size={14} />
            </button>
          </div>
        ) : (
          <div
            className="w-full h-48 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center p-4 cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <FaUpload className="text-gray-400 text-3xl mb-2" />
            <p className="text-sm text-gray-500 mb-2">
              {isLoading ? '処理中...' : 'クリックして画像をアップロード'}
            </p>
            <p className="text-xs text-gray-400 mb-4">
              JPG, PNG, GIF, WEBP（最大 {maxSizeKB/1024}MB、大きな画像は自動圧縮されます）
            </p>

            {/* 非表示のファイル入力 */}
            <input
              id="file-upload"
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/jpeg,image/png,image/gif,image/webp"
              className="hidden" // 非表示にする
              disabled={isLoading}
            />

            {/* 表示用のボタン */}
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation(); // イベントの伝播を停止
                fileInputRef.current?.click();
              }}
              className="py-2 px-4 bg-primary-50 text-primary-700 rounded-md text-sm font-semibold hover:bg-primary-100"
              disabled={isLoading}
            >
              ファイルを選択
            </button>

            {isCompressing && (
              <p className="text-xs text-amber-600 mt-2">
                画像を圧縮中...
              </p>
            )}
            {isUploading && (
              <p className="text-xs text-blue-600 mt-2">
                サーバーにアップロード中...
              </p>
            )}
          </div>
        )}
      </div>

      {error && (
        <div className="mt-1 w-full">
          <p className="text-red-500 text-sm break-words">{error}</p>
          <button
            type="button"
            onClick={handleRemoveImage}
            className="text-blue-500 text-xs mt-1 hover:underline"
          >
            画像なしで続ける
          </button>
        </div>
      )}

      {warning && (
        <div className="mt-1 w-full">
          <p className="text-amber-500 text-sm break-words">{warning}</p>
        </div>
      )}

      {isCompressing && (
        <p className="text-amber-500 text-xs mt-1 break-words">画像を圧縮中です。しばらくお待ちください...</p>
      )}

      {isUploading && (
        <p className="text-blue-500 text-xs mt-1 break-words">サーバーにアップロード中です。しばらくお待ちください...</p>
      )}
    </div>
  );
}
