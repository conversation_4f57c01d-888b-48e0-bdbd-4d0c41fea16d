"use client";
import React, { useState, useEffect, useRef } from 'react';

const HOLE_COUNT = 6;
const MOLE_SHOW_TIME = 900; // ms
const MOLE_HIDE_TIME = 600; // ms
const MAX_MISS = 3;

const moleEmoji = '🐹';

export default function WhackAMoleGame() {
  const [holes, setHoles] = useState<number[]>(Array(HOLE_COUNT).fill(0));
  const [moleIndex, setMoleIndex] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [miss, setMiss] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [running, setRunning] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // もぐら出現ロジック
  useEffect(() => {
    if (!running || gameOver) return;
    let hideTimeout: NodeJS.Timeout;
    const showMole = () => {
      const idx = Math.floor(Math.random() * HOLE_COUNT);
      setMoleIndex(idx);
      hideTimeout = setTimeout(() => {
        setMoleIndex(null);
        setMiss((m) => {
          if (m + 1 >= MAX_MISS) {
            setGameOver(true);
            setRunning(false);
            return m + 1;
          }
          return m + 1;
        });
        timerRef.current = setTimeout(showMole, MOLE_HIDE_TIME);
      }, MOLE_SHOW_TIME);
    };
    showMole();
    return () => {
      clearTimeout(hideTimeout);
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [running, gameOver]);

  // 叩く処理
  const handleWhack = (idx: number) => {
    if (gameOver || !running) return;
    if (moleIndex === idx) {
      setScore((s) => s + 1);
      setMoleIndex(null);
      if (timerRef.current) clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        if (!gameOver) setMoleIndex(Math.floor(Math.random() * HOLE_COUNT));
      }, MOLE_HIDE_TIME);
    }
  };

  const startGame = () => {
    setScore(0);
    setMiss(0);
    setGameOver(false);
    setRunning(true);
    setMoleIndex(null);
  };

  return (
    <div className="max-w-xs mx-auto select-none">
      <div className="flex justify-between mb-2 text-sm">
        <div>スコア: {score}</div>
        <div>ミス: {miss} / {MAX_MISS}</div>
        <button
          onClick={startGame}
          className="px-2 py-1 bg-blue-500 text-white rounded"
        >
          {gameOver ? 'リスタート' : 'スタート'}
        </button>
      </div>
      <div className="grid grid-cols-3 gap-4 p-4 bg-amber-50 rounded-lg">
        {holes.map((_, idx) => (
          <button
            key={idx}
            className={`aspect-square w-20 sm:w-24 md:w-24 rounded-full border-4 border-amber-300 flex items-end justify-center text-4xl sm:text-5xl md:text-6xl bg-amber-100 shadow-inner transition-all duration-100 ${moleIndex === idx ? 'ring-4 ring-pink-400' : ''}`}
            style={{ touchAction: 'manipulation' }}
            onClick={() => handleWhack(idx)}
            disabled={gameOver || !running}
          >
            {moleIndex === idx ? moleEmoji : ''}
          </button>
        ))}
      </div>
      {gameOver && (
        <div className="mt-4 text-center font-bold text-red-500 text-lg">💥 ゲームオーバー</div>
      )}
      <div className="mt-2 text-center text-xs text-slate-500">Powered by GPT-4.1</div>
    </div>
  );
} 