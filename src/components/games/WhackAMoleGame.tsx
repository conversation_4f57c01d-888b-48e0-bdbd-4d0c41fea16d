"use client";
import React, { useState, useEffect, useRef } from 'react';

const HOLE_COUNT = 6;
const INITIAL_MOLE_SHOW_TIME = 2000; // ms - 初期は2秒表示
const INITIAL_MOLE_HIDE_TIME = 1000; // ms - 初期は1秒非表示
const MIN_MOLE_SHOW_TIME = 500; // ms - 最短表示時間
const MIN_MOLE_HIDE_TIME = 300; // ms - 最短非表示時間
const MAX_MISS = 3;
const LEVEL_UP_SCORE = 5; // 5点ごとにレベルアップ
const SPEED_INCREASE_RATE = 0.9; // 90%に短縮（10%速くなる）

const moleEmoji = '🐹';

export default function WhackAMoleGame() {
  const [holes, setHoles] = useState<number[]>(Array(HOLE_COUNT).fill(0));
  const [moleIndex, setMoleIndex] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [miss, setMiss] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [running, setRunning] = useState(false);
  const [level, setLevel] = useState(1);
  const [combo, setCombo] = useState(0);
  const [maxCombo, setMaxCombo] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // レベルアップチェック
  useEffect(() => {
    const newLevel = Math.floor(score / LEVEL_UP_SCORE) + 1;
    if (newLevel !== level) {
      setLevel(newLevel);
    }
  }, [score, level]);

  // もぐら出現ロジック
  useEffect(() => {
    if (!running || gameOver) return;
    let hideTimeout: NodeJS.Timeout;

    // 現在のレベルに基づく速度計算（useEffect内で実行）
    const showTime = Math.max(
      MIN_MOLE_SHOW_TIME,
      INITIAL_MOLE_SHOW_TIME * Math.pow(SPEED_INCREASE_RATE, level - 1)
    );
    const hideTime = Math.max(
      MIN_MOLE_HIDE_TIME,
      INITIAL_MOLE_HIDE_TIME * Math.pow(SPEED_INCREASE_RATE, level - 1)
    );

    const showMole = () => {
      const idx = Math.floor(Math.random() * HOLE_COUNT);
      setMoleIndex(idx);
      hideTimeout = setTimeout(() => {
        setMoleIndex(null);
        setCombo(0); // もぐらを逃すとコンボリセット
        setMiss((m) => {
          if (m + 1 >= MAX_MISS) {
            setGameOver(true);
            setRunning(false);
            return m + 1;
          }
          return m + 1;
        });
        timerRef.current = setTimeout(showMole, hideTime);
      }, showTime);
    };
    showMole();
    return () => {
      clearTimeout(hideTimeout);
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [running, gameOver, level]);

  // 叩く処理
  const handleWhack = (idx: number) => {
    if (gameOver || !running) return;
    if (moleIndex === idx) {
      const newCombo = combo + 1;
      setCombo(newCombo);
      setMaxCombo(Math.max(maxCombo, newCombo));

      // コンボボーナス（3連続以上で追加ポイント）
      const bonusPoints = newCombo >= 3 ? Math.floor(newCombo / 3) : 0;
      setScore((s) => s + 1 + bonusPoints);

      setMoleIndex(null);
      if (timerRef.current) clearTimeout(timerRef.current);

      // 現在のレベルに基づく速度計算
      const hideTime = Math.max(
        MIN_MOLE_HIDE_TIME,
        INITIAL_MOLE_HIDE_TIME * Math.pow(SPEED_INCREASE_RATE, level - 1)
      );

      timerRef.current = setTimeout(() => {
        if (!gameOver) {
          const newIdx = Math.floor(Math.random() * HOLE_COUNT);
          setMoleIndex(newIdx);
        }
      }, hideTime);
    }
  };

  const startGame = () => {
    setScore(0);
    setMiss(0);
    setGameOver(false);
    setRunning(true);
    setMoleIndex(null);
    setLevel(1);
    setCombo(0);
    setMaxCombo(0);
  };

  return (
    <div className="max-w-xs mx-auto select-none">
      <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
        <div className="bg-blue-50 p-2 rounded">
          <div className="font-semibold text-blue-800">スコア</div>
          <div className="text-lg font-bold text-blue-600">{score}</div>
        </div>
        <div className="bg-red-50 p-2 rounded">
          <div className="font-semibold text-red-800">ミス</div>
          <div className="text-lg font-bold text-red-600">{miss} / {MAX_MISS}</div>
        </div>
        <div className="bg-green-50 p-2 rounded">
          <div className="font-semibold text-green-800">レベル</div>
          <div className="text-lg font-bold text-green-600">{level}</div>
        </div>
        <div className="bg-purple-50 p-2 rounded">
          <div className="font-semibold text-purple-800">コンボ</div>
          <div className="text-lg font-bold text-purple-600">{combo} (最高: {maxCombo})</div>
        </div>
      </div>
      <div className="flex justify-center mb-3">
        <button
          onClick={startGame}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-colors"
        >
          {gameOver ? 'リスタート' : 'スタート'}
        </button>
      </div>
      <div className="grid grid-cols-3 gap-4 p-4 bg-amber-50 rounded-lg">
        {holes.map((_, idx) => (
          <button
            key={idx}
            className={`aspect-square w-20 sm:w-24 md:w-24 rounded-full border-4 border-amber-300 flex items-end justify-center text-4xl sm:text-5xl md:text-6xl bg-amber-100 shadow-inner transition-all duration-100 ${
              moleIndex === idx
                ? 'ring-4 ring-pink-400 bg-amber-200 scale-105'
                : 'hover:bg-amber-50'
            }`}
            style={{ touchAction: 'manipulation' }}
            onClick={() => handleWhack(idx)}
            disabled={gameOver || !running}
          >
            {moleIndex === idx ? moleEmoji : ''}
          </button>
        ))}
      </div>

      {gameOver && (
        <div className="mt-4 p-4 bg-red-50 rounded-lg text-center">
          <div className="font-bold text-red-500 text-lg mb-2">💥 ゲームオーバー</div>
          <div className="text-sm text-gray-600">
            最終スコア: {score} | 最高レベル: {level} | 最高コンボ: {maxCombo}
          </div>
        </div>
      )}

      {running && !gameOver && (
        <div className="mt-3 text-center text-sm text-gray-500">
          レベル {level}: {combo >= 3 ? `🔥 ${combo}連続ヒット！` : 'もぐらを叩こう！'}
        </div>
      )}

      <div className="mt-2 text-center text-xs text-slate-500">
        段階的難易度システム搭載 | Powered by Claude
      </div>
    </div>
  );
}