"use client";
import React, { useRef, useEffect, useState } from 'react';

type Brick = { x: number; y: number; status: number };

const CONFIG = {
  canvasWidth: 300,
  canvasHeight: 400,
  paddleWidth: 75,
  paddleHeight: 10,
  paddleSpeed: 7,
  ballRadius: 7,
  rowCount: 5,
  colCount: 8,
  brickWidth: 35,
  brickHeight: 15,
  brickPadding: 5,
  brickOffsetTop: 30,
  brickOffsetLeft: 15,
  initialLives: 3,
};

export default function BreakoutGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [gameKey, setGameKey] = useState(0); // ゲームリスタート用

  const restartGame = () => {
    setGameKey(prev => prev + 1);
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let paddleX = (CONFIG.canvasWidth - CONFIG.paddleWidth) / 2;
    let ballX = CONFIG.canvasWidth / 2;
    let ballY = CONFIG.canvasHeight - 30;
    let ballDX = 2;
    let ballDY = -2;
    let lives = CONFIG.initialLives;
    let score = 0;
    let gameOver = false;
    let win = false;

    const bricks: Brick[][] = [];
    for (let r = 0; r < CONFIG.rowCount; r++) {
      bricks[r] = [];
      for (let c = 0; c < CONFIG.colCount; c++) {
        const x = c * (CONFIG.brickWidth + CONFIG.brickPadding) + CONFIG.brickOffsetLeft;
        const y = r * (CONFIG.brickHeight + CONFIG.brickPadding) + CONFIG.brickOffsetTop;
        bricks[r][c] = { x, y, status: 1 };
      }
    }

    let rightPressed = false;
    let leftPressed = false;

    const keyDownHandler = (e: KeyboardEvent) => {
      if (e.key === 'Right' || e.key === 'ArrowRight') rightPressed = true;
      else if (e.key === 'Left' || e.key === 'ArrowLeft') leftPressed = true;
    };
    const keyUpHandler = (e: KeyboardEvent) => {
      if (e.key === 'Right' || e.key === 'ArrowRight') rightPressed = false;
      else if (e.key === 'Left' || e.key === 'ArrowLeft') leftPressed = false;
    };
    const touchMoveHandler = (e: TouchEvent) => {
      e.preventDefault();
      const rect = canvas.getBoundingClientRect();
      const touchX = e.touches[0].clientX - rect.left;
      paddleX = touchX - CONFIG.paddleWidth / 2;
      if (paddleX < 0) paddleX = 0;
      else if (paddleX + CONFIG.paddleWidth > CONFIG.canvasWidth) paddleX = CONFIG.canvasWidth - CONFIG.paddleWidth;
    };

    document.addEventListener('keydown', keyDownHandler);
    document.addEventListener('keyup', keyUpHandler);
    canvas.addEventListener('touchmove', touchMoveHandler);

    const drawBricks = () => {
      for (let r = 0; r < CONFIG.rowCount; r++) {
        for (let c = 0; c < CONFIG.colCount; c++) {
          const b = bricks[r][c];
          if (b.status) {
            ctx.fillStyle = '#22c55e';
            ctx.fillRect(b.x, b.y, CONFIG.brickWidth, CONFIG.brickHeight);
            ctx.strokeStyle = '#065f46';
            ctx.strokeRect(b.x, b.y, CONFIG.brickWidth, CONFIG.brickHeight);
          }
        }
      }
    };

    const drawBall = () => {
      ctx.beginPath();
      ctx.arc(ballX, ballY, CONFIG.ballRadius, 0, Math.PI * 2);
      ctx.fillStyle = '#ef4444';
      ctx.fill();
      ctx.closePath();
    };

    const drawPaddle = () => {
      ctx.fillStyle = '#16a34a';
      ctx.fillRect(paddleX, CONFIG.canvasHeight - CONFIG.paddleHeight, CONFIG.paddleWidth, CONFIG.paddleHeight);
    };

    const drawInfo = () => {
      ctx.fillStyle = '#000';
      ctx.font = '16px Arial';
      ctx.fillText(`Score: ${score}`, 8, 20);
      ctx.fillText(`Lives: ${lives}`, CONFIG.canvasWidth - 65, 20);
    };

    const collisionDetection = () => {
      for (let r = 0; r < CONFIG.rowCount; r++) {
        for (let c = 0; c < CONFIG.colCount; c++) {
          const b = bricks[r][c];
          if (b.status) {
            // ボールの半径を考慮した正確な衝突判定
            const ballLeft = ballX - CONFIG.ballRadius;
            const ballRight = ballX + CONFIG.ballRadius;
            const ballTop = ballY - CONFIG.ballRadius;
            const ballBottom = ballY + CONFIG.ballRadius;

            if (ballRight > b.x && ballLeft < b.x + CONFIG.brickWidth &&
                ballBottom > b.y && ballTop < b.y + CONFIG.brickHeight) {

              // 衝突方向を判定して適切に反射
              const overlapLeft = ballRight - b.x;
              const overlapRight = (b.x + CONFIG.brickWidth) - ballLeft;
              const overlapTop = ballBottom - b.y;
              const overlapBottom = (b.y + CONFIG.brickHeight) - ballTop;

              const minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

              if (minOverlap === overlapLeft || minOverlap === overlapRight) {
                ballDX = -ballDX; // 水平方向の反射
              } else {
                ballDY = -ballDY; // 垂直方向の反射
              }

              b.status = 0;
              score++;
              if (score === CONFIG.rowCount * CONFIG.colCount) win = true;
              break; // 一度に一つのブロックのみ処理
            }
          }
        }
      }
    };

    const draw = () => {
      ctx.clearRect(0, 0, CONFIG.canvasWidth, CONFIG.canvasHeight);
      drawBricks();
      drawBall();
      drawPaddle();
      drawInfo();
      collisionDetection();

      if (ballX + ballDX > CONFIG.canvasWidth - CONFIG.ballRadius || ballX + ballDX < CONFIG.ballRadius) {
        ballDX = -ballDX;
      }
      if (ballY + ballDY < CONFIG.ballRadius) {
        ballDY = -ballDY;
      } else if (ballY + ballDY > CONFIG.canvasHeight - CONFIG.ballRadius) {
        if (ballX > paddleX && ballX < paddleX + CONFIG.paddleWidth) {
          // パドルの位置に応じて反射角度を変更
          const hitPos = (ballX - paddleX) / CONFIG.paddleWidth; // 0-1の範囲
          const angle = (hitPos - 0.5) * Math.PI / 3; // -60度から+60度
          const speed = Math.sqrt(ballDX * ballDX + ballDY * ballDY);
          ballDX = Math.sin(angle) * speed;
          ballDY = -Math.abs(Math.cos(angle) * speed); // 常に上向き
        } else {
          lives--;
          if (lives === 0) gameOver = true;
          ballX = CONFIG.canvasWidth / 2;
          ballY = CONFIG.canvasHeight - 30;
          ballDX = 2;
          ballDY = -2;
          paddleX = (CONFIG.canvasWidth - CONFIG.paddleWidth) / 2;
        }
      }

      if (rightPressed && paddleX < CONFIG.canvasWidth - CONFIG.paddleWidth) {
        paddleX += CONFIG.paddleSpeed;
      } else if (leftPressed && paddleX > 0) {
        paddleX -= CONFIG.paddleSpeed;
      }

      if (!gameOver && !win) {
        requestAnimationFrame(draw);
      } else {
        ctx.font = '24px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.fillText(win ? '🎉 YOU WIN!' : '💥 GAME OVER', CONFIG.canvasWidth / 2, CONFIG.canvasHeight / 2);
      }
    };

    draw();

    return () => {
      document.removeEventListener('keydown', keyDownHandler);
      document.removeEventListener('keyup', keyUpHandler);
      canvas.removeEventListener('touchmove', touchMoveHandler);
    };
  }, [gameKey]);

  return (
    <div className="max-w-md mx-auto">
      <canvas
        ref={canvasRef}
        width={CONFIG.canvasWidth}
        height={CONFIG.canvasHeight}
        className="w-full h-auto bg-slate-100 block"
      />
      <div className="flex justify-center gap-4 mt-2">
        <button
          className="px-4 py-2 bg-gray-200 rounded"
          onMouseDown={() => document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowLeft' }))}
          onMouseUp={() => document.dispatchEvent(new KeyboardEvent('keyup', { key: 'ArrowLeft' }))}
          onTouchStart={() => document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowLeft' }))}
          onTouchEnd={() => document.dispatchEvent(new KeyboardEvent('keyup', { key: 'ArrowLeft' }))}
        >◀</button>
        <button
          className="px-4 py-2 bg-gray-200 rounded"
          onMouseDown={() => document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowRight' }))}
          onMouseUp={() => document.dispatchEvent(new KeyboardEvent('keyup', { key: 'ArrowRight' }))}
          onTouchStart={() => document.dispatchEvent(new KeyboardEvent('keydown', { key: 'ArrowRight' }))}
          onTouchEnd={() => document.dispatchEvent(new KeyboardEvent('keyup', { key: 'ArrowRight' }))}
        >▶</button>
      </div>
      <div className="flex justify-center mt-2">
        <button
          onClick={restartGame}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          リスタート
        </button>
      </div>
    </div>
  );
}