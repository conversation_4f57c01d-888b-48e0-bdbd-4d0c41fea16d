'use client';

/**
 * Space Invaders風ゲーム コンポーネント
 * Created using Gemini
 * Features: Canvas API, React hooks, TypeScript
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';

// 型定義
type Position = { x: number; y: number };
type GameObject = Position & { id: number; type?: 'player' | 'alien' | 'bullet' | 'alienBullet' };
type GameState = 'PLAYING' | 'WIN' | 'LOSE' | 'START_SCREEN';

// ゲーム設定
const PLAYER_WIDTH = 40;
const PLAYER_HEIGHT = 20;
const PLAYER_SPEED = 3;
const BULLET_WIDTH = 4;
const BULLET_HEIGHT = 10;
const BULLET_SPEED = 5;
const ALIEN_ROWS = 5;
const ALIENS_PER_ROW = 10;
const ALIEN_WIDTH = 30;
const ALIEN_HEIGHT = 20;
const ALIEN_GAP = 10;
const ALIEN_SPEED_X = 0.5;
const ALIEN_SPEED_Y = 8;
const ALIEN_SHOOT_INTERVAL = 1500; // ms
const ALIEN_BULLET_SPEED = 3;

const CANVAS_WIDTH = (ALIENS_PER_ROW * (ALIEN_WIDTH + ALIEN_GAP)) + ALIEN_GAP; // Ensure canvas is wide enough
const CANVAS_HEIGHT = 600;

// 色
const COLORS = {
  PLAYER: '#00FF00', // Green
  BULLET: '#FFFFFF', // White
  ALIEN: '#FF00FF', // Magenta
  ALIEN_BULLET: '#FFFF00', // Yellow
  TEXT: '#FFFFFF',
  BACKGROUND: '#000000',
};

export default function SpaceInvadersGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [gameState, setGameState] = useState<GameState>('START_SCREEN');
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [level, setLevel] = useState(1);
  const [player, setPlayer] = useState<Position>({ x: CANVAS_WIDTH / 2 - PLAYER_WIDTH / 2, y: CANVAS_HEIGHT - PLAYER_HEIGHT - 20 });
  const [bullets, setBullets] = useState<GameObject[]>([]);
  const [aliens, setAliens] = useState<GameObject[]>([]);
  const [alienBullets, setAlienBullets] = useState<GameObject[]>([]);
  const [alienDirection, setAlienDirection] = useState<1 | -1>(1); // 1 for right, -1 for left
  const [keysPressed, setKeysPressed] = useState<Record<string, boolean>>({});
  const gameLoopRef = useRef<number | null>(null);
  const alienShootTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // モバイル判定
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const resetGame = useCallback((nextLevel = false) => {
    setPlayer({ x: CANVAS_WIDTH / 2 - PLAYER_WIDTH / 2, y: CANVAS_HEIGHT - PLAYER_HEIGHT - 20 });
    setBullets([]);
    setAlienBullets([]);
    setKeysPressed({});
    if (!nextLevel) {
        setScore(0);
        setLives(3);
        setLevel(1);
    } else {
        setLevel(l => l + 1);
    }

    const newAliens: GameObject[] = [];
    for (let i = 0; i < ALIEN_ROWS; i++) {
      for (let j = 0; j < ALIENS_PER_ROW; j++) {
        newAliens.push({
          id: Date.now() + i * ALIENS_PER_ROW + j, // Unique ID
          x: j * (ALIEN_WIDTH + ALIEN_GAP) + ALIEN_GAP + (ALIEN_WIDTH / 2),
          y: i * (ALIEN_HEIGHT + ALIEN_GAP) + ALIEN_GAP + 50 + (ALIEN_HEIGHT / 2),
          type: 'alien'
        });
      }
    }
    setAliens(newAliens);
    setAlienDirection(1);
  }, []);
  
  const startGame = useCallback(() => {
    resetGame();
    setGameState('PLAYING');
  }, [resetGame]);

  // モバイル操作用の関数
  const moveLeft = () => {
    setKeysPressed(prev => ({ ...prev, 'ArrowLeft': true }));
    setTimeout(() => setKeysPressed(prev => ({ ...prev, 'ArrowLeft': false })), 100);
  };

  const moveRight = () => {
    setKeysPressed(prev => ({ ...prev, 'ArrowRight': true }));
    setTimeout(() => setKeysPressed(prev => ({ ...prev, 'ArrowRight': false })), 100);
  };

  const shoot = () => {
    if (gameState !== 'PLAYING') return;
    setBullets(prev => [...prev, {
      id: Date.now(),
      x: player.x + PLAYER_WIDTH / 2 - BULLET_WIDTH / 2,
      y: player.y,
      type: 'bullet'
    }]);
  };

  useEffect(() => {
    if (gameState === 'START_SCREEN') {
        // Initial setup or message can be shown here using the draw function
    }
    if (gameState === 'PLAYING') {
        // Reset aliens if empty (level clear)
        if (aliens.length === 0) {
            resetGame(true); // Go to next level
        }
    }
  }, [gameState, aliens.length, resetGame]);

  // キー入力処理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (gameState === 'START_SCREEN' && e.key === 'Enter') {
        startGame();
        return;
      }
      if (gameState !== 'PLAYING') return;
      setKeysPressed(prev => ({ ...prev, [e.key]: true }));
      if (e.key === ' ') { // Space for shooting
        e.preventDefault();
        setBullets(prev => [...prev, {
          id: Date.now(),
          x: player.x + PLAYER_WIDTH / 2 - BULLET_WIDTH / 2,
          y: player.y,
          type: 'bullet'
        }]);
      }
    };
    const handleKeyUp = (e: KeyboardEvent) => {
      setKeysPressed(prev => ({ ...prev, [e.key]: false }));
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [player, gameState, startGame]);

  // エイリアンの射撃
  useEffect(() => {
    if (gameState !== 'PLAYING' || aliens.length === 0) {
      if (alienShootTimerRef.current) clearInterval(alienShootTimerRef.current);
      return;
    }

    alienShootTimerRef.current = setInterval(() => {
      const randomAlienIndex = Math.floor(Math.random() * aliens.length);
      const shootingAlien = aliens[randomAlienIndex];
      if (shootingAlien) {
        setAlienBullets(prev => [...prev, {
          id: Date.now() + Math.random(),
          x: shootingAlien.x - BULLET_WIDTH / 2, 
          y: shootingAlien.y + ALIEN_HEIGHT / 2,
          type: 'alienBullet'
        }]);
      }
    }, ALIEN_SHOOT_INTERVAL);

    return () => {
      if (alienShootTimerRef.current) clearInterval(alienShootTimerRef.current);
    };
  }, [gameState, aliens]);

  // ゲームループ
  useEffect(() => {
    if (gameState !== 'PLAYING') {
        if (gameLoopRef.current) cancelAnimationFrame(gameLoopRef.current);
        // Keep drawing for start/end screens
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;
        draw(ctx);
        return;
    }

    const gameTick = () => {
      // Player movement
      setPlayer(prevPlayer => {
        let newX = prevPlayer.x;
        if (keysPressed['ArrowLeft'] && newX > 0) newX -= PLAYER_SPEED;
        if (keysPressed['ArrowRight'] && newX < CANVAS_WIDTH - PLAYER_WIDTH) newX += PLAYER_SPEED;
        return { ...prevPlayer, x: newX };
      });

      // Bullets movement
      setBullets(prevBullets => prevBullets
        .map(b => ({ ...b, y: b.y - BULLET_SPEED }))
        .filter(b => b.y > 0)
      );

      // Alien bullets movement
      setAlienBullets(prevAlienBullets => prevAlienBullets
        .map(ab => ({ ...ab, y: ab.y + ALIEN_BULLET_SPEED }))
        .filter(ab => ab.y < CANVAS_HEIGHT)
      );

      // Aliens movement
      let wallWasHit = false;
      for (const alien of aliens) {
        const nextX = alien.x + ALIEN_SPEED_X * alienDirection;
        if (nextX <= ALIEN_WIDTH / 2 || nextX >= CANVAS_WIDTH - ALIEN_WIDTH / 2) {
          wallWasHit = true;
          break;
        }
      }

      if (wallWasHit) {
        setAlienDirection(prevDir => (prevDir * -1) as 1 | -1);
        setAliens(prevAliens => 
          prevAliens.map(alien => ({ ...alien, y: alien.y + ALIEN_SPEED_Y }))
        );
      } else {
        setAliens(prevAliens =>
          prevAliens.map(alien => ({ ...alien, x: alien.x + ALIEN_SPEED_X * alienDirection }))
        );
      }

      // Collision detection: Bullets vs Aliens
      bullets.forEach(bullet => {
        aliens.forEach(alien => {
          if (
            bullet.x < alien.x + ALIEN_WIDTH / 2 &&
            bullet.x + BULLET_WIDTH > alien.x - ALIEN_WIDTH / 2 &&
            bullet.y < alien.y + ALIEN_HEIGHT / 2 &&
            bullet.y + BULLET_HEIGHT > alien.y - ALIEN_HEIGHT / 2
          ) {
            setScore(s => s + 100);
            setAliens(prev => prev.filter(a => a.id !== alien.id));
            setBullets(prev => prev.filter(b => b.id !== bullet.id));
          }
        });
      });

      // Collision detection: Alien Bullets vs Player
      alienBullets.forEach(alienBullet => {
        if (
          alienBullet.x < player.x + PLAYER_WIDTH &&
          alienBullet.x + BULLET_WIDTH > player.x &&
          alienBullet.y < player.y + PLAYER_HEIGHT &&
          alienBullet.y + BULLET_HEIGHT > player.y
        ) {
          setLives(l => l - 1);
          setAlienBullets(prev => prev.filter(ab => ab.id !== alienBullet.id));
          if (lives - 1 <= 0) {
            setGameState('LOSE');
          } else {
            // Brief invulnerability or reset player position can be added here
            setPlayer({ x: CANVAS_WIDTH / 2 - PLAYER_WIDTH / 2, y: CANVAS_HEIGHT - PLAYER_HEIGHT - 20 });
          }
        }
      });
        
      // Collision detection: Aliens vs Player or Aliens reach bottom
      aliens.forEach(alien => {
        if (
            (alien.x < player.x + PLAYER_WIDTH &&
            alien.x + ALIEN_WIDTH > player.x &&
            alien.y < player.y + PLAYER_HEIGHT &&
            alien.y + ALIEN_HEIGHT > player.y) ||
            alien.y + ALIEN_HEIGHT / 2 >= CANVAS_HEIGHT - PLAYER_HEIGHT // Alien reached bottom defense line
        ) {
            setGameState('LOSE');
        }
      });

      // Win condition
      if (aliens.length === 0 && gameState === 'PLAYING') {
        // setGameState('WIN'); // This will be handled by the useEffect watching aliens.length
         resetGame(true); // Move to next level or show win screen
      }
    };

    gameLoopRef.current = requestAnimationFrame(function animate() {
      gameTick();
      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) draw(ctx);
      }
      if (gameState === 'PLAYING') {
        gameLoopRef.current = requestAnimationFrame(animate);
      }
    });
    return () => {
      if (gameLoopRef.current) cancelAnimationFrame(gameLoopRef.current);
    };
  }, [gameState, keysPressed, player, bullets, aliens, alienBullets, alienDirection, lives, resetGame]);

  // 描画処理
  const draw = (ctx: CanvasRenderingContext2D) => {
    ctx.fillStyle = COLORS.BACKGROUND;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    if (gameState === 'START_SCREEN') {
        ctx.fillStyle = COLORS.TEXT;
        ctx.font = '40px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('SPACE INVADERS', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 - 40);
        ctx.font = '20px Arial';
        ctx.fillText('Press ENTER to Start', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 20);
        ctx.font = '16px Arial';
        ctx.fillText('Controls: ← → to Move, SPACE to Shoot', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 60);
        return;
    }

    // Player
    ctx.fillStyle = COLORS.PLAYER;
    ctx.fillRect(player.x, player.y, PLAYER_WIDTH, PLAYER_HEIGHT);

    // Bullets
    bullets.forEach(b => {
      ctx.fillStyle = COLORS.BULLET;
      ctx.fillRect(b.x, b.y, BULLET_WIDTH, BULLET_HEIGHT);
    });

    // Aliens
    aliens.forEach(a => {
      ctx.fillStyle = COLORS.ALIEN;
      ctx.fillRect(a.x - ALIEN_WIDTH / 2, a.y - ALIEN_HEIGHT / 2, ALIEN_WIDTH, ALIEN_HEIGHT);
    });

    // Alien Bullets
    alienBullets.forEach(ab => {
      ctx.fillStyle = COLORS.ALIEN_BULLET;
      ctx.fillRect(ab.x, ab.y, BULLET_WIDTH, BULLET_HEIGHT);
    });

    // Score, Lives, Level
    ctx.fillStyle = COLORS.TEXT;
    ctx.font = '20px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Score: ${score}`, 10, 30);
    ctx.textAlign = 'right';
    ctx.fillText(`Lives: ${lives}`, CANVAS_WIDTH - 10, 30);
    ctx.textAlign = 'center';
    ctx.fillText(`Level: ${level}`, CANVAS_WIDTH / 2, 30);

    if (gameState === 'LOSE') {
      ctx.fillStyle = 'rgba(0,0,0,0.7)';
      ctx.fillRect(0,0, CANVAS_WIDTH, CANVAS_HEIGHT);
      ctx.fillStyle = 'red';
      ctx.font = '40px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('GAME OVER', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2);
      ctx.font = '20px Arial';
      ctx.fillText('Press ENTER to Restart', CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 40);
    }
    // Win state is handled by resetting to next level, no specific WIN screen for now
  };

  // Initial draw for start screen
   useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    draw(ctx);
  }, [draw]); // draw is now a dependency

  // モバイル用操作ボタン
  const MobileControls = () => {
    if (!isMobile) return null;

    const buttonStyle = {
      width: "70px",
      height: "70px",
      backgroundColor: "#4F46E5",
      color: "white",
      border: "3px solid #6366F1",
      borderRadius: "12px",
      fontSize: "18px",
      fontWeight: "bold",
      cursor: "pointer",
      userSelect: "none" as const,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      boxShadow: "0 4px 12px rgba(79,70,229,0.4)",
      transition: "all 0.1s"
    };

    const shootButtonStyle = {
      ...buttonStyle,
      backgroundColor: "#DC2626",
      border: "3px solid #EF4444",
      boxShadow: "0 4px 12px rgba(220,38,38,0.4)",
      fontSize: "16px"
    };

    return (
      <div style={{
        position: "fixed",
        bottom: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        zIndex: 1000,
        display: "flex",
        alignItems: "center",
        gap: "20px"
      }}>
        {/* 左移動ボタン */}
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            moveLeft();
          }}
          style={buttonStyle}
        >
          ←
        </button>
        
        {/* 射撃ボタン */}
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            shoot();
          }}
          style={shootButtonStyle}
        >
          FIRE
        </button>
        
        {/* 右移動ボタン */}
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            moveRight();
          }}
          style={buttonStyle}
        >
          →
        </button>
      </div>
    );
  };

  // レスポンシブ対応のスタイル
  const containerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    gap: isMobile ? "12px" : "16px",
    padding: isMobile ? "16px" : "0",
    width: "100%",
    maxWidth: "100vw",
    overflow: "hidden"
  };

  const canvasStyle = {
    border: "2px solid #6366F1",
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
    backgroundColor: "#000",
    maxWidth: "100%",
    height: "auto",
    ...(isMobile && {
      width: "100%",
      maxWidth: "350px"
    })
  };

  const statsStyle = {
    display: "grid",
    gridTemplateColumns: "1fr 1fr 1fr",
    gap: isMobile ? "8px" : "16px",
    textAlign: "center" as const,
    backgroundColor: "#1F2937",
    color: "white",
    padding: isMobile ? "12px" : "16px",
    borderRadius: "8px",
    width: "100%",
    maxWidth: isMobile ? "350px" : `${CANVAS_WIDTH}px`
  };

  return (
    <>
      <div style={containerStyle}>
        <div style={statsStyle}>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>SCORE</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{score}</div>
          </div>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>LEVEL</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{level}</div>
          </div>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>LIVES</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{lives}</div>
          </div>
        </div>

        <canvas
          ref={canvasRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          style={canvasStyle}
        />

        {(gameState === 'LOSE' || gameState === 'WIN' || gameState === 'START_SCREEN') && (
          <button 
            onClick={startGame}
            style={{
              marginTop: "16px",
              backgroundColor: "#4F46E5",
              color: "white",
              fontWeight: "bold",
              padding: isMobile ? "12px 24px" : "16px 32px",
              borderRadius: "8px",
              border: "none",
              fontSize: isMobile ? "16px" : "18px",
              cursor: "pointer",
              boxShadow: "0 4px 12px rgba(79,70,229,0.4)",
              transition: "all 0.1s"
            }}
          >
            {gameState === 'LOSE' ? 'Restart Game' : 'Start Game'}
          </button>
        )}

        {/* モバイル用操作説明 */}
        {isMobile && (
          <div style={{
            backgroundColor: "#1F2937",
            color: "white",
            padding: "12px",
            borderRadius: "8px",
            textAlign: "center",
            fontSize: "12px",
            maxWidth: "350px",
            marginTop: "8px"
          }}>
            <div style={{ fontWeight: "bold", marginBottom: "4px" }}>操作方法</div>
            <div>← → で移動、FIREで射撃</div>
          </div>
        )}
      </div>
      <MobileControls />
    </>
  );
} 