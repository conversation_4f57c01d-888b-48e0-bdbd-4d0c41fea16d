"use client";
import React, { useState, useEffect, useRef } from 'react';

type Cell = {
  x: number;
  y: number;
  isMine: boolean;
  adjacent: number;
  isRevealed: boolean;
  isFlagged: boolean;
};

const GRID_SIZE = 9;
const MINE_COUNT = 10;

export default function MinesweeperGame() {
  const [grid, setGrid] = useState<Cell[][]>([]);
  const [flagsLeft, setFlagsLeft] = useState<number>(MINE_COUNT);
  const [timer, setTimer] = useState<number>(0);
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [gameOver, setGameOver] = useState<boolean>(false);
  const [win, setWin] = useState<boolean>(false);
  const [mode, setMode] = useState<'open' | 'flag'>('open');
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const initGrid = (): Cell[][] => {
    const newGrid: Cell[][] = Array.from({ length: GRID_SIZE }, (_, y) =>
      Array.from({ length: GRID_SIZE }, (_, x) => ({
        x,
        y,
        isMine: false,
        adjacent: 0,
        isRevealed: false,
        isFlagged: false
      }))
    );
    let minesPlaced = 0;
    while (minesPlaced < MINE_COUNT) {
      const ry = Math.floor(Math.random() * GRID_SIZE);
      const rx = Math.floor(Math.random() * GRID_SIZE);
      if (!newGrid[ry][rx].isMine) {
        newGrid[ry][rx].isMine = true;
        minesPlaced++;
      }
    }
    for (let y = 0; y < GRID_SIZE; y++) {
      for (let x = 0; x < GRID_SIZE; x++) {
        if (!newGrid[y][x].isMine) {
          let count = 0;
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const ny = y + dy;
              const nx = x + dx;
              if (
                ny >= 0 &&
                ny < GRID_SIZE &&
                nx >= 0 &&
                nx < GRID_SIZE &&
                newGrid[ny][nx].isMine
              ) {
                count++;
              }
            }
          }
          newGrid[y][x].adjacent = count;
        }
      }
    }
    return newGrid;
  };

  const revealCell = (y: number, x: number, gridCopy: Cell[][]): void => {
    const cell = gridCopy[y][x];
    if (cell.isRevealed || cell.isFlagged) return;
    cell.isRevealed = true;
    if (cell.adjacent === 0 && !cell.isMine) {
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          const ny = y + dy;
          const nx = x + dx;
          if (
            ny >= 0 &&
            ny < GRID_SIZE &&
            nx >= 0 &&
            nx < GRID_SIZE &&
            !gridCopy[ny][nx].isRevealed
          ) {
            revealCell(ny, nx, gridCopy);
          }
        }
      }
    }
  };

  const checkWin = (gridCopy: Cell[][]): boolean => {
    for (const row of gridCopy) {
      for (const cell of row) {
        if (!cell.isMine && !cell.isRevealed) return false;
      }
    }
    return true;
  };

  const startTimer = () => {
    setIsRunning(true);
    timerRef.current = setInterval(() => {
      setTimer((t) => t + 1);
    }, 1000);
  };

  const stopTimer = () => {
    setIsRunning(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleCellClick = (y: number, x: number) => {
    if (gameOver || win) return;
    if (!isRunning) startTimer();
    const newGrid = grid.map((row) => row.map((c) => ({ ...c })));
    const cell = newGrid[y][x];
    if (mode === 'flag') {
      if (!cell.isRevealed) {
        cell.isFlagged = !cell.isFlagged;
        setFlagsLeft((f) => (cell.isFlagged ? f - 1 : f + 1));
      }
    } else {
      if (cell.isFlagged) return;
      if (cell.isMine) {
        newGrid.forEach((row) =>
          row.forEach((c) => {
            if (c.isMine) c.isRevealed = true;
          })
        );
        setGameOver(true);
        stopTimer();
      } else {
        revealCell(y, x, newGrid);
        if (checkWin(newGrid)) {
          newGrid.forEach((row) =>
            row.forEach((c) => {
              if (c.isMine) c.isFlagged = true;
            })
          );
          setWin(true);
          stopTimer();
        }
      }
    }
    setGrid(newGrid);
  };

  const restart = () => {
    stopTimer();
    setGrid(initGrid());
    setFlagsLeft(MINE_COUNT);
    setTimer(0);
    setGameOver(false);
    setWin(false);
    setMode('open');
  };

  useEffect(() => {
    setGrid(initGrid());
    return () => stopTimer();
  }, []);

  return (
    <div className="max-w-md mx-auto">
      <div className="flex justify-between mb-2 text-sm">
        <div>⏱ {timer}s</div>
        <div>🚩 {flagsLeft}</div>
        <button
          onClick={restart}
          className="px-2 py-1 bg-blue-500 text-white rounded"
        >
          リスタート
        </button>
      </div>
      <div className="flex justify-center mb-2">
        <button
          className={`px-2 py-1 mr-2 rounded ${
            mode === 'open'
              ? 'bg-gray-600 text-white'
              : 'bg-white border'
          }`}
          onClick={() => setMode('open')}
        >
          開く
        </button>
        <button
          className={`px-2 py-1 rounded ${
            mode === 'flag'
              ? 'bg-gray-600 text-white'
              : 'bg-white border'
          }`}
          onClick={() => setMode('flag')}
        >
          旗
        </button>
      </div>
      <div
        className="grid gap-0.5"
        style={{
          gridTemplateColumns: `repeat(${GRID_SIZE}, minmax(0, 1fr))`,
        }}
      >
        {grid.map((row, y) =>
          row.map((cell, x) => (
            <div
              key={`${y}-${x}`}
              className={`aspect-square flex items-center justify-center text-sm font-bold ${
                cell.isRevealed ? 'bg-slate-200' : 'bg-slate-400 cursor-pointer'
              } ${
                cell.isRevealed && cell.isMine ? 'bg-red-500' : ''
              }`}
              onClick={() => handleCellClick(y, x)}
            >
              {cell.isRevealed
                ? cell.isMine
                  ? '💣'
                  : cell.adjacent > 0
                  ? cell.adjacent
                  : ''
                : cell.isFlagged
                ? '🚩'
                : ''}
            </div>
          ))
        )}
      </div>
      {(gameOver || win) && (
        <div className="mt-4 text-center font-bold">
          {gameOver ? '💥 ゲームオーバー' : '🎉 クリア！'}
        </div>
      )}
    </div>
  );
} 