'use client';

/**
 * Pac-Man風ゲーム コンポーネント
 * Created using Gemini
 * Features: Canvas API, React hooks, TypeScript
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';

// 型定義
type Position = { x: number; y: number };
type Direction = 'UP' | 'DOWN' | 'LEFT' | 'RIGHT' | 'STOP';
type GameState = 'PLAYING' | 'WIN' | 'LOSE';

// ゲーム設定
const TILE_SIZE = 30;
const PACMAN_SPEED = 1;
const GHOST_SPEED = 0.8;
const POWER_PELLET_DURATION = 5000; // ms
const GAME_SPEED = 60; // FPS

// 色
const COLORS = {
  WALL: '#1976D2', // Blue
  PELLET: '#FFEB3B', // Yellow
  POWER_PELLET: '#FF9800', // Orange
  PACMAN: '#FFEB3B', // Yellow
  GHOST_NORMAL: ['#FF5252', '#448AFF', '#FF4081', '#00E676'], // <PERSON>, <PERSON>, <PERSON>, <PERSON>
  GHOST_FRIGHTENED: '#757575', // Grey
  TEXT: '#FFFFFF',
  BACKGROUND: '#000000',
};

// 迷路データ (0: 空白, 1: 壁, 2: アイテム, 3: パワーアイテム, P: プレイヤー初期位置, G: 敵初期位置)
const MAZE_LAYOUT = [
  '1111111111111111111',
  '1P22222221222222221',
  '1211121121211211121',
  '1311121121211211131',
  '1222222222222222221',
  '1211121211121211121',
  '1222221221221222221',
  '11111211G1G11211111',
  '0000121000001210000',
  '1111121111111211111',
  '12222222G1G22222221',
  '1211121121211211121',
  '1322122222222122321',
  '1121121211121211211',
  '1222221221221222221',
  '1211111121211111121',
  '1P22222222222222221',
  '1111111111111111111',
];

export default function PacmanGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [gameState, setGameState] = useState<GameState>('PLAYING');
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [level, setLevel] = useState(1);
  const [pacman, setPacman] = useState<Position>({ x: 0, y: 0 });
  const [pacmanDirection, setPacmanDirection] = useState<Direction>('STOP');
  const [ghosts, setGhosts] = useState<Position[]>([]);
  const [ghostDirections, setGhostDirections] = useState<Direction[]>([]); // ゴーストの進行方向を管理
  const [pellets, setPellets] = useState<Position[]>([]);
  const [powerPellets, setPowerPellets] = useState<Position[]>([]);
  const [isFrightened, setIsFrightened] = useState(false);
  const frightenedTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [isMobile, setIsMobile] = useState(false);

  // モバイル判定
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 迷路の初期化
  const initializeMaze = useCallback(() => {
    const newPellets: Position[] = [];
    const newPowerPellets: Position[] = [];
    const newGhosts: Position[] = [];
    const initialGhostDirections: Direction[] = [];
    let pacmanStartPos: Position = { x: 0, y: 0 };

    MAZE_LAYOUT.forEach((rowStr, y) => {
      rowStr.split('').forEach((char, x) => {
        const pos = { x: x * TILE_SIZE + TILE_SIZE / 2, y: y * TILE_SIZE + TILE_SIZE / 2 };
        if (char === 'P') pacmanStartPos = pos;
        if (char === 'G') {
          newGhosts.push(pos);
          initialGhostDirections.push(getRandomDirection()); // 各ゴーストにランダムな初期方向を設定
        }
        if (char === '2') newPellets.push(pos);
        if (char === '3') newPowerPellets.push(pos);
      });
    });

    setPacman(pacmanStartPos);
    setGhosts(newGhosts);
    setGhostDirections(initialGhostDirections);
    setPellets(newPellets);
    setPowerPellets(newPowerPellets);
    setPacmanDirection('STOP');
  }, []);

  // ゲーム開始・リセット
  const startGame = useCallback(() => {
    initializeMaze();
    setScore(0);
    setLives(3);
    setGameState('PLAYING');
    setIsFrightened(false);
    if (frightenedTimerRef.current) clearTimeout(frightenedTimerRef.current);
  }, [initializeMaze]);

  useEffect(() => {
    startGame();
  }, [startGame, level]); // レベル変更時にも再初期化

  // キー入力処理
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    e.preventDefault(); // 画面スクロールを抑制
    switch (e.key) {
      case 'ArrowUp': case 'w': setPacmanDirection('UP'); break;
      case 'ArrowDown': case 's': setPacmanDirection('DOWN'); break;
      case 'ArrowLeft': case 'a': setPacmanDirection('LEFT'); break;
      case 'ArrowRight': case 'd': setPacmanDirection('RIGHT'); break;
    }
  }, []);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // 衝突判定 (タイルベース)
  const isWallCollision = (pos: Position): boolean => {
    const tileX = Math.floor(pos.x / TILE_SIZE);
    const tileY = Math.floor(pos.y / TILE_SIZE);
    if (tileX < 0 || tileX >= MAZE_LAYOUT[0].length || tileY < 0 || tileY >= MAZE_LAYOUT.length) {
      return true; // 範囲外は壁とみなす
    }
    return MAZE_LAYOUT[tileY]?.[tileX] === '1';
  };
  
  // ゲームループ
  useEffect(() => {
    const gameTick = () => {
      if (gameState !== 'PLAYING') return;

      // Pacman移動
      setPacman(prevPacman => {
        if (pacmanDirection === 'STOP') return prevPacman;
        
        let newX = prevPacman.x;
        let newY = prevPacman.y;
        switch (pacmanDirection) {
          case 'UP': newY -= PACMAN_SPEED; break;
          case 'DOWN': newY += PACMAN_SPEED; break;
          case 'LEFT': newX -= PACMAN_SPEED; break;
          case 'RIGHT': newX += PACMAN_SPEED; break;
        }
        
        // 衝突判定のための予測位置 (円の中心から少し先を見る)
        const checkDistance = TILE_SIZE / 3;
        const nextTileCenterX = Math.floor((newX + (pacmanDirection === 'LEFT' ? -checkDistance : pacmanDirection === 'RIGHT' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE/2;
        const nextTileCenterY = Math.floor((newY + (pacmanDirection === 'UP' ? -checkDistance : pacmanDirection === 'DOWN' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE/2;

        if (!isWallCollision({ x: nextTileCenterX, y: nextTileCenterY })) {
          return { x: newX, y: newY };
        }        
        return prevPacman; // 壁なら移動しない
      });

      // Ghost移動 (経路探索ロジック改善)
      setGhosts(prevGhosts => {
        const newDirections = [...ghostDirections];
        const updatedGhosts = prevGhosts.map((ghost, index) => {
          let currentDir = newDirections[index];
          const possibleMoves: { dir: Direction; pos: Position; distToPacman: number }[] = [];
          const allDirections: Direction[] = ['UP', 'DOWN', 'LEFT', 'RIGHT'];

          for (const testDir of allDirections) {
            // 行き止まりでない限り、来た方向へすぐ戻るのは避ける (確率で許可)
            if (allDirections.filter(d => {
                let checkX = ghost.x;
                let checkY = ghost.y;
                const speed = GHOST_SPEED; //仮のスピード
                switch (d) {
                  case 'UP': checkY -= speed; break;
                  case 'DOWN': checkY += speed; break;
                  case 'LEFT': checkX -= speed; break;
                  case 'RIGHT': checkX += speed; break;
                }
                const checkDistance = TILE_SIZE / 3;
                const nextTileXForCheck = Math.floor((checkX + (d === 'LEFT' ? -checkDistance : d === 'RIGHT' ? checkDistance : 0)) / TILE_SIZE);
                const nextTileYForCheck = Math.floor((checkY + (d === 'UP' ? -checkDistance : d === 'DOWN' ? checkDistance : 0)) / TILE_SIZE);
                 return !isWallCollision({x: nextTileXForCheck * TILE_SIZE + TILE_SIZE/2, y: nextTileYForCheck * TILE_SIZE + TILE_SIZE/2});
            }).length > 1 && testDir === getOppositeDirection(currentDir) && Math.random() < 0.9) {
                 continue;
            }


            let newX = ghost.x;
            let newY = ghost.y;

            switch (testDir) {
              case 'UP': newY -= GHOST_SPEED; break;
              case 'DOWN': newY += GHOST_SPEED; break;
              case 'LEFT': newX -= GHOST_SPEED; break;
              case 'RIGHT': newX += GHOST_SPEED; break;
            }

            // 次のタイルの中心で衝突判定
            // 少し内側で判定することで、角抜けを防ぐ調整
            const checkDistance = TILE_SIZE / 3;
            const nextTileCenterX = Math.floor((newX + (testDir === 'LEFT' ? -checkDistance : testDir === 'RIGHT' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE / 2;
            const nextTileCenterY = Math.floor((newY + (testDir === 'UP' ? -checkDistance : testDir === 'DOWN' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE / 2;
            
            if (!isWallCollision({ x: nextTileCenterX, y: nextTileCenterY })) {
              possibleMoves.push({
                dir: testDir,
                pos: { x: newX, y: newY },
                distToPacman: Math.hypot(newX - pacman.x, newY - pacman.y),
              });
            }
          }

          if (possibleMoves.length > 0) {
            possibleMoves.sort((a, b) => {
              // isFrightened状態ならパックマンから遠い順、そうでなければ近い順
              return isFrightened ? b.distToPacman - a.distToPacman : a.distToPacman - b.distToPacman;
            });

            let chosenMove = possibleMoves[0];
            // 直進性を少し持たせる: 現在の進行方向が選択肢にあれば、一定確率で優先
            const preferredMove = possibleMoves.find(move => move.dir === currentDir);
            if (preferredMove && preferredMove.dir !== getOppositeDirection(currentDir) && Math.random() < 0.7 && possibleMoves.length >1 ) {
                 if(isFrightened && preferredMove.distToPacman > chosenMove.distToPacman){ // 恐怖時はより遠ざかる方を優先
                    chosenMove = preferredMove;
                 } else if (!isFrightened && preferredMove.distToPacman < chosenMove.distToPacman) { // 通常時はより近付く方を優先
                    chosenMove = preferredMove;
                 } else if (possibleMoves.filter(m => m.dir !== getOppositeDirection(currentDir)).length > 1) {
                    // 直進が最適でない場合でも、行き止まりでなければ直進を試みることがある
                    if (Math.random() < 0.5) chosenMove = preferredMove;
                 }


            } else if (possibleMoves.length > 1 && possibleMoves[0].dir === getOppositeDirection(currentDir)) {
                // 最善手がUターンの場合、他の選択肢があればそちらを優先することがある
                if(Math.random() < 0.3) chosenMove = possibleMoves[1] || possibleMoves[0];
            }


            newDirections[index] = chosenMove.dir;
            return chosenMove.pos;
          }
          
          // 移動可能な方向がない場合（ほぼ起こらないはずだが念のため）
          // 現在の方向と反対方向を試す（行き止まりからの脱出）
          const oppositeDir = getOppositeDirection(currentDir);
          let newX = ghost.x;
          let newY = ghost.y;
           switch (oppositeDir) {
              case 'UP': newY -= GHOST_SPEED; break;
              case 'DOWN': newY += GHOST_SPEED; break;
              case 'LEFT': newX -= GHOST_SPEED; break;
              case 'RIGHT': newX += GHOST_SPEED; break;
            }
            const checkDistance = TILE_SIZE / 3;
            const nextTileCenterX = Math.floor((newX + (oppositeDir === 'LEFT' ? -checkDistance : oppositeDir === 'RIGHT' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE / 2;
            const nextTileCenterY = Math.floor((newY + (oppositeDir === 'UP' ? -checkDistance : oppositeDir === 'DOWN' ? checkDistance : 0)) / TILE_SIZE) * TILE_SIZE + TILE_SIZE / 2;

           if(!isWallCollision({ x: nextTileCenterX, y: nextTileCenterY})){
               newDirections[index] = oppositeDir;
               return {x: newX, y: newY};
           }
           // それでもダメならランダムな方向に
           const randomFallbackDir = getRandomDirectionExcluding(currentDir);
            newDirections[index] = randomFallbackDir; // Fallback
            // Note: ここで実際に移動する処理は省略。元の位置に留まる。より高度なスタック解除ロジックが必要な場合がある。
            return ghost; // 本当に行き場がない場合はその場に留まる
        });
        setGhostDirections(newDirections);
        return updatedGhosts;
      });

      // アイテム取得
      setPellets(prevPellets => prevPellets.filter(p => {
        const dist = Math.hypot(pacman.x - p.x, pacman.y - p.y);
        if (dist < TILE_SIZE / 2) {
          setScore(s => s + 10);
          return false;
        }
        return true;
      }));

      setPowerPellets(prevPowerPellets => prevPowerPellets.filter(pp => {
        const dist = Math.hypot(pacman.x - pp.x, pacman.y - pp.y);
        if (dist < TILE_SIZE / 2) {
          setScore(s => s + 50);
          setIsFrightened(true);
          if (frightenedTimerRef.current) clearTimeout(frightenedTimerRef.current);
          frightenedTimerRef.current = setTimeout(() => setIsFrightened(false), POWER_PELLET_DURATION);
          return false;
        }
        return true;
      }));

      // Ghostとの衝突
      ghosts.forEach((ghost, index) => {
        const dist = Math.hypot(pacman.x - ghost.x, pacman.y - ghost.y);
        if (dist < TILE_SIZE / 2) {
          if (isFrightened) {
            setScore(s => s + 200);
            // Ghostを初期位置に戻す (簡易)
            const initialGhostPositions = MAZE_LAYOUT.reduce((acc, rowStr, y) => {
                rowStr.split('').forEach((char, x) => {
                    if (char === 'G') acc.push({ x: x * TILE_SIZE + TILE_SIZE / 2, y: y * TILE_SIZE + TILE_SIZE / 2 });
                });
                return acc;
            }, [] as Position[]);
            setGhosts(prev => prev.map((g, i) => i === index ? initialGhostPositions[i % initialGhostPositions.length] : g));

          } else {
            setLives(l => l - 1);
            if (lives - 1 <= 0) {
              setGameState('LOSE');
            } else {
              // Pacmanを初期位置に戻す
               initializeMaze(); // or just reset pacman position
            }
          }
        }
      });

      // クリア判定
      if (pellets.length === 0 && powerPellets.length === 0 && gameState === 'PLAYING') {
        setGameState('WIN');
        setLevel(l => l + 1); // 次のレベルへ
      }
    };

    const gameInterval = setInterval(gameTick, 1000 / GAME_SPEED);
    return () => {
      clearInterval(gameInterval);
    };
  }, [gameState, pacman, pacmanDirection, ghosts, pellets, powerPellets, isFrightened, lives, initializeMaze, ghostDirections]);

  // 描画処理
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = MAZE_LAYOUT[0].length * TILE_SIZE;
    canvas.height = MAZE_LAYOUT.length * TILE_SIZE;

    // 背景描画
    ctx.fillStyle = COLORS.BACKGROUND;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 壁描画
    ctx.fillStyle = COLORS.WALL;
    MAZE_LAYOUT.forEach((rowStr, y) => {
      rowStr.split('').forEach((char, x) => {
        if (char === '1') {
          ctx.fillRect(x * TILE_SIZE, y * TILE_SIZE, TILE_SIZE, TILE_SIZE);
        }
      });
    });

    // アイテム描画
    ctx.fillStyle = COLORS.PELLET;
    pellets.forEach(p => {
      ctx.beginPath();
      ctx.arc(p.x, p.y, TILE_SIZE / 5, 0, Math.PI * 2);
      ctx.fill();
    });

    // パワーアイテム描画
    ctx.fillStyle = COLORS.POWER_PELLET;
    powerPellets.forEach(pp => {
      ctx.beginPath();
      ctx.arc(pp.x, pp.y, TILE_SIZE / 3, 0, Math.PI * 2);
      ctx.fill();
    });

    // Pacman描画
    ctx.fillStyle = COLORS.PACMAN;
    ctx.beginPath();
    // 口の開閉アニメーション（簡易版）
    const mouthAngle = Math.PI / 6 * (Math.sin(Date.now() / 150) + 1); // 0 から PI/3 の間で変化
    let startAngle = 0;
    let endAngle = Math.PI * 2;

    switch (pacmanDirection) {
        case 'UP':
            startAngle = -Math.PI / 2 - mouthAngle / 2;
            endAngle = -Math.PI / 2 + mouthAngle / 2;
            break;
        case 'DOWN':
            startAngle = Math.PI / 2 - mouthAngle / 2;
            endAngle = Math.PI / 2 + mouthAngle / 2;
            break;
        case 'LEFT':
            startAngle = Math.PI - mouthAngle / 2;
            endAngle = Math.PI + mouthAngle / 2;
            break;
        case 'RIGHT':
            startAngle = 0 - mouthAngle/2;
            endAngle = 0 + mouthAngle/2;
            break;
        case 'STOP': // 停止中は口を閉じる
            startAngle = 0;
            endAngle = Math.PI * 2;
            break;
    }
    if (pacmanDirection !== 'STOP') {
        ctx.arc(pacman.x, pacman.y, TILE_SIZE / 2 - 2, startAngle, endAngle, true);
        ctx.lineTo(pacman.x, pacman.y);
    } else {
        ctx.arc(pacman.x, pacman.y, TILE_SIZE / 2 - 2, 0, Math.PI * 2);
    }
    ctx.fill();


    // Ghost描画
    ghosts.forEach((ghost, index) => {
      ctx.fillStyle = isFrightened ? COLORS.GHOST_FRIGHTENED : COLORS.GHOST_NORMAL[index % COLORS.GHOST_NORMAL.length];
      ctx.beginPath();
      // ゴーストの形状を少し変更（半円と矩形）
      const ghostRadius = TILE_SIZE / 2 - 2;
      ctx.arc(ghost.x, ghost.y, ghostRadius, Math.PI, 0);
      ctx.lineTo(ghost.x + ghostRadius, ghost.y + ghostRadius);
      ctx.lineTo(ghost.x - ghostRadius, ghost.y + ghostRadius);
      ctx.closePath();
      ctx.fill();

      // 目
      ctx.fillStyle = 'white';
      ctx.beginPath();
      ctx.arc(ghost.x - ghostRadius / 3, ghost.y - ghostRadius / 3, ghostRadius / 4, 0, Math.PI * 2);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(ghost.x + ghostRadius / 3, ghost.y - ghostRadius / 3, ghostRadius / 4, 0, Math.PI * 2);
      ctx.fill();

      ctx.fillStyle = 'black';
       ctx.beginPath();
      ctx.arc(ghost.x - ghostRadius / 3, ghost.y - ghostRadius / 3, ghostRadius / 8, 0, Math.PI * 2);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(ghost.x + ghostRadius / 3, ghost.y - ghostRadius / 3, ghostRadius / 8, 0, Math.PI * 2);
      ctx.fill();

    });

    // スコア・ライフ表示
    ctx.fillStyle = COLORS.TEXT;
    ctx.font = '20px Arial';
    ctx.fillText(`Score: ${score}`, 10, 20);
    ctx.fillText(`Lives: ${lives}`, canvas.width - 80, 20);
    ctx.fillText(`Level: ${level}`, canvas.width / 2 - 30, 20);

    // ゲームオーバー/クリアメッセージ
    if (gameState === 'LOSE') {
      ctx.fillStyle = 'rgba(0,0,0,0.7)';
      ctx.fillRect(0,0, canvas.width, canvas.height);
      ctx.fillStyle = 'red';
      ctx.font = '40px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('GAME OVER', canvas.width / 2, canvas.height / 2);
      ctx.font = '20px Arial';
      ctx.fillText('Press R to Restart', canvas.width / 2, canvas.height / 2 + 40);
    }
    if (gameState === 'WIN') {
        ctx.fillStyle = 'rgba(0,0,0,0.7)';
        ctx.fillRect(0,0, canvas.width, canvas.height);
        ctx.fillStyle = 'green';
        ctx.font = '40px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('LEVEL CLEARED!', canvas.width / 2, canvas.height / 2);
        ctx.font = '20px Arial';
        ctx.fillText('Loading Next Level...', canvas.width / 2, canvas.height / 2 + 40);
    }

  }, [gameState, score, lives, level, pacman, ghosts, pellets, powerPellets, isFrightened]);

  // モバイル操作用の関数
  const moveUp = () => setPacmanDirection('UP');
  const moveDown = () => setPacmanDirection('DOWN');
  const moveLeft = () => setPacmanDirection('LEFT');
  const moveRight = () => setPacmanDirection('RIGHT');

  // モバイル用操作ボタン
  const MobileControls = () => {
    if (!isMobile) return null;

    const buttonStyle = {
      width: "70px",
      height: "70px",
      backgroundColor: "#FFEB3B",
      color: "#000",
      border: "3px solid #FFC107",
      borderRadius: "12px",
      fontSize: "24px",
      fontWeight: "bold",
      cursor: "pointer",
      userSelect: "none" as const,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      boxShadow: "0 4px 12px rgba(255,235,59,0.4)",
      transition: "all 0.1s"
    };

    return (
      <div style={{
        position: "fixed",
        bottom: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        zIndex: 1000,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: "8px"
      }}>
        {/* 上ボタン */}
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            moveUp();
          }}
          style={buttonStyle}
        >
          ↑
        </button>
        
        {/* 左右ボタン */}
        <div style={{ display: "flex", gap: "16px" }}>
          <button
            onTouchStart={(e) => {
              e.preventDefault();
              moveLeft();
            }}
            style={buttonStyle}
          >
            ←
          </button>
          <button
            onTouchStart={(e) => {
              e.preventDefault();
              moveRight();
            }}
            style={buttonStyle}
          >
            →
          </button>
        </div>
        
        {/* 下ボタン */}
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            moveDown();
          }}
          style={buttonStyle}
        >
          ↓
        </button>
      </div>
    );
  };

  const canvasWidth = MAZE_LAYOUT[0].length * TILE_SIZE;
  const canvasHeight = MAZE_LAYOUT.length * TILE_SIZE;

  // レスポンシブ対応のスタイル
  const containerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    gap: isMobile ? "12px" : "16px",
    padding: isMobile ? "16px" : "0",
    width: "100%",
    maxWidth: "100vw",
    overflow: "hidden"
  };

  const canvasStyle = {
    border: "2px solid #FFEB3B",
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
    backgroundColor: "#000",
    maxWidth: "100%",
    height: "auto",
    ...(isMobile && {
      width: "100%",
      maxWidth: "350px"
    })
  };

  const statsStyle = {
    display: "grid",
    gridTemplateColumns: "1fr 1fr 1fr",
    gap: isMobile ? "8px" : "16px",
    textAlign: "center" as const,
    backgroundColor: "#1F2937",
    color: "white",
    padding: isMobile ? "12px" : "16px",
    borderRadius: "8px",
    width: "100%",
    maxWidth: isMobile ? "350px" : "400px"
  };

  return (
    <>
      <div style={containerStyle}>
        <div style={statsStyle}>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>SCORE</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{score}</div>
          </div>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>LEVEL</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{level}</div>
          </div>
          <div>
            <div style={{ fontSize: isMobile ? "12px" : "14px" }}>LIVES</div>
            <div style={{ fontSize: isMobile ? "20px" : "24px", fontWeight: "bold" }}>{lives}</div>
          </div>
        </div>

        <canvas
          ref={canvasRef}
          width={canvasWidth} 
          height={canvasHeight}
          style={canvasStyle}
        />

        {(gameState === 'LOSE' || gameState === 'WIN') && (
          <button 
            onClick={startGame}
            style={{
              marginTop: "16px",
              backgroundColor: "#FFEB3B",
              color: "#000",
              fontWeight: "bold",
              padding: isMobile ? "12px 24px" : "16px 32px",
              borderRadius: "8px",
              border: "none",
              fontSize: isMobile ? "16px" : "18px",
              cursor: "pointer",
              boxShadow: "0 4px 12px rgba(255,235,59,0.4)"
            }}
          >
            {gameState === 'LOSE' ? 'Restart Game' : 'Next Level'}
          </button>
        )}

        {/* モバイル用操作説明 */}
        {isMobile && (
          <div style={{
            backgroundColor: "#1F2937",
            color: "white",
            padding: "12px",
            borderRadius: "8px",
            textAlign: "center",
            fontSize: "12px",
            maxWidth: "350px",
            marginTop: "8px"
          }}>
            <div style={{ fontWeight: "bold", marginBottom: "4px" }}>操作方法</div>
            <div>画面下部の矢印ボタンでパックマンを操作</div>
          </div>
        )}
      </div>
      <MobileControls />
    </>
  );
}

// Helper Functions
const getRandomDirection = (): Direction => {
  const directions: Direction[] = ['UP', 'DOWN', 'LEFT', 'RIGHT'];
  return directions[Math.floor(Math.random() * directions.length)];
};

const getOppositeDirection = (dir: Direction): Direction => {
  if (dir === 'UP') return 'DOWN';
  if (dir === 'DOWN') return 'UP';
  if (dir === 'LEFT') return 'RIGHT';
  if (dir === 'RIGHT') return 'LEFT';
  return 'STOP'; // Should not happen
};

const getRandomDirectionExcluding = (excludeDir?: Direction): Direction => {
    const directions: Direction[] = ['UP', 'DOWN', 'LEFT', 'RIGHT'];
    const availableDirections = directions.filter(d => d !== excludeDir);
    if (availableDirections.length === 0) return getRandomDirection(); // Fallback
    return availableDirections[Math.floor(Math.random() * availableDirections.length)];
}; 