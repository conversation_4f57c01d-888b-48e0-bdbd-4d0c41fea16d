"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";

// テトリスの設定
const COLS = 10;
const ROWS = 20;
const BLOCK_SIZE = 30;
const TICK_INTERVAL = 500; // ms

// テトリスのブロック定義
const TETROMINOS = [
  // I
  {
    shape: [
      [0, 0, 0, 0],
      [1, 1, 1, 1],
      [0, 0, 0, 0],
      [0, 0, 0, 0],
    ],
    color: "#00f0f0",
  },
  // J
  {
    shape: [
      [1, 0, 0],
      [1, 1, 1],
      [0, 0, 0],
    ],
    color: "#0000f0",
  },
  // L
  {
    shape: [
      [0, 0, 1],
      [1, 1, 1],
      [0, 0, 0],
    ],
    color: "#f0a000",
  },
  // O
  {
    shape: [
      [1, 1],
      [1, 1],
    ],
    color: "#f0f000",
  },
  // S
  {
    shape: [
      [0, 1, 1],
      [1, 1, 0],
      [0, 0, 0],
    ],
    color: "#00f000",
  },
  // T
  {
    shape: [
      [0, 1, 0],
      [1, 1, 1],
      [0, 0, 0],
    ],
    color: "#a000f0",
  },
  // Z
  {
    shape: [
      [1, 1, 0],
      [0, 1, 1],
      [0, 0, 0],
    ],
    color: "#f00000",
  },
];

// RPG要素：スキル定義
const SKILLS = [
  {
    id: "clearLine",
    name: "一列消去",
    description: "一番下の行を即座に消去",
    level: 2, // このレベルから使用可能
    cooldown: 15, // 使用後のクールダウン（秒）
    icon: "↓"
  },
  {
    id: "slowDown",
    name: "スローダウン",
    description: "10秒間ブロックの落下速度を遅くする",
    level: 3,
    cooldown: 20,
    icon: "⏱"
  },
  {
    id: "changeBlock",
    name: "ブロック変換",
    description: "現在のブロックを別のランダムなブロックに変換",
    level: 4,
    cooldown: 12,
    icon: "↺"
  },
  {
    id: "removeBlocks",
    name: "ブロック破壊",
    description: "盤面上のランダムな位置のブロックを消去",
    level: 5,
    cooldown: 30,
    icon: "💥"
  }
];

type Tetromino = typeof TETROMINOS[number];
type Skill = typeof SKILLS[number];

type Cell = {
  color: string;
  filled: boolean;
};

type Board = (Cell | null)[][];

type SkillState = {
  id: string;
  cooldown: number;
  active: boolean;
  duration?: number;
};

// RPG要素：レベルごとの経験値要件
const EXP_PER_LEVEL = [
  0,    // レベル1（初期）
  100,  // レベル2
  300,  // レベル3
  600,  // レベル4
  1000, // レベル5
  1500, // レベル6
  2100, // レベル7
  2800, // レベル8
  3600, // レベル9
  5000, // レベル10
];

function randomTetromino(): Tetromino {
  return TETROMINOS[Math.floor(Math.random() * TETROMINOS.length)];
}

function rotate(matrix: number[][]): number[][] {
  // 時計回りに回転
  return matrix[0].map((_, i) => matrix.map(row => row[i]).reverse());
}

function createEmptyBoard(): Board {
  return Array.from({ length: ROWS }, () => Array(COLS).fill(null));
}

function canMove(
  shape: number[][],
  board: Board,
  pos: { x: number; y: number }
): boolean {
  for (let y = 0; y < shape.length; y++) {
    for (let x = 0; x < shape[y].length; x++) {
      if (shape[y][x]) {
        const newY = pos.y + y;
        const newX = pos.x + x;
        if (
          newX < 0 ||
          newX >= COLS ||
          newY >= ROWS ||
          (newY >= 0 && board[newY][newX])
        ) {
          return false;
        }
      }
    }
  }
  return true;
}

function merge(board: Board, shape: number[][], pos: { x: number; y: number }, color: string): Board {
  const newBoard = board.map(row => row.slice());
  for (let y = 0; y < shape.length; y++) {
    for (let x = 0; x < shape[y].length; x++) {
      if (shape[y][x]) {
        const newY = pos.y + y;
        const newX = pos.x + x;
        if (newY >= 0 && newY < ROWS && newX >= 0 && newX < COLS) {
          newBoard[newY][newX] = { color, filled: true };
        }
      }
    }
  }
  return newBoard;
}

function clearLines(board: Board): { board: Board; lines: number } {
  const newBoard = board.filter(row => row.some(cell => !cell));
  const linesCleared = ROWS - newBoard.length;
  while (newBoard.length < ROWS) {
    newBoard.unshift(Array(COLS).fill(null));
  }
  return { board: newBoard, lines: linesCleared };
}

// RPG要素：特定の行を消去するスキル関数
function clearBottomLine(board: Board): Board {
  const newBoard = board.slice(0, -1);
  newBoard.unshift(Array(COLS).fill(null));
  return newBoard;
}

// RPG要素：ランダムブロック消去スキル関数
function removeRandomBlocks(board: Board, count: number = 5): Board {
  const newBoard = board.map(row => [...row]);
  let blocksRemoved = 0;
  
  while (blocksRemoved < count) {
    const y = Math.floor(Math.random() * ROWS);
    const x = Math.floor(Math.random() * COLS);
    
    if (newBoard[y][x]) {
      newBoard[y][x] = null;
      blocksRemoved++;
    }
  }
  
  return newBoard;
}

// RPG要素：経験値からレベルを計算
function calculateLevel(exp: number): number {
  for (let i = EXP_PER_LEVEL.length - 1; i >= 0; i--) {
    if (exp >= EXP_PER_LEVEL[i]) {
      return i + 1;
    }
  }
  return 1; // 最低レベル
}

const TetrisRPGGame: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [board, setBoard] = useState<Board>(createEmptyBoard());
  const [current, setCurrent] = useState(() => {
    const tet = randomTetromino();
    return { shape: tet.shape, color: tet.color, pos: { x: 3, y: -2 } };
  });
  const [next, setNext] = useState(randomTetromino());
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [tick, setTick] = useState(0);
  
  // RPG要素
  const [exp, setExp] = useState(0);
  const [level, setLevel] = useState(1);
  const [fallSpeed, setFallSpeed] = useState(TICK_INTERVAL);
  const [skills, setSkills] = useState<SkillState[]>(
    SKILLS.map(skill => ({
      id: skill.id,
      cooldown: 0,
      active: false,
      duration: 0
    }))
  );
  const [isMobile, setIsMobile] = useState(false);

  // モバイル判定
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // RPG要素：スキル効果の時間管理
  useEffect(() => {
    const skillTimer = setInterval(() => {
      setSkills(prevSkills => {
        return prevSkills.map(skill => {
          // クールダウン減少
          if (skill.cooldown > 0) {
            return { ...skill, cooldown: skill.cooldown - 1 };
          }
          
          // アクティブなスキルの持続時間減少
          if (skill.active && skill.duration) {
            const newDuration = skill.duration - 1;
            if (newDuration <= 0) {
              // スキル効果終了時の処理
              if (skill.id === "slowDown") {
                // スロー効果終了、通常速度に戻す
                setFallSpeed(TICK_INTERVAL - (level - 1) * 30);
              }
              return { ...skill, active: false, duration: 0 };
            }
            return { ...skill, duration: newDuration };
          }
          
          return skill;
        });
      });
    }, 1000); // 1秒ごとに更新
    
    return () => clearInterval(skillTimer);
  }, [level]);

  // 描画
  useEffect(() => {
    const ctx = canvasRef.current?.getContext("2d");
    if (!ctx) return;
    ctx.clearRect(0, 0, COLS * BLOCK_SIZE, ROWS * BLOCK_SIZE);
    // 盤面
    for (let y = 0; y < ROWS; y++) {
      for (let x = 0; x < COLS; x++) {
        const cell = board[y][x];
        if (cell) {
          ctx.fillStyle = cell.color;
          ctx.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
          ctx.strokeStyle = "#222";
          ctx.strokeRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        }
      }
    }
    // 現在のテトロミノ
    const { shape, color, pos } = current;
    for (let y = 0; y < shape.length; y++) {
      for (let x = 0; x < shape[y].length; x++) {
        if (shape[y][x]) {
          const drawY = pos.y + y;
          const drawX = pos.x + x;
          if (drawY >= 0) {
            ctx.fillStyle = color;
            ctx.fillRect(drawX * BLOCK_SIZE, drawY * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
            ctx.strokeStyle = "#222";
            ctx.strokeRect(drawX * BLOCK_SIZE, drawY * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
          }
        }
      }
    }
  }, [board, current, tick]);

  // レベル更新
  useEffect(() => {
    const newLevel = calculateLevel(exp);
    if (newLevel !== level) {
      setLevel(newLevel);
      // レベルアップ時に落下速度を少し速くする
      setFallSpeed(prev => {
        // スローダウンスキルがアクティブでなければ速度アップ
        const isSlowActive = skills.find(s => s.id === "slowDown")?.active;
        if (!isSlowActive) {
          return Math.max(TICK_INTERVAL - (newLevel - 1) * 30, 150); // 最低150msまで
        }
        return prev;
      });
    }
  }, [exp, level, skills]);

  // ゲームループ
  useEffect(() => {
    if (gameOver) return;
    const interval = setInterval(() => {
      setTick(t => t + 1);
    }, fallSpeed);
    return () => clearInterval(interval);
  }, [gameOver, fallSpeed]);

  // テトロミノ落下
  useEffect(() => {
    if (gameOver) return;
    const { shape, color, pos } = current;
    const nextPos = { x: pos.x, y: pos.y + 1 };
    if (canMove(shape, board, nextPos)) {
      setCurrent(cur => ({ ...cur, pos: nextPos }));
    } else {
      // 着地
      const merged = merge(board, shape, pos, color);
      const { board: cleared, lines } = clearLines(merged);
      setBoard(cleared);
      
      // RPG要素：スコアと経験値を加算
      const lineScore = lines * 100;
      const lineExp = lines * 20;
      setScore(s => s + lineScore);
      setExp(e => e + lineExp);
      
      // 新しいテトロミノ
      const nextTet = next;
      const startPos = { x: 3, y: -2 };
      if (!canMove(nextTet.shape, cleared, startPos)) {
        setGameOver(true);
      } else {
        setCurrent({ shape: nextTet.shape, color: nextTet.color, pos: startPos });
        setNext(randomTetromino());
      }
    }
    // eslint-disable-next-line
  }, [tick]);

  // キー操作
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (gameOver) return;
      let moved = false;
      if (e.key === "ArrowLeft") {
        const nextPos = { x: current.pos.x - 1, y: current.pos.y };
        if (canMove(current.shape, board, nextPos)) {
          setCurrent(cur => ({ ...cur, pos: nextPos }));
          moved = true;
        }
      } else if (e.key === "ArrowRight") {
        const nextPos = { x: current.pos.x + 1, y: current.pos.y };
        if (canMove(current.shape, board, nextPos)) {
          setCurrent(cur => ({ ...cur, pos: nextPos }));
          moved = true;
        }
      } else if (e.key === "ArrowDown") {
        const nextPos = { x: current.pos.x, y: current.pos.y + 1 };
        if (canMove(current.shape, board, nextPos)) {
          setCurrent(cur => ({ ...cur, pos: nextPos }));
          moved = true;
        }
      } else if (e.key === "ArrowUp") {
        const rotated = rotate(current.shape);
        if (canMove(rotated, board, current.pos)) {
          setCurrent(cur => ({ ...cur, shape: rotated }));
          moved = true;
        }
      } else if (e.key === " ") {
        // ハードドロップ
        let dropY = current.pos.y;
        while (canMove(current.shape, board, { x: current.pos.x, y: dropY + 1 })) {
          dropY++;
        }
        setCurrent(cur => ({ ...cur, pos: { x: current.pos.x, y: dropY } }));
      } else if (e.key === "1" || e.key === "2" || e.key === "3" || e.key === "4") {
        // スキル発動
        const skillIndex = parseInt(e.key) - 1;
        if (skillIndex >= 0 && skillIndex < SKILLS.length) {
          activateSkill(SKILLS[skillIndex].id);
        }
      }
      if (moved) {
        e.preventDefault();
      }
    },
    [current, board, gameOver]
  );

  // RPG要素：スキル発動関数
  const activateSkill = (skillId: string) => {
    const skillData = SKILLS.find(s => s.id === skillId);
    const skillState = skills.find(s => s.id === skillId);
    
    // スキルがクールダウン中でないか、十分なレベルに達していない場合は発動しない
    if (!skillData || !skillState || skillState.cooldown > 0 || level < skillData.level) {
      return;
    }
    
    // スキル発動処理
    setSkills(prevSkills => 
      prevSkills.map(s => {
        if (s.id === skillId) {
          return {
            ...s,
            active: true,
            cooldown: skillData.cooldown,
            duration: s.id === "slowDown" ? 10 : 0 // スローダウンは10秒間持続
          };
        }
        return s;
      })
    );
    
    // スキル効果の適用
    switch (skillId) {
      case "clearLine":
        setBoard(clearBottomLine);
        break;
      case "slowDown":
        setFallSpeed(TICK_INTERVAL * 2); // 落下速度を半分に
        break;
      case "changeBlock":
        const newTet = randomTetromino();
        setCurrent(cur => ({
          ...cur,
          shape: newTet.shape,
          color: newTet.color
        }));
        break;
      case "removeBlocks":
        setBoard(board => removeRandomBlocks(board));
        break;
    }
    
    // スキル使用でも少し経験値獲得
    setExp(e => e + 5);
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  // モバイル操作用の関数
  const moveLeft = () => {
    if (gameOver) return;
    const nextPos = { x: current.pos.x - 1, y: current.pos.y };
    if (canMove(current.shape, board, nextPos)) {
      setCurrent(cur => ({ ...cur, pos: nextPos }));
    }
  };

  const moveRight = () => {
    if (gameOver) return;
    const nextPos = { x: current.pos.x + 1, y: current.pos.y };
    if (canMove(current.shape, board, nextPos)) {
      setCurrent(cur => ({ ...cur, pos: nextPos }));
    }
  };

  const rotatePiece = () => {
    if (gameOver) return;
    const rotated = rotate(current.shape);
    if (canMove(rotated, board, current.pos)) {
      setCurrent(cur => ({ ...cur, shape: rotated }));
    }
  };

  const hardDrop = () => {
    if (gameOver) return;
    let dropY = current.pos.y;
    while (canMove(current.shape, board, { x: current.pos.x, y: dropY + 1 })) {
      dropY++;
    }
    setCurrent(cur => ({ ...cur, pos: { x: current.pos.x, y: dropY } }));
  };

  const handleRestart = () => {
    setBoard(createEmptyBoard());
    const tet = randomTetromino();
    setCurrent({ shape: tet.shape, color: tet.color, pos: { x: 3, y: -2 } });
    setNext(randomTetromino());
    setScore(0);
    setExp(0);
    setLevel(1);
    setFallSpeed(TICK_INTERVAL);
    setSkills(SKILLS.map(skill => ({
      id: skill.id,
      cooldown: 0,
      active: false,
      duration: 0
    })));
    setGameOver(false);
    setTick(0);
  };

  // 次のテトロミノ表示
  const renderNext = () => {
    return (
      <div style={{ margin: "8px 0" }}>
        <div>Next:</div>
        <div style={{ display: "inline-block", background: "#222", padding: 4 }}>
          {next.shape.map((row, y) => (
            <div key={y} style={{ height: 12 }}>
              {row.map((cell, x) => (
                <span
                  key={x}
                  style={{
                    display: "inline-block",
                    width: 12,
                    height: 12,
                    background: cell ? next.color : "#222",
                    border: cell ? "1px solid #444" : "none",
                  }}
                />
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // RPG要素：スキルボタン表示
  const renderSkills = () => {
    return (
      <div style={{ marginTop: 16 }}>
        <div>スキル:</div>
        <div style={{ display: "flex", gap: 8, flexWrap: "wrap", marginTop: 4 }}>
          {SKILLS.map((skill, index) => {
            const skillState = skills.find(s => s.id === skill.id);
            const isAvailable = level >= skill.level;
            const isOnCooldown = Boolean(skillState?.cooldown && skillState?.cooldown > 0);
            
            return (
              <button
                key={skill.id}
                onClick={() => activateSkill(skill.id)}
                disabled={!isAvailable || isOnCooldown}
                title={`${skill.name}: ${skill.description} (Lv${skill.level}〜)`}
                style={{
                  width: 36,
                  height: 36,
                  fontSize: 16,
                  opacity: isAvailable ? (isOnCooldown ? 0.5 : 1) : 0.3,
                  position: "relative",
                  backgroundColor: isAvailable ? (isOnCooldown ? "#333" : "#555") : "#222",
                  color: "white",
                  border: "none",
                  borderRadius: 4,
                  cursor: isAvailable && !isOnCooldown ? "pointer" : "not-allowed"
                }}
              >
                {skill.icon}
                {isOnCooldown && (
                  <div style={{
                    position: "absolute",
                    bottom: 0,
                    right: 0,
                    fontSize: 10,
                    backgroundColor: "rgba(0,0,0,0.7)",
                    padding: "0 2px"
                  }}>
                    {skillState?.cooldown}
                  </div>
                )}
                <div style={{ 
                  fontSize: 9, 
                  position: "absolute", 
                  top: -2, 
                  right: -2, 
                  backgroundColor: "#f00", 
                  borderRadius: 8,
                  width: 16,
                  height: 16,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  visibility: isAvailable ? "visible" : "hidden"
                }}>
                  {index + 1}
                </div>
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  // RPG要素：経験値バー表示
  const renderExpBar = () => {
    const currentLevelExp = level > 1 ? EXP_PER_LEVEL[level - 2] : 0;
    const nextLevelExp = level < EXP_PER_LEVEL.length ? EXP_PER_LEVEL[level - 1] : EXP_PER_LEVEL[EXP_PER_LEVEL.length - 1];
    const expForNextLevel = nextLevelExp - currentLevelExp;
    const currentExp = exp - currentLevelExp;
    const expPercentage = Math.min(100, (currentExp / expForNextLevel) * 100);
    
    return (
      <div style={{ marginTop: 8 }}>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <div>Level: {level}</div>
          <div>Exp: {exp}</div>
        </div>
        <div style={{ 
          width: "100%", 
          height: 8, 
          backgroundColor: "#333", 
          borderRadius: 4,
          overflow: "hidden",
          marginTop: 4
        }}>
          <div style={{ 
            width: `${expPercentage}%`, 
            height: "100%", 
            backgroundColor: "#5f9", 
            transition: "width 0.3s" 
          }} />
        </div>
      </div>
    );
  };

  // モバイル用操作ボタン
  const MobileControls = () => {
    if (!isMobile) return null;

    const buttonStyle = {
      width: "60px",
      height: "60px",
      backgroundColor: "#555",
      color: "white",
      border: "2px solid #777",
      borderRadius: "8px",
      fontSize: "20px",
      fontWeight: "bold",
      cursor: "pointer",
      userSelect: "none" as const,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      boxShadow: "0 2px 8px rgba(0,0,0,0.3)"
    };

    const skillButtonStyle = {
      width: "50px",
      height: "50px",
      backgroundColor: "#333",
      color: "white",
      border: "2px solid #555",
      borderRadius: "6px",
      fontSize: "16px",
      cursor: "pointer",
      userSelect: "none" as const,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      position: "relative" as const
    };

    return (
      <div style={{
        position: "fixed",
        bottom: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        zIndex: 1000,
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        alignItems: "center"
      }}>
        {/* スキルボタン */}
        <div style={{ display: "flex", gap: "8px" }}>
          {SKILLS.map((skill, index) => {
            const skillState = skills.find(s => s.id === skill.id);
            const isAvailable = level >= skill.level;
            const isOnCooldown = Boolean(skillState?.cooldown && skillState?.cooldown > 0);
            
            return (
              <button
                key={skill.id}
                onTouchStart={(e) => {
                  e.preventDefault();
                  if (isAvailable && !isOnCooldown) {
                    activateSkill(skill.id);
                  }
                }}
                style={{
                  ...skillButtonStyle,
                  opacity: isAvailable ? (isOnCooldown ? 0.5 : 1) : 0.3,
                  backgroundColor: isAvailable ? (isOnCooldown ? "#333" : "#555") : "#222"
                }}
              >
                {skill.icon}
                {isOnCooldown && (
                  <div style={{
                    position: "absolute",
                    bottom: 0,
                    right: 0,
                    fontSize: 10,
                    backgroundColor: "rgba(0,0,0,0.7)",
                    padding: "0 2px"
                  }}>
                    {skillState?.cooldown}
                  </div>
                )}
              </button>
            );
          })}
        </div>

        {/* 移動・回転ボタン */}
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <button
            onTouchStart={(e) => {
              e.preventDefault();
              moveLeft();
            }}
            style={buttonStyle}
          >
            ←
          </button>
          
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            <button
              onTouchStart={(e) => {
                e.preventDefault();
                rotatePiece();
              }}
              style={buttonStyle}
            >
              ↻
            </button>
            <button
              onTouchStart={(e) => {
                e.preventDefault();
                hardDrop();
              }}
              style={buttonStyle}
            >
              ↓
            </button>
          </div>
          
          <button
            onTouchStart={(e) => {
              e.preventDefault();
              moveRight();
            }}
            style={buttonStyle}
          >
            →
          </button>
        </div>
      </div>
    );
  };

  const containerStyle = {
    display: "flex",
    flexDirection: isMobile ? "column" : "row" as "column" | "row",
    alignItems: isMobile ? "center" : "flex-start",
    gap: isMobile ? "16px" : "24px",
    width: "100%",
    maxWidth: isMobile ? "100%" : "none",
    padding: isMobile ? "0 16px" : "0"
  };

  const canvasStyle = {
    background: "#222",
    border: "2px solid #444",
    maxWidth: "100%",
    height: "auto",
    ...(isMobile && {
      width: "100%",
      maxWidth: "300px"
    })
  };

  const sidebarStyle = {
    color: "#fff",
    minWidth: isMobile ? "100%" : "180px",
    maxWidth: isMobile ? "300px" : "none",
    textAlign: isMobile ? "center" : "left" as "center" | "left"
  };

  return (
    <>
      <div style={containerStyle}>
        <div style={{ display: "flex", justifyContent: "center" }}>
          <canvas
            ref={canvasRef}
            width={COLS * BLOCK_SIZE}
            height={ROWS * BLOCK_SIZE}
            style={canvasStyle}
          />
        </div>
        <div style={sidebarStyle}>
          <h2 style={{ margin: 0 }}>
            Tetris RPG
            <div style={{ 
              fontSize: 12, 
              fontWeight: "normal", 
              backgroundColor: "#5500ff", 
              display: "inline-block",
              padding: "2px 6px",
              borderRadius: 4,
              marginLeft: 8
            }}>
              Powered by Claude 3.7
            </div>
          </h2>
          <div>Score: {score}</div>
          {renderExpBar()}
          {renderNext()}
          {!isMobile && renderSkills()}
          {gameOver && (
            <div style={{ marginTop: 16 }}>
              <div style={{ color: "#f00" }}>Game Over</div>
              <button onClick={handleRestart}>Restart</button>
            </div>
          )}
          <div style={{ marginTop: 16, fontSize: 12, color: "#aaa" }}>
            <div>操作方法:</div>
            {isMobile ? (
              <>
                <div>画面下部のボタンで操作</div>
                <div>←→: 移動</div>
                <div>↻: 回転</div>
                <div>↓: ハードドロップ</div>
                <div>スキルボタン: スキル発動</div>
              </>
            ) : (
              <>
                <div>←→: 移動</div>
                <div>↑: 回転</div>
                <div>↓: ソフトドロップ</div>
                <div>Space: ハードドロップ</div>
                <div>1-4: スキル発動</div>
              </>
            )}
          </div>
        </div>
      </div>
      <MobileControls />
    </>
  );
};

export default TetrisRPGGame; 