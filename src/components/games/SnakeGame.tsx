'use client';

/**
 * Snake Game Component
 * Created using Claude-4
 * Features: Canvas API, React hooks, TypeScript, localStorage
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';

// 型定義
type Position = {
  x: number;
  y: number;
};

type Direction = 'UP' | 'DOWN' | 'LEFT' | 'RIGHT';
type GameState = 'WAITING' | 'PLAYING' | 'PAUSED' | 'GAME_OVER';

// ゲーム設定
const GAME_CONFIG = {
  BOARD_SIZE: 20,
  CELL_SIZE: 20,
  INITIAL_SPEED: 200,
  MIN_SPEED: 100,
  SPEED_INCREMENT: 10,
  POINTS_PER_FOOD: 10,
} as const;

const COLORS = {
  BOARD: '#f0f4f0',
  GRID: '#e0e8e0',
  SNAKE_HEAD: '#22c55e',
  SNAKE_BODY: '#16a34a',
  FOOD: '#ef4444',
} as const;

export default function SnakeGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameLoopRef = useRef<NodeJS.Timeout | null>(null);

  // ゲーム状態
  const [gameState, setGameState] = useState<GameState>('WAITING');
  const [snake, setSnake] = useState<Position[]>([
    { x: 10, y: 10 },
    { x: 9, y: 10 },
    { x: 8, y: 10 },
  ]);
  const [food, setFood] = useState<Position>({ x: 15, y: 15 });
  const [direction, setDirection] = useState<Direction>('RIGHT');
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(0);
  const [speed, setSpeed] = useState<number>(GAME_CONFIG.INITIAL_SPEED);

  // localStorage からハイスコアを読み込み
  useEffect(() => {
    const savedHighScore = localStorage.getItem('snake-high-score');
    if (savedHighScore) {
      setHighScore(parseInt(savedHighScore, 10));
    }
  }, []);

  // ハイスコアをlocalStorageに保存
  const updateHighScore = useCallback((newScore: number) => {
    if (newScore > highScore) {
      setHighScore(newScore);
      localStorage.setItem('snake-high-score', newScore.toString());
    }
  }, [highScore]);

  // ランダムな餌の位置を生成
  const generateFood = useCallback((currentSnake: Position[]): Position => {
    let newFood: Position;
    do {
      newFood = {
        x: Math.floor(Math.random() * GAME_CONFIG.BOARD_SIZE),
        y: Math.floor(Math.random() * GAME_CONFIG.BOARD_SIZE),
      };
    } while (currentSnake.some(segment => segment.x === newFood.x && segment.y === newFood.y));
    return newFood;
  }, []);

  // 衝突判定
  const checkCollision = useCallback((head: Position, body: Position[]): boolean => {
    // 壁との衝突
    if (head.x < 0 || head.x >= GAME_CONFIG.BOARD_SIZE || 
        head.y < 0 || head.y >= GAME_CONFIG.BOARD_SIZE) {
      return true;
    }
    // 自分の体との衝突
    return body.some(segment => segment.x === head.x && segment.y === head.y);
  }, []);

  // 次の位置を計算
  const getNextPosition = useCallback((currentHead: Position, currentDirection: Direction): Position => {
    const moves = {
      UP: { x: 0, y: -1 },
      DOWN: { x: 0, y: 1 },
      LEFT: { x: -1, y: 0 },
      RIGHT: { x: 1, y: 0 },
    };
    const move = moves[currentDirection];
    return {
      x: currentHead.x + move.x,
      y: currentHead.y + move.y,
    };
  }, []);

  // ゲームループ
  const gameLoop = useCallback(() => {
    setSnake(currentSnake => {
      const head = currentSnake[0];
      const newHead = getNextPosition(head, direction);

      // 衝突判定
      if (checkCollision(newHead, currentSnake)) {
        setGameState('GAME_OVER');
        updateHighScore(score);
        return currentSnake;
      }

      const newSnake = [newHead, ...currentSnake];

      // 餌を食べたかチェック
      if (newHead.x === food.x && newHead.y === food.y) {
        setScore(prevScore => {
          const newScore = prevScore + GAME_CONFIG.POINTS_PER_FOOD;
          // スピードアップ
          const newSpeed = Math.max(
            GAME_CONFIG.MIN_SPEED,
            GAME_CONFIG.INITIAL_SPEED - Math.floor(newScore / GAME_CONFIG.POINTS_PER_FOOD) * GAME_CONFIG.SPEED_INCREMENT
          );
          setSpeed(newSpeed);
          return newScore;
        });
        setFood(generateFood(newSnake));
        return newSnake;
      } else {
        // 尻尾を削除
        return newSnake.slice(0, -1);
      }
    });
  }, [direction, food, score, checkCollision, getNextPosition, generateFood, updateHighScore]);

  // キーボード操作
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    const { key } = event;
    
    if (key === 'Enter') {
      if (gameState === 'WAITING' || gameState === 'GAME_OVER') {
        startGame();
      }
      return;
    }

    if (key === ' ') {
      event.preventDefault();
      if (gameState === 'PLAYING') {
        setGameState('PAUSED');
      } else if (gameState === 'PAUSED') {
        setGameState('PLAYING');
      }
      return;
    }

    if (gameState !== 'PLAYING') return;

    const directionMap: { [key: string]: Direction } = {
      ArrowUp: 'UP',
      ArrowDown: 'DOWN',
      ArrowLeft: 'LEFT',
      ArrowRight: 'RIGHT',
    };

    const newDirection = directionMap[key];
    if (newDirection) {
      // 反対方向への移動を防ぐ
      const opposites = {
        UP: 'DOWN',
        DOWN: 'UP',
        LEFT: 'RIGHT',
        RIGHT: 'LEFT',
      };
      if (opposites[direction] !== newDirection) {
        setDirection(newDirection);
      }
    }
  }, [gameState, direction]);

  // ゲーム開始
  const startGame = useCallback(() => {
    setSnake([
      { x: 10, y: 10 },
      { x: 9, y: 10 },
      { x: 8, y: 10 },
    ]);
    setFood(generateFood([{ x: 10, y: 10 }, { x: 9, y: 10 }, { x: 8, y: 10 }]));
    setDirection('RIGHT');
    setScore(0);
    setSpeed(GAME_CONFIG.INITIAL_SPEED);
    setGameState('PLAYING');
  }, [generateFood]);

  // モバイル操作
  const handleDirectionButton = useCallback((newDirection: Direction) => {
    if (gameState === 'PLAYING') {
      const opposites = {
        UP: 'DOWN',
        DOWN: 'UP',
        LEFT: 'RIGHT',
        RIGHT: 'LEFT',
      };
      if (opposites[direction] !== newDirection) {
        setDirection(newDirection);
      }
    }
  }, [gameState, direction]);

  // イベントリスナーの設定
  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // ゲームループの管理
  useEffect(() => {
    if (gameState === 'PLAYING') {
      gameLoopRef.current = setInterval(gameLoop, speed);
    } else {
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    }

    return () => {
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }
    };
  }, [gameState, gameLoop, speed]);

  // Canvas描画
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // キャンバスクリア
    ctx.fillStyle = COLORS.BOARD;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // グリッド描画
    ctx.strokeStyle = COLORS.GRID;
    ctx.lineWidth = 1;
    for (let i = 0; i <= GAME_CONFIG.BOARD_SIZE; i++) {
      ctx.beginPath();
      ctx.moveTo(i * GAME_CONFIG.CELL_SIZE, 0);
      ctx.lineTo(i * GAME_CONFIG.CELL_SIZE, canvas.height);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, i * GAME_CONFIG.CELL_SIZE);
      ctx.lineTo(canvas.width, i * GAME_CONFIG.CELL_SIZE);
      ctx.stroke();
    }

    // ヘビ描画
    snake.forEach((segment, index) => {
      ctx.fillStyle = index === 0 ? COLORS.SNAKE_HEAD : COLORS.SNAKE_BODY;
      ctx.fillRect(
        segment.x * GAME_CONFIG.CELL_SIZE + 1,
        segment.y * GAME_CONFIG.CELL_SIZE + 1,
        GAME_CONFIG.CELL_SIZE - 2,
        GAME_CONFIG.CELL_SIZE - 2
      );
    });

    // 餌描画
    ctx.fillStyle = COLORS.FOOD;
    ctx.beginPath();
    ctx.arc(
      food.x * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
      food.y * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
      GAME_CONFIG.CELL_SIZE / 2 - 2,
      0,
      2 * Math.PI
    );
    ctx.fill();
  }, [snake, food]);

  // 初期Canvas描画（マウント時）
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 初期描画
    ctx.fillStyle = COLORS.BOARD;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // グリッド描画
    ctx.strokeStyle = COLORS.GRID;
    ctx.lineWidth = 1;
    for (let i = 0; i <= GAME_CONFIG.BOARD_SIZE; i++) {
      ctx.beginPath();
      ctx.moveTo(i * GAME_CONFIG.CELL_SIZE, 0);
      ctx.lineTo(i * GAME_CONFIG.CELL_SIZE, canvas.height);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(0, i * GAME_CONFIG.CELL_SIZE);
      ctx.lineTo(canvas.width, i * GAME_CONFIG.CELL_SIZE);
      ctx.stroke();
    }
  }, []);

  const canvasSize = GAME_CONFIG.BOARD_SIZE * GAME_CONFIG.CELL_SIZE;

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* スコア表示 */}
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">{score}</div>
            <div className="text-sm text-gray-600">スコア</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">{highScore}</div>
            <div className="text-sm text-gray-600">ハイスコア</div>
          </div>
        </div>
        <div className="mt-4 text-center">
          <div className="text-sm text-gray-500">
            速度: {GAME_CONFIG.INITIAL_SPEED - speed + GAME_CONFIG.MIN_SPEED}ms
          </div>
        </div>
      </div>

      {/* ゲームステータス */}
      <div className="text-center">
        {gameState === 'WAITING' && (
          <div className="text-lg text-gray-700">
            <div className="mb-2">🎮 Snake Game</div>
            <div className="text-sm">Enterキーでゲーム開始</div>
          </div>
        )}
        {gameState === 'PLAYING' && (
          <div className="text-lg text-green-600 font-medium">
            プレイ中... (スペースでポーズ)
          </div>
        )}
        {gameState === 'PAUSED' && (
          <div className="text-lg text-yellow-600 font-medium">
            ⏸️ ポーズ中 (スペースで再開)
          </div>
        )}
        {gameState === 'GAME_OVER' && (
          <div className="text-lg text-red-600 font-medium">
            <div className="mb-2">💀 ゲームオーバー</div>
            <div className="text-sm">Enterキーでリスタート</div>
          </div>
        )}
      </div>

      {/* ゲームキャンバス */}
      <div className="relative flex justify-center">
        <canvas
          ref={canvasRef}
          width={canvasSize}
          height={canvasSize}
          className="border-2 border-green-300 rounded-lg shadow-lg bg-white"
          style={{ display: 'block' }}
        />
      </div>

      {/* コントロールボタン */}
      <div className="flex flex-col items-center space-y-4">
        <div className="flex space-x-4">
          <button
            onClick={startGame}
            className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {gameState === 'WAITING' ? 'ゲーム開始' : 'リスタート'}
          </button>
          {gameState === 'PLAYING' || gameState === 'PAUSED' ? (
            <button
              onClick={() => setGameState(gameState === 'PLAYING' ? 'PAUSED' : 'PLAYING')}
              className="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              {gameState === 'PLAYING' ? 'ポーズ' : '再開'}
            </button>
          ) : null}
        </div>

        {/* モバイル操作ボタン */}
        <div className="md:hidden">
          <div className="text-center mb-2">
            <button
              onClick={() => handleDirectionButton('UP')}
              className="bg-blue-500 hover:bg-blue-600 text-white w-12 h-12 rounded-lg font-bold text-xl transition-colors"
            >
              ↑
            </button>
          </div>
          <div className="flex justify-center space-x-2">
            <button
              onClick={() => handleDirectionButton('LEFT')}
              className="bg-blue-500 hover:bg-blue-600 text-white w-12 h-12 rounded-lg font-bold text-xl transition-colors"
            >
              ←
            </button>
            <button
              onClick={() => handleDirectionButton('DOWN')}
              className="bg-blue-500 hover:bg-blue-600 text-white w-12 h-12 rounded-lg font-bold text-xl transition-colors"
            >
              ↓
            </button>
            <button
              onClick={() => handleDirectionButton('RIGHT')}
              className="bg-blue-500 hover:bg-blue-600 text-white w-12 h-12 rounded-lg font-bold text-xl transition-colors"
            >
              →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 