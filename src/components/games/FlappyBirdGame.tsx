"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";

// ゲーム設定
const CANVAS_WIDTH = 400;
const CANVAS_HEIGHT = 600;
const BIRD_SIZE = 20;
const PIPE_WIDTH = 60;
const PIPE_GAP = 150;
const GRAVITY = 0.4; // 0.3 → 0.4 に微調整（適度な落下速度）
const JUMP_FORCE = -7; // -10 → -7 に変更（ジャンプ力を適度に）
const PIPE_SPEED = 2;
const PIPE_SPAWN_INTERVAL = 120; // フレーム数

// 色設定
const COLORS = {
  SKY: "#87CEEB",
  BIRD: "#FFD700",
  PIPE: "#228B22",
  GROUND: "#8B4513",
  TEXT: "#000000"
};

type Bird = {
  x: number;
  y: number;
  velocity: number;
};

type Pipe = {
  x: number;
  topHeight: number;
  bottomY: number;
  passed: boolean;
};

type GameState = "waiting" | "playing" | "gameOver";

const FlappyBirdGame: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const frameCountRef = useRef<number>(0);

  const [gameState, setGameState] = useState<GameState>("waiting");
  const [bird, setBird] = useState<Bird>({
    x: 100,
    y: CANVAS_HEIGHT / 2,
    velocity: 0
  });
  const [pipes, setPipes] = useState<Pipe[]>([]);
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(() => {
    if (typeof window !== "undefined") {
      return parseInt(localStorage.getItem("flappy-bird-high-score") || "0");
    }
    return 0;
  });
  const [isMobile, setIsMobile] = useState(false);

  // モバイル判定
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // パイプ生成
  const createPipe = useCallback((): Pipe => {
    const minHeight = 50;
    const maxHeight = CANVAS_HEIGHT - PIPE_GAP - minHeight - 100; // 地面分を考慮
    const topHeight = Math.random() * (maxHeight - minHeight) + minHeight;
    
    return {
      x: CANVAS_WIDTH,
      topHeight,
      bottomY: topHeight + PIPE_GAP,
      passed: false
    };
  }, []);

  // 衝突判定
  const checkCollision = useCallback((bird: Bird, pipes: Pipe[]): boolean => {
    // 地面との衝突
    if (bird.y + BIRD_SIZE >= CANVAS_HEIGHT - 50) {
      return true;
    }
    
    // 天井との衝突
    if (bird.y <= 0) {
      return true;
    }

    // パイプとの衝突
    for (const pipe of pipes) {
      if (
        bird.x + BIRD_SIZE > pipe.x &&
        bird.x < pipe.x + PIPE_WIDTH
      ) {
        if (
          bird.y < pipe.topHeight ||
          bird.y + BIRD_SIZE > pipe.bottomY
        ) {
          return true;
        }
      }
    }

    return false;
  }, []);

  // ジャンプ
  const jump = useCallback(() => {
    if (gameState === "waiting") {
      setGameState("playing");
    }
    
    if (gameState === "playing") {
      setBird(prev => ({
        ...prev,
        velocity: JUMP_FORCE
      }));
    }
  }, [gameState]);

  // ゲームリスタート
  const restartGame = useCallback(() => {
    setBird({
      x: 100,
      y: CANVAS_HEIGHT / 2,
      velocity: 0
    });
    setPipes([]);
    setScore(0);
    setGameState("waiting");
    frameCountRef.current = 0;
  }, []);

  // キーボード操作
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === "Space") {
        e.preventDefault();
        if (gameState === "gameOver") {
          restartGame();
        } else {
          jump();
        }
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [gameState, jump, restartGame]);

  // ゲームループ
  const gameLoop = useCallback(() => {
    if (gameState !== "playing") return;

    frameCountRef.current++;

    // 鳥の更新
    setBird(prev => {
      const newBird = {
        ...prev,
        velocity: prev.velocity + GRAVITY,
        y: prev.y + prev.velocity + GRAVITY
      };
      return newBird;
    });

    // パイプの更新
    setPipes(prev => {
      let newPipes = [...prev];

      // パイプの移動
      newPipes = newPipes.map(pipe => ({
        ...pipe,
        x: pipe.x - PIPE_SPEED
      }));

      // 画面外のパイプを削除
      newPipes = newPipes.filter(pipe => pipe.x + PIPE_WIDTH > 0);

      // 新しいパイプの生成
      if (frameCountRef.current % PIPE_SPAWN_INTERVAL === 0) {
        newPipes.push(createPipe());
      }

      return newPipes;
    });

    // スコア更新
    setPipes(prev => {
      const newPipes = prev.map(pipe => {
        if (!pipe.passed && pipe.x + PIPE_WIDTH < bird.x) {
          setScore(s => s + 1);
          return { ...pipe, passed: true };
        }
        return pipe;
      });
      return newPipes;
    });

    // 衝突判定
    setBird(currentBird => {
      setPipes(currentPipes => {
        if (checkCollision(currentBird, currentPipes)) {
          setGameState("gameOver");
          // ハイスコア更新
          if (score > highScore) {
            setHighScore(score);
            if (typeof window !== "undefined") {
              localStorage.setItem("flappy-bird-high-score", score.toString());
            }
          }
        }
        return currentPipes;
      });
      return currentBird;
    });
  }, [gameState, bird.x, score, highScore, checkCollision, createPipe]);

  // 描画
  const draw = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 背景
    ctx.fillStyle = COLORS.SKY;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // 地面
    ctx.fillStyle = COLORS.GROUND;
    ctx.fillRect(0, CANVAS_HEIGHT - 50, CANVAS_WIDTH, 50);

    // パイプ
    ctx.fillStyle = COLORS.PIPE;
    pipes.forEach(pipe => {
      // 上のパイプ
      ctx.fillRect(pipe.x, 0, PIPE_WIDTH, pipe.topHeight);
      // 下のパイプ
      ctx.fillRect(pipe.x, pipe.bottomY, PIPE_WIDTH, CANVAS_HEIGHT - pipe.bottomY - 50);
      
      // パイプの境界線
      ctx.strokeStyle = "#000";
      ctx.lineWidth = 2;
      ctx.strokeRect(pipe.x, 0, PIPE_WIDTH, pipe.topHeight);
      ctx.strokeRect(pipe.x, pipe.bottomY, PIPE_WIDTH, CANVAS_HEIGHT - pipe.bottomY - 50);
    });

    // 鳥
    ctx.fillStyle = COLORS.BIRD;
    ctx.fillRect(bird.x, bird.y, BIRD_SIZE, BIRD_SIZE);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 2;
    ctx.strokeRect(bird.x, bird.y, BIRD_SIZE, BIRD_SIZE);

    // スコア
    ctx.fillStyle = COLORS.TEXT;
    ctx.font = "24px Arial";
    ctx.textAlign = "center";
    ctx.fillText(`Score: ${score}`, CANVAS_WIDTH / 2, 40);

    // ゲーム状態別のメッセージ
    if (gameState === "waiting") {
      ctx.font = "20px Arial";
      ctx.fillText(isMobile ? "タップでスタート" : "スペースキーでスタート", CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 50);
    } else if (gameState === "gameOver") {
      ctx.font = "32px Arial";
      ctx.fillStyle = "#FF0000";
      ctx.fillText("GAME OVER", CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2);
      ctx.font = "16px Arial";
      ctx.fillStyle = COLORS.TEXT;
      ctx.fillText(`High Score: ${highScore}`, CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 30);
      ctx.fillText(isMobile ? "タップでリスタート" : "スペースキーでリスタート", CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2 + 50);
    }
  }, [bird, pipes, score, highScore, gameState, isMobile]);

  // アニメーションループ
  useEffect(() => {
    const animate = () => {
      gameLoop();
      draw();
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameLoop, draw]);

  // モバイル用操作ボタン
  const MobileControls = () => {
    if (!isMobile) return null;

    return (
      <div style={{
        position: "fixed",
        bottom: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        zIndex: 1000,
        display: "flex",
        gap: "16px"
      }}>
        <button
          onTouchStart={(e) => {
            e.preventDefault();
            if (gameState === "gameOver") {
              restartGame();
            } else {
              jump();
            }
          }}
          style={{
            width: "80px",
            height: "80px",
            borderRadius: "50%",
            backgroundColor: gameState === "gameOver" ? "#0066cc" : "#00aa00",
            color: "white",
            border: "none",
            fontSize: "24px",
            fontWeight: "bold",
            boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
            cursor: "pointer",
            userSelect: "none",
            display: "flex",
            alignItems: "center",
            justifyContent: "center"
          }}
        >
          {gameState === "gameOver" ? "🔄" : "🐦"}
        </button>
      </div>
    );
  };

  const canvasStyle = {
    background: COLORS.SKY,
    border: "2px solid #444",
    cursor: "pointer",
    maxWidth: "100%",
    height: "auto",
    ...(isMobile && {
      width: "100%",
      maxWidth: "350px"
    })
  };

  const containerStyle = {
    display: "flex",
    flexDirection: isMobile ? "column" : "row" as "column" | "row",
    alignItems: isMobile ? "center" : "flex-start",
    gap: isMobile ? "16px" : "24px",
    width: "100%",
    maxWidth: isMobile ? "100%" : "none"
  };

  const sidebarStyle = {
    color: "#fff",
    minWidth: isMobile ? "100%" : "180px",
    maxWidth: isMobile ? "350px" : "none",
    padding: isMobile ? "0 16px" : "0"
  };

  return (
    <>
      <div style={containerStyle}>
        <div style={{ display: "flex", justifyContent: "center" }}>
          <canvas
            ref={canvasRef}
            width={CANVAS_WIDTH}
            height={CANVAS_HEIGHT}
            style={canvasStyle}
            onClick={gameState === "gameOver" ? restartGame : jump}
          />
        </div>
        <div style={sidebarStyle}>
          <h2 style={{ margin: 0, textAlign: isMobile ? "center" : "left" }}>
            Flappy Bird
            <div style={{ 
              fontSize: 12, 
              fontWeight: "normal", 
              backgroundColor: "#0066cc", 
              display: "inline-block",
              padding: "2px 6px",
              borderRadius: 4,
              marginLeft: 8
            }}>
              Powered by Claude
            </div>
          </h2>
          <div style={{ textAlign: isMobile ? "center" : "left" }}>Score: {score}</div>
          <div style={{ textAlign: isMobile ? "center" : "left" }}>High Score: {highScore}</div>
          
          <div style={{ marginTop: 16 }}>
            <h3 style={{ margin: "0 0 8px 0", textAlign: isMobile ? "center" : "left" }}>ゲーム状態</h3>
            <div style={{ 
              padding: 8, 
              backgroundColor: 
                gameState === "waiting" ? "#666" :
                gameState === "playing" ? "#0a0" :
                "#a00",
              borderRadius: 4,
              textAlign: "center"
            }}>
              {gameState === "waiting" && "待機中"}
              {gameState === "playing" && "プレイ中"}
              {gameState === "gameOver" && "ゲームオーバー"}
            </div>
          </div>

          {!isMobile && (
            <div style={{ marginTop: 16 }}>
              <button 
                onClick={gameState === "gameOver" ? restartGame : jump}
                style={{
                  width: "100%",
                  padding: "12px",
                  fontSize: "16px",
                  backgroundColor: gameState === "gameOver" ? "#0066cc" : "#00aa00",
                  color: "white",
                  border: "none",
                  borderRadius: 4,
                  cursor: "pointer"
                }}
              >
                {gameState === "gameOver" ? "リスタート" : "ジャンプ"}
              </button>
            </div>
          )}

          <div style={{ marginTop: 16, fontSize: 12, color: "#aaa", textAlign: isMobile ? "center" : "left" }}>
            <div>操作方法:</div>
            {isMobile ? (
              <>
                <div>画面タップ: ジャンプ</div>
                <div>下部ボタン: ジャンプ/リスタート</div>
              </>
            ) : (
              <>
                <div>スペースキー: ジャンプ</div>
                <div>クリック: ジャンプ</div>
                <div>ゲームオーバー時:</div>
                <div>スペースキー/クリック: リスタート</div>
              </>
            )}
          </div>

          <div style={{ marginTop: 16, fontSize: 12, color: "#aaa", textAlign: isMobile ? "center" : "left" }}>
            <div>ゲームルール:</div>
            <div>• パイプの隙間を通り抜ける</div>
            <div>• パイプや地面に触れるとゲームオーバー</div>
            <div>• 通過するたびにスコア+1</div>
            <div>• ハイスコアは自動保存</div>
          </div>
        </div>
      </div>
      <MobileControls />
    </>
  );
};

export default FlappyBirdGame; 