'use client';

/**
 * 2048 Game Component
 * Created using Claude-4
 * Features: Canvas API, React hooks, TypeScript, smooth animations
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';

// 型定義
type Tile = {
  id: number;
  value: number;
  row: number;
  col: number;
  isNew?: boolean;
  isMerged?: boolean;
};

type Direction = 'up' | 'down' | 'left' | 'right';
type GameState = 'playing' | 'won' | 'lost';

// ゲーム設定（動的に変更可能にする）
const getGameConfig = (isMobile: boolean) => ({
  GRID_SIZE: 4,
  CELL_SIZE: isMobile ? 70 : 100,
  CELL_GAP: isMobile ? 8 : 10,
  ANIMATION_DURATION: 150,
  NEW_TILE_ANIMATION_DURATION: 200,
} as const);

// 色設定
const TILE_COLORS = {
  2: { bg: '#eee4da', text: '#776e65' },
  4: { bg: '#ede0c8', text: '#776e65' },
  8: { bg: '#f2b179', text: '#f9f6f2' },
  16: { bg: '#f59563', text: '#f9f6f2' },
  32: { bg: '#f67c5f', text: '#f9f6f2' },
  64: { bg: '#f65e3b', text: '#f9f6f2' },
  128: { bg: '#edcf72', text: '#f9f6f2' },
  256: { bg: '#edcc61', text: '#f9f6f2' },
  512: { bg: '#edc850', text: '#f9f6f2' },
  1024: { bg: '#edc53f', text: '#f9f6f2' },
  2048: { bg: '#edc22e', text: '#f9f6f2' },
} as const;

const BACKGROUND_COLOR = '#bbada0';
const CELL_COLOR = '#cdc1b4';

export default function Game2048() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [tiles, setTiles] = useState<Tile[]>([]);
  const [score, setScore] = useState(0);
  const [bestScore, setBestScore] = useState(0);
  const [gameState, setGameState] = useState<GameState>('playing');
  const [moveCount, setMoveCount] = useState(0);
  const [history, setHistory] = useState<{ tiles: Tile[], score: number }[]>([]);
  let tileIdCounter = useRef(0);
  const [isMobile, setIsMobile] = useState(false);

  // モバイル判定
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // localStorage からベストスコアを読み込み
  useEffect(() => {
    const savedBestScore = localStorage.getItem('2048-best-score');
    if (savedBestScore) {
      setBestScore(parseInt(savedBestScore, 10));
    }
  }, []);

  // ベストスコアをlocalStorageに保存
  const updateBestScore = useCallback((newScore: number) => {
    if (newScore > bestScore) {
      setBestScore(newScore);
      localStorage.setItem('2048-best-score', newScore.toString());
    }
  }, [bestScore]);

  // 新しいタイルを生成
  const generateNewTile = useCallback((currentTiles: Tile[]): Tile | null => {
    const config = getGameConfig(isMobile);
    const emptyPositions: { row: number, col: number }[] = [];
    
    // 空のポジションを見つける
    for (let row = 0; row < config.GRID_SIZE; row++) {
      for (let col = 0; col < config.GRID_SIZE; col++) {
        const occupied = currentTiles.some(tile => tile.row === row && tile.col === col);
        if (!occupied) {
          emptyPositions.push({ row, col });
        }
      }
    }

    if (emptyPositions.length === 0) return null;

    const randomPosition = emptyPositions[Math.floor(Math.random() * emptyPositions.length)];
    const value = Math.random() < 0.9 ? 2 : 4; // 90%の確率で2、10%の確率で4

    return {
      id: tileIdCounter.current++,
      value,
      row: randomPosition.row,
      col: randomPosition.col,
      isNew: true,
    };
  }, [isMobile]);

  // ゲーム初期化
  const initializeGame = useCallback(() => {
    tileIdCounter.current = 0;
    const initialTiles: Tile[] = [];
    
    // 最初の2つのタイルを配置
    for (let i = 0; i < 2; i++) {
      const newTile = generateNewTile(initialTiles);
      if (newTile) {
        initialTiles.push(newTile);
      }
    }

    setTiles(initialTiles);
    setScore(0);
    setGameState('playing');
    setMoveCount(0);
    setHistory([]);
  }, [generateNewTile]);

  // 初期化
  useEffect(() => {
    initializeGame();
  }, [initializeGame]);

  // タイル移動のロジック
  const moveTiles = useCallback((direction: Direction) => {
    if (gameState !== 'playing') return false;

    const config = getGameConfig(isMobile);
    const newTiles = [...tiles];
    let moved = false;
    let newScore = score;
    const mergedTileIds = new Set<number>();

    // 方向に応じてタイルをソート
    const getSortedTiles = () => {
      switch (direction) {
        case 'up':
          return newTiles.sort((a, b) => a.row - b.row);
        case 'down':
          return newTiles.sort((a, b) => b.row - a.row);
        case 'left':
          return newTiles.sort((a, b) => a.col - b.col);
        case 'right':
          return newTiles.sort((a, b) => b.col - a.col);
      }
    };

    const sortedTiles = getSortedTiles();

    // 各タイルを移動
    sortedTiles.forEach(tile => {
      if (mergedTileIds.has(tile.id)) return;

      let newRow = tile.row;
      let newCol = tile.col;

      // 移動方向に移動
      while (true) {
        let nextRow = newRow;
        let nextCol = newCol;

        switch (direction) {
          case 'up': nextRow--; break;
          case 'down': nextRow++; break;
          case 'left': nextCol--; break;
          case 'right': nextCol++; break;
        }

        // 境界チェック
        if (nextRow < 0 || nextRow >= config.GRID_SIZE || 
            nextCol < 0 || nextCol >= config.GRID_SIZE) {
          break;
        }

        // 他のタイルとの衝突をチェック
        const collision = newTiles.find(t => 
          t.id !== tile.id && 
          t.row === nextRow && 
          t.col === nextCol &&
          !mergedTileIds.has(t.id)
        );

        if (collision) {
          // 同じ値のタイルなら合成
          if (collision.value === tile.value) {
            collision.value *= 2;
            collision.isMerged = true;
            newScore += collision.value;
            mergedTileIds.add(tile.id);
            mergedTileIds.add(collision.id);
            
            // タイルを削除
            const tileIndex = newTiles.findIndex(t => t.id === tile.id);
            if (tileIndex !== -1) {
              newTiles.splice(tileIndex, 1);
            }
            moved = true;

            // 2048達成チェック
            if (collision.value === 2048 && gameState === 'playing') {
              setGameState('won');
            }
          }
          break;
        }

        // 移動
        newRow = nextRow;
        newCol = nextCol;
      }

      // 位置が変わった場合
      if (newRow !== tile.row || newCol !== tile.col) {
        tile.row = newRow;
        tile.col = newCol;
        moved = true;
      }
    });

    if (moved) {
      // 履歴に保存
      setHistory(prev => [...prev, { tiles: [...tiles], score }]);
      
      // 新しいタイルを追加
      const newTile = generateNewTile(newTiles);
      if (newTile) {
        newTiles.push(newTile);
      }

      setTiles(newTiles);
      setScore(newScore);
      setMoveCount(prev => prev + 1);
      updateBestScore(newScore);

      // ゲームオーバーチェック
      setTimeout(() => {
        if (!canMove(newTiles)) {
          setGameState('lost');
        }
      }, config.ANIMATION_DURATION);
    }

    return moved;
  }, [tiles, score, gameState, generateNewTile, updateBestScore, isMobile]);

  // 移動可能かチェック
  const canMove = useCallback((currentTiles: Tile[]) => {
    const config = getGameConfig(isMobile);
    // 空きスペースがあるか
    if (currentTiles.length < config.GRID_SIZE * config.GRID_SIZE) {
      return true;
    }

    // 隣接する同じ値のタイルがあるか
    for (const tile of currentTiles) {
      const neighbors = [
        { row: tile.row - 1, col: tile.col },
        { row: tile.row + 1, col: tile.col },
        { row: tile.row, col: tile.col - 1 },
        { row: tile.row, col: tile.col + 1 },
      ];

      for (const neighbor of neighbors) {
        if (neighbor.row >= 0 && neighbor.row < config.GRID_SIZE &&
            neighbor.col >= 0 && neighbor.col < config.GRID_SIZE) {
          const neighborTile = currentTiles.find(t => 
            t.row === neighbor.row && t.col === neighbor.col
          );
          if (neighborTile && neighborTile.value === tile.value) {
            return true;
          }
        }
      }
    }

    return false;
  }, [isMobile]);

  // Undo機能
  const undoMove = useCallback(() => {
    if (history.length === 0) return;
    
    const lastState = history[history.length - 1];
    setTiles(lastState.tiles);
    setScore(lastState.score);
    setHistory(prev => prev.slice(0, -1));
    setMoveCount(prev => prev - 1);
    
    if (gameState === 'lost') {
      setGameState('playing');
    }
  }, [history, gameState]);

  // キーボード操作
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    const { key } = event;
    
    if (key === 'r' || key === 'R') {
      initializeGame();
      return;
    }

    if (key === 'u' || key === 'U') {
      undoMove();
      return;
    }

    const directionMap: { [key: string]: Direction } = {
      ArrowUp: 'up',
      ArrowDown: 'down',
      ArrowLeft: 'left',
      ArrowRight: 'right',
      w: 'up',
      W: 'up',
      s: 'down',
      S: 'down',
      a: 'left',
      A: 'left',
      d: 'right',
      D: 'right',
    };

    const direction = directionMap[key];
    if (direction) {
      event.preventDefault();
      moveTiles(direction);
    }
  }, [initializeGame, undoMove, moveTiles]);

  // イベントリスナーの設定
  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // Canvas描画
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const config = getGameConfig(isMobile);

    // キャンバスクリア
    ctx.fillStyle = BACKGROUND_COLOR;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // セルの背景を描画
    for (let row = 0; row < config.GRID_SIZE; row++) {
      for (let col = 0; col < config.GRID_SIZE; col++) {
        const x = col * (config.CELL_SIZE + config.CELL_GAP) + config.CELL_GAP;
        const y = row * (config.CELL_SIZE + config.CELL_GAP) + config.CELL_GAP;
        
        ctx.fillStyle = CELL_COLOR;
        ctx.fillRect(x, y, config.CELL_SIZE, config.CELL_SIZE);
      }
    }

    // タイルを描画
    tiles.forEach(tile => {
      const x = tile.col * (config.CELL_SIZE + config.CELL_GAP) + config.CELL_GAP;
      const y = tile.row * (config.CELL_SIZE + config.CELL_GAP) + config.CELL_GAP;
      
      const colors = TILE_COLORS[tile.value as keyof typeof TILE_COLORS] || 
                     { bg: '#3c3a32', text: '#f9f6f2' };
      
      // タイル背景
      ctx.fillStyle = colors.bg;
      ctx.fillRect(x, y, config.CELL_SIZE, config.CELL_SIZE);
      
      // タイル数字
      ctx.fillStyle = colors.text;
      const fontSize = isMobile ? (tile.value > 99 ? '24px' : '32px') : (tile.value > 99 ? '32px' : '40px');
      ctx.font = `bold ${fontSize} Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(
        tile.value.toString(),
        x + config.CELL_SIZE / 2,
        y + config.CELL_SIZE / 2
      );
    });
  }, [tiles, isMobile]);

  const config = getGameConfig(isMobile);
  const canvasSize = config.GRID_SIZE * (config.CELL_SIZE + config.CELL_GAP) + config.CELL_GAP;

  // レスポンシブ対応のスタイル
  const containerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    gap: isMobile ? "16px" : "24px",
    padding: isMobile ? "16px" : "0",
    width: "100%",
    maxWidth: "100vw",
    overflow: "hidden"
  };

  const scoreStyle = {
    backgroundColor: "white",
    borderRadius: "12px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
    padding: isMobile ? "16px" : "24px",
    width: "100%",
    maxWidth: isMobile ? "350px" : "400px"
  };

  return (
    <div style={containerStyle}>
      {/* スコア表示 */}
      <div style={scoreStyle}>
        <div style={{ 
          display: "grid", 
          gridTemplateColumns: "1fr 1fr", 
          gap: isMobile ? "12px" : "16px", 
          textAlign: "center" as const 
        }}>
          <div>
            <div style={{ 
              fontSize: isMobile ? "20px" : "24px", 
              fontWeight: "bold", 
              color: "#9333EA" 
            }}>{score}</div>
            <div style={{ 
              fontSize: isMobile ? "12px" : "14px", 
              color: "#6B7280" 
            }}>スコア</div>
          </div>
          <div>
            <div style={{ 
              fontSize: isMobile ? "20px" : "24px", 
              fontWeight: "bold", 
              color: "#2563EB" 
            }}>{bestScore}</div>
            <div style={{ 
              fontSize: isMobile ? "12px" : "14px", 
              color: "#6B7280" 
            }}>ベストスコア</div>
          </div>
        </div>
        <div style={{ marginTop: isMobile ? "12px" : "16px", textAlign: "center" }}>
          <div style={{ 
            fontSize: isMobile ? "11px" : "14px", 
            color: "#9CA3AF" 
          }}>
            手数: {moveCount} | Undoできる: {history.length}回
          </div>
        </div>
      </div>

      {/* ゲームステータス */}
      <div style={{ textAlign: "center" }}>
        {gameState === 'playing' && (
          <div style={{ 
            fontSize: isMobile ? "14px" : "18px", 
            color: "#9333EA", 
            fontWeight: "500" 
          }}>
            🎯 2048を目指して数字を合成しよう！
          </div>
        )}
        {gameState === 'won' && (
          <div style={{ 
            fontSize: isMobile ? "14px" : "18px", 
            color: "#059669", 
            fontWeight: "500" 
          }}>
            🎉 おめでとうございます！2048達成！
          </div>
        )}
        {gameState === 'lost' && (
          <div style={{ 
            fontSize: isMobile ? "14px" : "18px", 
            color: "#DC2626", 
            fontWeight: "500" 
          }}>
            💀 ゲームオーバー - 動ける手がありません
          </div>
        )}
      </div>

      {/* ゲームキャンバス */}
      <div style={{ 
        position: "relative", 
        display: "flex", 
        justifyContent: "center",
        width: "100%"
      }}>
        <canvas
          ref={canvasRef}
          width={canvasSize}
          height={canvasSize}
          style={{
            border: "2px solid #A855F7",
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
            backgroundColor: "white",
            display: "block",
            maxWidth: "100%",
            height: "auto",
            ...(isMobile && {
              width: "100%",
              maxWidth: "350px"
            })
          }}
        />
      </div>

      {/* コントロールボタン */}
      <div style={{ 
        display: "flex", 
        flexDirection: "column", 
        alignItems: "center", 
        gap: isMobile ? "16px" : "20px" 
      }}>
        <div style={{ 
          display: "flex", 
          gap: isMobile ? "12px" : "16px" 
        }}>
          <button
            onClick={initializeGame}
            style={{
              backgroundColor: "#9333EA",
              color: "white",
              padding: isMobile ? "10px 20px" : "12px 24px",
              borderRadius: "8px",
              border: "none",
              fontSize: isMobile ? "14px" : "16px",
              fontWeight: "500",
              cursor: "pointer",
              transition: "all 0.2s"
            }}
          >
            新しいゲーム
          </button>
          <button
            onClick={undoMove}
            disabled={history.length === 0}
            style={{
              backgroundColor: history.length === 0 ? "#D1D5DB" : "#6B7280",
              color: "white",
              padding: isMobile ? "10px 20px" : "12px 24px",
              borderRadius: "8px",
              border: "none",
              fontSize: isMobile ? "14px" : "16px",
              fontWeight: "500",
              cursor: history.length === 0 ? "not-allowed" : "pointer",
              transition: "all 0.2s"
            }}
          >
            一手戻す (U)
          </button>
        </div>

        {/* モバイル操作ボタン */}
        {isMobile && (
          <div>
            <div style={{ textAlign: "center", marginBottom: "8px" }}>
              <button
                onClick={() => moveTiles('up')}
                style={{
                  backgroundColor: "#3B82F6",
                  color: "white",
                  width: "48px",
                  height: "48px",
                  borderRadius: "8px",
                  border: "none",
                  fontSize: "20px",
                  fontWeight: "bold",
                  cursor: "pointer",
                  transition: "all 0.2s"
                }}
              >
                ↑
              </button>
            </div>
            <div style={{ 
              display: "flex", 
              justifyContent: "center", 
              gap: "8px" 
            }}>
              <button
                onClick={() => moveTiles('left')}
                style={{
                  backgroundColor: "#3B82F6",
                  color: "white",
                  width: "48px",
                  height: "48px",
                  borderRadius: "8px",
                  border: "none",
                  fontSize: "20px",
                  fontWeight: "bold",
                  cursor: "pointer",
                  transition: "all 0.2s"
                }}
              >
                ←
              </button>
              <button
                onClick={() => moveTiles('down')}
                style={{
                  backgroundColor: "#3B82F6",
                  color: "white",
                  width: "48px",
                  height: "48px",
                  borderRadius: "8px",
                  border: "none",
                  fontSize: "20px",
                  fontWeight: "bold",
                  cursor: "pointer",
                  transition: "all 0.2s"
                }}
              >
                ↓
              </button>
              <button
                onClick={() => moveTiles('right')}
                style={{
                  backgroundColor: "#3B82F6",
                  color: "white",
                  width: "48px",
                  height: "48px",
                  borderRadius: "8px",
                  border: "none",
                  fontSize: "20px",
                  fontWeight: "bold",
                  cursor: "pointer",
                  transition: "all 0.2s"
                }}
              >
                →
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 