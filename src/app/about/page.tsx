import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import { getSettings, getFeaturedPosts } from '@/lib/api';
import { processContentForDisplay } from '@/lib/contentUtils';

export default async function AboutPage() {
  // 設定を取得
  const settings = await getSettings([
    'profile_name',
    'profile_image',
    'profile_description',
    'profile_title',
    'twitter_url',
    'instagram_url',
    'youtube_url',
    'about_section_intro',
    'about_section_bio',
    'about_section_blog',
    'about_section_contact',
  ]);

  // 注目記事を取得（最大3件）
  const { posts: featuredPosts } = await getFeaturedPosts(3);
  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="relative h-80">
          <div className="absolute inset-0 z-0">
            <div className="relative w-full h-full">
              <Image
                src="/images/dogu-figure.jpg"
                alt="プロフィール背景"
                fill
                className="object-cover brightness-50"
                priority
              />
            </div>
          </div>
          <div className="container-custom relative z-10 h-full flex flex-col justify-end pb-12">
            <div className="text-white max-w-3xl">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-serif font-medium mb-4">
                プロフィール
              </h1>
              <p className="text-xl text-primary-100">
                造物者の空気感の管理人について
              </p>
            </div>
          </div>
        </section>

        {/* Profile Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/3 p-8 flex flex-col items-center text-center border-b md:border-b-0 md:border-r border-gray-200">
                  <div className="w-40 h-40 rounded-full mb-6 overflow-hidden relative profile-image">
                    <Image
                      src={settings.profile_image || "/images/owl-icon.png"}
                      alt={settings.profile_name || "造物者"}
                      fill
                      sizes="(max-width: 768px) 160px, 160px"
                      className="object-cover"
                    />
                  </div>
                  <h2 className="text-2xl font-serif font-medium mb-2">{settings.profile_name || "造物者"}</h2>
                  <p className="text-primary-600 mb-4">
                    {settings.profile_title || "美術史研究者 / 工芸愛好家"}
                  </p>
                  <div className="flex space-x-4 mt-4">
                    {settings.twitter_url && (
                      <a href={settings.twitter_url} target="_blank" rel="noopener noreferrer" className="text-accent-600 hover:text-accent-800">
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                      </a>
                    )}
                    {settings.instagram_url && (
                      <a href={settings.instagram_url} target="_blank" rel="noopener noreferrer" className="text-accent-600 hover:text-accent-800">
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                        </svg>
                      </a>
                    )}
                    {settings.youtube_url && (
                      <a href={settings.youtube_url} target="_blank" rel="noopener noreferrer" className="text-accent-600 hover:text-accent-800">
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                        </svg>
                      </a>
                    )}
                  </div>
                </div>

                <div className="md:w-2/3 p-8">
                  <div className="prose max-w-none">
                    {/* はじめまして */}
                    <h3>はじめまして</h3>
                    {settings.about_section_intro ? (
                      <div dangerouslySetInnerHTML={{
                        __html: processContentForDisplay(settings.about_section_intro)
                      }} />
                    ) : (
                      <p>
                        「造物者の空気感」へようこそ。このブログでは、趣味としての日本の美術、音楽、スポーツ観戦や、その他生活についての考察や発見を記録しています。
                      </p>
                    )}

                    {/* 経歴 */}
                    <h3>経歴</h3>
                    {settings.about_section_bio ? (
                      <div dangerouslySetInnerHTML={{
                        __html: processContentForDisplay(settings.about_section_bio)
                      }} />
                    ) : (
                      <>
                        <p>
                          高校時代から勉強もせずコンビニエンスストアでアルバイト。大学に入ってからもコンビニエンスストアでアルバイトをしながら、仲間とマージャンの日々を過ごしました。社会人になってからは、スーパーマーケット会社に勤務し、アメリカに1年間留学。帰国後、システム設計、開発、導入などの業務に25年間携わってきました。
                        </p>
                        <p>
                          そろそろ穏やかに、気ままな生活をしたいと思い独立。現在、未婚妻の芸術活動を支援する傍ら、AIを使った開発などを楽しんでいます。
                        </p>
                      </>
                    )}

                    {/* ブログについて */}
                    <h3>ブログについて</h3>
                    {settings.about_section_blog ? (
                      <div dangerouslySetInnerHTML={{
                        __html: processContentForDisplay(settings.about_section_blog)
                      }} />
                    ) : (
                      <p>
                        このブログは、私のこれまでの思い出や思い入れのある事項、また、新しく学んだことなどを無作為に記録するためのブログです。
                      </p>
                    )}

                    {/* 連絡先 */}
                    <h3>連絡先</h3>
                    {settings.about_section_contact ? (
                      <div dangerouslySetInnerHTML={{
                        __html: processContentForDisplay(settings.about_section_contact)
                      }} />
                    ) : (
                      <p>
                        ご質問やご意見がありましたら、<Link href="/contact" className="text-accent-600 hover:text-accent-800">お問い合わせフォーム</Link>からお気軽にご連絡ください。
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Works Section */}
        <section className="py-12 bg-gray-50">
          <div className="container-custom">
            <h2 className="text-2xl font-serif font-medium mb-8">注目の記事</h2>

            {featuredPosts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredPosts.map((post) => (
                  <div key={post.id} className="card h-full flex flex-col">
                    <div className="relative h-48 w-full">
                      <Image
                        src={post.featuredImage || "/images/dogu-figure.jpg"}
                        alt={post.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6 flex-grow flex flex-col">
                      <h3 className="text-xl font-serif font-medium mb-3">
                        <Link href={`/posts/${post.slug}`} className="text-primary-900 hover:text-accent-700">
                          {post.title}
                        </Link>
                      </h3>
                      <p className="text-primary-600 mb-4 flex-grow">
                        {post.excerpt || post.title}
                      </p>
                      <Link
                        href={`/posts/${post.slug}`}
                        className="inline-flex items-center text-accent-600 hover:text-accent-800 mt-auto"
                      >
                        続きを読む
                        <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                        </svg>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10">
                <p className="text-gray-500">注目の記事はまだありません。</p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
