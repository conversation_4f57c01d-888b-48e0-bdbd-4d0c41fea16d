import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import FeaturedPost from '@/components/ui/FeaturedPost';
import PostCard from '@/components/ui/PostCard';
import CategoryList from '@/components/ui/CategoryList';
import Link from 'next/link';
import Image from 'next/image';

// Import API functions
import { getCategories, getFeaturedPosts, getRecentPosts, getSettings } from '@/lib/api';
import { getAdminUser } from '@/lib/getAdminUser';

// ISR (Incremental Static Regeneration) を設定してパフォーマンス改善
export const revalidate = 300; // 5分ごとに再生成

export default async function Home() {
  // データの取得
  const categoriesData = await getCategories();
  const { posts: featuredPosts } = await getFeaturedPosts(3);
  const { posts: recentPosts } = await getRecentPosts(3);
  const settings = await getSettings([
    'site_title',
    'site_description',
    'profile_name',
    'profile_image',
    'profile_description',
  ]);

  // 管理者ユーザーを取得
  const adminUser = await getAdminUser();

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="relative text-white py-16">
          <div className="absolute inset-0 z-0">
            <div className="relative w-full h-full">
              <Image
                src="/images/dogu-figure.jpg"
                alt="Hero Background"
                fill
                sizes="100vw"
                className="object-cover brightness-50"
                priority
              />
            </div>
          </div>
          <div className="container-custom relative z-10">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-serif font-medium mb-6">{settings.site_title || '造物者の空気感'}</h1>
              <p className="text-xl text-primary-100 mb-8">
                {settings.site_description || '日本の美術、工芸、考古学、文化についての考察と発見の記録'}
              </p>
              <Link href="/about" className="btn btn-primary">
                詳しく見る
              </Link>
            </div>
          </div>
        </section>

        {/* Featured Posts */}
        <section className="py-12">
          <div className="container-custom">
            <h2 className="text-2xl font-serif font-medium mb-8">注目の記事</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {featuredPosts && featuredPosts.length > 0 ? (
                featuredPosts.map((post) => (
                  <FeaturedPost key={post.id} post={post} />
                ))
              ) : (
                <div className="col-span-3 bg-white rounded-lg shadow-md p-8 text-center">
                  <p className="text-primary-600 mb-4">注目の記事はまだありません。</p>
                  <p className="text-sm text-gray-500">新しい記事が追加されるまでお待ちください。</p>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-12 bg-gray-50">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-serif font-medium mb-8">最新の記事</h2>

                <div className="space-y-6">
                  {recentPosts && recentPosts.length > 0 ? (
                    recentPosts.map((post, index) => (
                      <PostCard key={post.id} post={post} imageIndex={index} />
                    ))
                  ) : (
                    <div className="bg-white rounded-lg shadow-md p-8 text-center">
                      <p className="text-primary-600 mb-4">記事はまだありません。</p>
                      <p className="text-sm text-gray-500 mb-6">新しい記事が追加されるまでお待ちください。</p>
                      <p className="text-sm text-gray-600">
                        管理画面から記事を追加することができます。
                      </p>
                    </div>
                  )}
                </div>

                <div className="mt-8 text-center">
                  <Link href="/posts" className="btn btn-secondary">
                    すべての記事を見る
                  </Link>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-8">
                <CategoryList categories={categoriesData} />

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">プロフィール</h2>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-24 h-24 rounded-full mb-4 overflow-hidden relative profile-image">
                      {adminUser ? (
                        <Image
                          src={adminUser.profileImage || "/images/owl-icon.png"}
                          alt={adminUser.username}
                          fill
                          sizes="(max-width: 768px) 100px, 96px"
                          className="object-cover"
                        />
                      ) : (
                        <Image
                          src={settings.profile_image || "/images/owl-icon.png"}
                          alt={settings.profile_name || "プロフィール画像"}
                          fill
                          sizes="(max-width: 768px) 100px, 96px"
                          className="object-cover"
                        />
                      )}
                    </div>
                    <h3 className="font-medium text-lg mb-2">
                      {adminUser ? adminUser.username : (settings.profile_name || "造物者")}
                    </h3>
                    <p className="text-primary-600 mb-4">
                      {adminUser?.bio || settings.profile_description || "美術史研究者。日本の伝統工芸と現代アートの接点に関心を持つ。"}
                    </p>
                    <Link
                      href={adminUser ? `/profile/${adminUser.id}` : "/about"}
                      className="text-accent-600 hover:text-accent-800"
                    >
                      詳しいプロフィール
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
