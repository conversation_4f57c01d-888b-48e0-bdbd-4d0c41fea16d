'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function TestUploadPage() {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testApiResult, setTestApiResult] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    setFile(selectedFile);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(selectedFile);
  };

  const handleUpload = async () => {
    if (!file) {
      setError('ファイルを選択してください');
      return;
    }

    setIsLoading(true);
    setError(null);
    setUploadResult(null);

    try {
      // Create FormData
      const formData = new FormData();
      formData.append('file', file);

      // Log request details
      console.log('アップロード開始:', {
        fileName: file.name,
        fileType: file.type,
        fileSize: `${Math.round(file.size / 1024)}KB`,
      });

      // Send request to the simplified API endpoint
      const res = await fetch('/api/upload-simple', {
        method: 'POST',
        body: formData,
      });

      // Log response details
      console.log('サーバーレスポンス:', {
        status: res.status,
        statusText: res.statusText,
        headers: {
          'content-type': res.headers.get('content-type'),
        }
      });

      if (!res.ok) {
        // Get error response text
        let errorText = '';
        try {
          errorText = await res.text();
        } catch (e) {
          errorText = 'レスポンステキストの取得に失敗';
        }
        console.error('サーバーエラーレスポンス:', errorText);
        throw new Error(`Failed to upload image: ${res.status} ${res.statusText}`);
      }

      const data = await res.json();
      console.log('アップロード成功:', data);

      setUploadResult(JSON.stringify(data, null, 2));
    } catch (err) {
      console.error('アップロードエラー:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  const testApi = async () => {
    setTestApiResult(null);
    setError(null);

    try {
      const res = await fetch('/api/test');

      console.log('テストAPIレスポンス:', {
        status: res.status,
        statusText: res.statusText,
        headers: {
          'content-type': res.headers.get('content-type'),
        }
      });

      if (!res.ok) {
        throw new Error(`Test API failed: ${res.status} ${res.statusText}`);
      }

      const data = await res.json();
      console.log('テストAPI成功:', data);

      setTestApiResult(JSON.stringify(data, null, 2));
    } catch (err) {
      console.error('テストAPIエラー:', err);
      setError(err instanceof Error ? err.message : String(err));
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">画像アップロードテスト</h1>

      <div className="mb-4">
        <button
          onClick={testApi}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2"
        >
          テストAPI呼び出し
        </button>
      </div>

      <div className="mb-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
      </div>

      {previewUrl && (
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">プレビュー</h2>
          <div className="relative w-64 h-64 border border-gray-300">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              style={{ objectFit: 'contain' }}
            />
          </div>
        </div>
      )}

      <button
        onClick={handleUpload}
        disabled={!file || isLoading}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:bg-gray-400"
      >
        {isLoading ? 'アップロード中...' : 'アップロード'}
      </button>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">
          <h2 className="font-bold">エラー</h2>
          <p>{error}</p>
        </div>
      )}

      {testApiResult && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">テストAPI結果</h2>
          <pre className="bg-gray-100 p-3 rounded overflow-auto max-h-60">
            {testApiResult}
          </pre>
        </div>
      )}

      {uploadResult && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-2">アップロード結果</h2>
          <pre className="bg-gray-100 p-3 rounded overflow-auto max-h-60">
            {uploadResult}
          </pre>
        </div>
      )}
    </div>
  );
}
