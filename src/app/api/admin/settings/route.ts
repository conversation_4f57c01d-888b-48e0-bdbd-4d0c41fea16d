import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema for settings
const settingSchema = z.object({
  key: z.string().min(1),
  value: z.string(),
  description: z.string().optional(),
});

// GET /api/admin/settings - Get all settings
export async function GET(request: Request) {
  try {
    console.log('設定一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    if (!auth.isAdmin) {
      console.log('管理者権限がありません');
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      );
    }

    console.log('設定データを取得します');
    const settings = await prisma.siteSettings.findMany({
      orderBy: {
        key: 'asc',
      },
    });

    console.log(`${settings.length}件の設定を取得しました`);
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings - Update multiple settings
export async function POST(request: Request) {
  try {
    console.log('設定更新APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    if (!auth.isAdmin) {
      console.log('管理者権限がありません');
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log('リクエストボディ:', body);

    // Validate request body (array of settings)
    if (!Array.isArray(body)) {
      console.log('無効な入力形式です。設定の配列が必要です。');
      return NextResponse.json(
        { error: 'Invalid input. Expected an array of settings.' },
        { status: 400 }
      );
    }

    // Process each setting
    const updatedSettings = [];
    for (const setting of body) {
      const validation = settingSchema.safeParse(setting);
      if (!validation.success) {
        console.log('設定データが無効です:', setting, validation.error);
        return NextResponse.json(
          {
            error: 'Invalid setting data',
            details: validation.error.format(),
            setting
          },
          { status: 400 }
        );
      }

      console.log('設定を更新/作成します:', setting.key);
      // Update or create the setting
      const updatedSetting = await prisma.siteSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value, description: setting.description },
        create: {
          key: setting.key,
          value: setting.value,
          description: setting.description || null,
        },
      });

      updatedSettings.push(updatedSetting);
    }

    console.log(`${updatedSettings.length}件の設定を更新しました`);
    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
