import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema for update
const updateSubCategorySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  slug: z.string().min(1).max(100)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .optional(),
  description: z.string().max(500).optional().nullable(),
  categoryId: z.string().min(1).optional(),
});

// Route parameter type

// GET /api/admin/subcategories/[id] - Get a single subcategory
export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('サブカテゴリー詳細APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const subCategory = await prisma.subCategory.findUnique({
      where: {
        id: params.id,
      },
      include: {
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!subCategory) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      );
    }

    // Get related counts
    const postsCount = await prisma.post.count({
      where: {
        subCategoryId: params.id,
      },
    });

    // Add post count directly to the subcategory object
    const subCategoryWithCounts = {
      ...subCategory,
      _count: {
        posts: postsCount
      }
    };

    return NextResponse.json(subCategoryWithCounts);
  } catch (error) {
    console.error('Error fetching subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subcategory' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/subcategories/[id] - Update a subcategory
export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('サブカテゴリー更新APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', body);

    // Validate request body
    const validation = updateSubCategorySchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if subcategory exists
    const existingSubCategory = await prisma.subCategory.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingSubCategory) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      );
    }

    // If categoryId is provided, check if it exists
    if (body.categoryId) {
      const category = await prisma.category.findUnique({
        where: {
          id: body.categoryId,
        },
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        );
      }
    }

    // Check if slug is already taken by another subcategory in the same category
    if (body.slug && body.slug !== existingSubCategory.slug) {
      const categoryId = body.categoryId || existingSubCategory.categoryId;
      const duplicateSubCategory = await prisma.subCategory.findFirst({
        where: {
          slug: body.slug,
          categoryId,
          NOT: {
            id: params.id,
          },
        },
      });

      if (duplicateSubCategory) {
        return NextResponse.json(
          { error: 'Slug is already taken in this category' },
          { status: 409 }
        );
      }
    }

    // Update subcategory
    const updatedSubCategory = await prisma.subCategory.update({
      where: {
        id: params.id,
      },
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
        categoryId: body.categoryId,
      },
    });

    return NextResponse.json(updatedSubCategory);
  } catch (error) {
    console.error('Error updating subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to update subcategory' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/subcategories/[id] - Delete a subcategory
export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('サブカテゴリー削除APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    // Check if subcategory exists
    const existingSubCategory = await prisma.subCategory.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingSubCategory) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      );
    }

    // Update posts to remove the subcategory reference
    await prisma.post.updateMany({
      where: {
        subCategoryId: params.id,
      },
      data: {
        subCategoryId: null,
      },
    });

    // Delete subcategory
    await prisma.subCategory.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json(
      { message: 'Subcategory deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to delete subcategory' },
      { status: 500 }
    );
  }
}
