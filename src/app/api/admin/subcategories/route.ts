import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema
const subCategorySchema = z.object({
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(100)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().max(500).optional(),
  categoryId: z.string().min(1),
});

// GET /api/admin/subcategories - Get all subcategories
export async function GET(request: Request) {
  try {
    console.log('サブカテゴリー一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const subCategories = await prisma.subCategory.findMany({
      include: {
        category: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            posts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(subCategories);
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subcategories' },
      { status: 500 }
    );
  }
}

// POST /api/admin/subcategories - Create a new subcategory
export async function POST(request: Request) {
  try {
    console.log('サブカテゴリー作成APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', body);

    // Validate request body
    const validation = subCategorySchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { name, slug, description, categoryId } = body;

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: {
        id: categoryId,
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if subcategory with the same slug already exists
    const existingSubCategory = await prisma.subCategory.findFirst({
      where: {
        slug,
        categoryId,
      },
    });

    if (existingSubCategory) {
      return NextResponse.json(
        { error: 'Subcategory with this slug already exists in this category' },
        { status: 409 }
      );
    }

    // Create subcategory
    const subCategory = await prisma.subCategory.create({
      data: {
        name,
        slug,
        description,
        categoryId,
      },
    });

    return NextResponse.json(subCategory, { status: 201 });
  } catch (error) {
    console.error('Error creating subcategory:', error);
    return NextResponse.json(
      { error: 'Failed to create subcategory' },
      { status: 500 }
    );
  }
}
