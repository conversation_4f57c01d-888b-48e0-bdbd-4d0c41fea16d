import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// GET /api/admin/messages - Get all messages
export async function GET(request: Request) {
  try {
    console.log('メッセージ一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const messages = await prisma.contactMessage.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}
