import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';
import { z } from 'zod';
import { sign } from 'jsonwebtoken';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// バリデーションスキーマ
const loginSchema = z.object({
  email: z.string().email().or(z.string().min(3)), // メールアドレスまたはユーザー名
  password: z.string().min(1),
});

// POST /api/admin/auth/login - シンプルなログイン処理
export async function POST(request: Request) {
  try {
    console.log('シンプルログインAPIが呼び出されました');

    // リクエストボディを取得
    const body = await request.json();
    console.log('リクエストボディ:', body);

    // バリデーション
    const result = loginSchema.safeParse(body);
    if (!result.success) {
      console.log('バリデーションエラー:', result.error);
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.format() },
        { status: 400 }
      );
    }

    const { email, password } = result.data;
    console.log('ログイン試行:', { email, passwordLength: password.length });

    try {
      // データベースからユーザーを検索（メールアドレスまたはユーザー名で）
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { username: email },
          ],
        },
      });

      console.log('データベース検索結果:', user ? 'ユーザーが見つかりました' : 'ユーザーが見つかりません');

      if (!user) {
        console.log('ユーザーが見つかりません:', email);
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        );
      }

      console.log('ユーザーが見つかりました:', {
        id: user.id,
        username: user.username,
        email: user.email,
        isAdmin: user.isAdmin,
      });

      // パスワードの検証
      const isPasswordValid = await bcrypt.compare(password, user.password);
      console.log('パスワード検証結果:', isPasswordValid);

      if (!isPasswordValid) {
        console.log('パスワードが一致しません');
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        );
      }

      // JWTトークンの生成
      const secretKey = process.env.NEXTAUTH_SECRET || 'fallback-secret-key';
      console.log('シークレットキーの長さ:', secretKey.length);

      const token = sign(
        {
          id: user.id,
          email: user.email,
          username: user.username,
          isAdmin: user.isAdmin,
        },
        secretKey,
        { expiresIn: '24h' }
      );

      console.log('ログイン成功、トークンを生成しました');

      // レスポンスを返す
      return NextResponse.json({
        success: true,
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.isAdmin,
        },
      });
    } catch (dbError) {
      console.error('データベース操作中にエラーが発生しました:', dbError);
      return NextResponse.json(
        { error: 'Database error', details: dbError instanceof Error ? dbError.message : String(dbError) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('ログイン処理中にエラーが発生しました:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
