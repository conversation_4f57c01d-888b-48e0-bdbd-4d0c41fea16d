import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verify } from 'jsonwebtoken';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// GET /api/admin/auth/verify - トークン検証
export async function GET(request: Request) {
  try {
    console.log('トークン検証APIが呼び出されました');
    
    // Authorizationヘッダーからトークンを取得
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('認証トークンがありません');
      return NextResponse.json(
        { authenticated: false, error: 'No token provided' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    console.log('トークンを検証します');
    
    try {
      // トークンの検証
      const decoded = verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret-key') as {
        id: string;
        email: string;
        username: string;
        isAdmin: boolean;
      };
      
      // ユーザーIDの取得
      const userId = decoded.id;
      
      // データベースでユーザーを確認
      const user = await prisma.user.findUnique({
        where: {
          id: userId,
        },
        select: {
          id: true,
          username: true,
          email: true,
          isAdmin: true,
        },
      });
      
      if (!user) {
        console.log('ユーザーが見つかりません:', userId);
        return NextResponse.json(
          { authenticated: false, error: 'User not found' },
          { status: 401 }
        );
      }
      
      console.log('トークン検証成功:', user.username);
      
      // 認証成功
      return NextResponse.json({
        authenticated: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: user.isAdmin,
        },
      });
    } catch (error) {
      console.error('トークン検証エラー:', error);
      return NextResponse.json(
        { authenticated: false, error: 'Invalid token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('トークン検証中にエラーが発生しました:', error);
    return NextResponse.json(
      { authenticated: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
