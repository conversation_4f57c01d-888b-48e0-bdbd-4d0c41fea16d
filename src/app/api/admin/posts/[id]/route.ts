import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// 設定ファイルをインポート（Next.js App Routerでは直接使用しないが、設定を明示するために記述）
import './route.config';

// 新しい形式でAPI設定を行います
export const maxDuration = 120; // 120秒に延長
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema for update
const updatePostSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  slug: z.string().min(1).max(200)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .optional(),
  content: z.string().min(1).optional(),
  excerpt: z.string().max(500).optional().nullable(),
  featuredImage: z.string().optional().nullable(),
  published: z.boolean().optional(),
  featured: z.boolean().optional(),
  publishedAt: z.string().optional().nullable(),
  categoryId: z.string().min(1).optional(),
  subCategoryId: z.string().optional().nullable(),
});

// Route parameter type

// GET /api/admin/posts/[id] - Get a single post
export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('記事詳細APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    console.log('記事データを取得します:', params.id);
    const post = await prisma.post.findFirst({
      where: {
        id: params.id,
        authorId: auth.userId, // 自分の記事のみアクセス可能
      },
      include: {
        user: {
          select: {
            username: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
        subCategory: {
          select: {
            name: true,
          },
        },
        images: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(post);
  } catch (error) {
    console.error('Error fetching post:', error);

    // エラーの詳細情報をログに出力
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }

    // 常にJSONレスポンスを返す
    const headers = {
      'Content-Type': 'application/json',
    };

    return NextResponse.json(
      {
        error: 'Failed to fetch post',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers
      }
    );
  }
}

// PUT /api/admin/posts/[id] - Update a post
export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('記事更新APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', { ...body, content: body.content?.substring(0, 100) + '...' });

    // Validate request body
    const validation = updatePostSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if post exists
    const existingPost = await prisma.post.findFirst({
      where: {
        id: params.id,
        authorId: auth.userId, // 自分の記事のみ更新可能
      },
    });

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if slug is already taken by another post
    if (body.slug && body.slug !== existingPost.slug) {
      const duplicatePost = await prisma.post.findUnique({
        where: {
          slug: body.slug,
        },
      });

      if (duplicatePost && duplicatePost.id !== params.id) {
        return NextResponse.json(
          { error: 'Slug is already taken' },
          { status: 409 }
        );
      }
    }

    // Prepare data for update
    const updateData: any = { ...body };

    // Convert publishedAt string to Date if it exists
    if (updateData.publishedAt) {
      updateData.publishedAt = new Date(updateData.publishedAt);
    } else if (updateData.published && !existingPost.publishedAt) {
      // If post is being published for the first time and no publishedAt is provided
      updateData.publishedAt = new Date();
    }

    // Update post
    const updatedPost = await prisma.post.update({
      where: {
        id: params.id,
      },
      data: updateData,
      include: {
        user: {
          select: {
            username: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
        subCategory: {
          select: {
            name: true,
          },
        },
      },
    });

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error('Error updating post:', error);

    // エラーの詳細情報を取得
    const errorMessage = error instanceof Error
      ? error.message
      : 'Unknown error occurred';

    // エラースタックも記録（開発環境のみ）
    if (error instanceof Error && error.stack) {
      console.error('Error stack:', error.stack);
    }

    // クライアントに返すエラーメッセージ
    return NextResponse.json(
      {
        error: 'Failed to update post',
        message: errorMessage,
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/posts/[id] - Delete a post
export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('記事削除APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    // Check if post exists
    const existingPost = await prisma.post.findFirst({
      where: {
        id: params.id,
        authorId: auth.userId, // 自分の記事のみ削除可能
      },
    });

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Delete post
    await prisma.post.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json(
      { message: 'Post deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting post:', error);

    // エラーの詳細情報をログに出力
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }

    // 常にJSONレスポンスを返す
    const headers = {
      'Content-Type': 'application/json',
    };

    return NextResponse.json(
      {
        error: 'Failed to delete post',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers
      }
    );
  }
}
