import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema
const postSchema = z.object({
  title: z.string().min(1).max(200),
  slug: z.string().min(1).max(200)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  content: z.string().min(1),
  excerpt: z.string().max(500).optional().nullable(),
  featuredImage: z.string().optional().nullable(),
  published: z.boolean().optional().default(false),
  featured: z.boolean().optional().default(false),
  publishedAt: z.string().optional().nullable(),
  categoryId: z.string().min(1),
  subCategoryId: z.string().optional().nullable(),
});

// GET /api/admin/posts - Get all posts
export async function GET(request: Request) {
  try {
    console.log('記事一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    console.log('記事データを取得します（ユーザー:', auth.userId, ')');
    const posts = await prisma.post.findMany({
      where: {
        authorId: auth.userId, // 自分の記事のみ取得
      },
      include: {
        user: {
          select: {
            username: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
        subCategory: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    console.log(`${posts.length}件の記事を取得しました`);
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}

// POST /api/admin/posts - Create a new post
export async function POST(request: Request) {
  try {
    console.log('記事作成APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', { ...body, content: body.content?.substring(0, 100) + '...' });

    // Validate request body
    const validation = postSchema.safeParse(body);
    if (!validation.success) {
      console.log('バリデーションエラー:', validation.error);
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if post with the same slug already exists
    const existingPost = await prisma.post.findUnique({
      where: {
        slug: body.slug,
      },
    });

    if (existingPost) {
      console.log('同じスラッグの記事が既に存在します:', body.slug);
      return NextResponse.json(
        { error: 'Post with this slug already exists' },
        { status: 409 }
      );
    }

    // Get user ID from token
    const userId = auth.userId;
    if (!userId) {
      console.log('ユーザーIDが見つかりません');
      return NextResponse.json(
        { error: 'User ID not found in token' },
        { status: 400 }
      );
    }

    console.log('トークンからのユーザーID:', userId);

    // Check if user exists in the database
    const userExists = await prisma.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!userExists) {
      console.error(`ユーザーID ${userId} がデータベースに見つかりません`);
      return NextResponse.json(
        { error: 'User not found in database. Please log out and log in again.' },
        { status: 400 }
      );
    }

    // Prepare data for creation
    const postData = {
      ...body,
      authorId: userId,
      // Convert publishedAt string to Date if it exists
      publishedAt: body.publishedAt ? new Date(body.publishedAt) : body.published ? new Date() : null,
    };

    console.log('記事を作成します:', { title: body.title, slug: body.slug });
    // Create post
    const post = await prisma.post.create({
      data: postData,
      include: {
        user: {
          select: {
            username: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
        subCategory: {
          select: {
            name: true,
          },
        },
      },
    });

    console.log('記事が作成されました:', post.id);
    return NextResponse.json(post, { status: 201 });
  } catch (error) {
    console.error('Error creating post:', error);

    // エラーの詳細情報をログに出力
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }

    // 常にJSONレスポンスを返す
    const headers = {
      'Content-Type': 'application/json',
    };

    return NextResponse.json(
      {
        error: 'Failed to create post',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers
      }
    );
  }
}
