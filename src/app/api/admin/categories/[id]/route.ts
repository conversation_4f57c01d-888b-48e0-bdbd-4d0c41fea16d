import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema for update
const updateCategorySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  slug: z.string().min(1).max(100)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .optional(),
  description: z.string().max(500).optional().nullable(),
});

// GET /api/admin/categories/[id] - Get a single category
export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('カテゴリー詳細APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    console.log('カテゴリーデータを取得します:', params.id);
    const category = await prisma.category.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Get related counts
    const postsCount = await prisma.post.count({
      where: {
        categoryId: params.id,
      },
    });

    const subCategoriesCount = await prisma.subCategory.count({
      where: {
        categoryId: params.id,
      },
    });

    return NextResponse.json({
      category,
      counts: {
        posts: postsCount,
        subCategories: subCategoriesCount,
      },
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/categories/[id] - Update a category
export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('カテゴリー更新APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', body);

    // Validate request body
    const validation = updateCategorySchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if slug is already taken by another category
    if (body.slug && body.slug !== existingCategory.slug) {
      const duplicateCategory = await prisma.category.findUnique({
        where: {
          slug: body.slug,
        },
      });

      if (duplicateCategory && duplicateCategory.id !== params.id) {
        return NextResponse.json(
          { error: 'Slug is already taken' },
          { status: 409 }
        );
      }
    }

    // Update category
    const updatedCategory = await prisma.category.update({
      where: {
        id: params.id,
      },
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
      },
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/categories/[id] - Delete a category
export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('カテゴリー削除APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Delete category
    await prisma.category.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json(
      { message: 'Category deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
