import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema
const categorySchema = z.object({
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(100)
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().max(500).optional(),
});

// GET /api/admin/categories - Get all categories
export async function GET(request: Request) {
  try {
    console.log('カテゴリー一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    console.log('カテゴリーデータを取得します');
    const categories = await prisma.category.findMany({
      include: {
        subCategories: {
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
          },
        },
        _count: {
          select: {
            posts: true,
            subCategories: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`${categories.length}件のカテゴリーを取得しました`);
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/admin/categories - Create a new category
export async function POST(request: Request) {
  try {
    console.log('カテゴリー作成APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', body);

    // Validate request body
    const validation = categorySchema.safeParse(body);
    if (!validation.success) {
      console.log('バリデーションエラー:', validation.error);
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { name, slug, description } = body;

    // Check if category with the same slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: {
        slug,
      },
    });

    if (existingCategory) {
      console.log('同じスラッグのカテゴリーが既に存在します:', slug);
      return NextResponse.json(
        { error: 'Category with this slug already exists' },
        { status: 409 }
      );
    }

    console.log('カテゴリーを作成します:', { name, slug });
    // Create category
    const category = await prisma.category.create({
      data: {
        name,
        slug,
        description,
      },
    });

    console.log('カテゴリーが作成されました:', category.id);
    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
