import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';

// API設定
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    // セキュリティキーの確認
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('key');

    // 環境変数のSETUP_SECRET_KEYまたはNEXTAUTH_SECRETと比較
    const validKey = process.env.SETUP_SECRET_KEY || process.env.NEXTAUTH_SECRET;

    if (secretKey !== validKey) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // カスタムユーザー名とパスワードを取得（指定がなければデフォルト値を使用）
    const username = searchParams.get('username') || 'admin';
    const email = searchParams.get('email') || '<EMAIL>';
    const password = searchParams.get('password') || 'admin123';

    // 既存のユーザーを確認
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email },
        ],
      },
    });

    if (existingUser) {
      // 既存ユーザーのパスワードを更新
      const hashedPassword = await bcrypt.hash(password, 10);

      const updatedUser = await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          password: hashedPassword,
          isAdmin: true, // 管理者権限を付与
        },
        select: {
          id: true,
          username: true,
          email: true,
          isAdmin: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Admin user updated successfully',
        user: updatedUser,
        credentials: {
          username: username,
          email: email,
          password: password,
        },
      });
    }

    // 新しい管理者ユーザーを作成
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        isAdmin: true,
      },
      select: {
        id: true,
        username: true,
        email: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: newUser,
      credentials: {
        username: username,
        email: email,
        password: password,
      },
    });
  } catch (error) {
    console.error('Create admin API error:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
