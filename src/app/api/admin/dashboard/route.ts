import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verify } from 'jsonwebtoken';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// GET /api/admin/dashboard - ダッシュボードデータの取得
export async function GET(request: Request) {
  try {
    console.log('ダッシュボードAPIが呼び出されました');
    
    // Authorizationヘッダーからトークンを取得
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('認証トークンがありません');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    try {
      // トークンの検証
      const decoded = verify(token, process.env.NEXTAUTH_SECRET || 'fallback-secret-key') as {
        id: string;
        isAdmin: boolean;
      };
      
      // データベースからダッシュボードデータを取得
      const [
        postsCount,
        categoriesCount,
        usersCount,
        messagesCount,
        unreadMessagesCount,
        recentPosts
      ] = await Promise.all([
        prisma.post.count({
          where: {
            authorId: decoded.id, // ログインユーザーの記事のみカウント
          },
        }),
        prisma.category.count(),
        prisma.user.count(),
        prisma.contactMessage.count(),
        prisma.contactMessage.count({
          where: {
            isRead: false,
          },
        }),
        prisma.post.findMany({
          where: {
            authorId: decoded.id, // ログインユーザーの記事のみ取得
          },
          take: 5,
          orderBy: {
            updatedAt: 'desc',
          },
          include: {
            // @ts-ignore - Prismaスキーマ変更により一時的に無視
            user: {
              select: {
                username: true,
              },
            },
          },
        }),
      ]);
      
      // レスポンスを返す
      return NextResponse.json({
        postsCount,
        categoriesCount,
        usersCount,
        messagesCount,
        unreadMessagesCount,
        recentPosts,
      });
    } catch (error) {
      console.error('トークン検証エラー:', error);
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('ダッシュボードデータの取得中にエラーが発生しました:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
