import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// API設定
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    // セキュリティキーの確認
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('key');

    // 環境変数のSETUP_SECRET_KEYまたはNEXTAUTH_SECRETと比較
    const validKey = process.env.SETUP_SECRET_KEY || process.env.NEXTAUTH_SECRET;

    if (secretKey !== validKey) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // ユーザー一覧を取得（パスワードは除外）
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
        // パスワードは含めない
        password: false,
      },
    });

    // パスワードハッシュの長さを確認（セキュリティのため完全なハッシュは返さない）
    const usersWithPasswordInfo = await Promise.all(
      users.map(async (user) => {
        const userWithPassword = await prisma.user.findUnique({
          where: { id: user.id },
          select: { password: true },
        });

        return {
          ...user,
          passwordLength: userWithPassword?.password?.length || 0,
          passwordPreview: userWithPassword?.password
            ? `${userWithPassword.password.substring(0, 10)}...`
            : null,
        };
      })
    );

    // 環境変数の状態を確認（機密情報は隠す）
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      DATABASE_URL_EXISTS: !!process.env.DATABASE_URL,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
      NEXTAUTH_SECRET_LENGTH: process.env.NEXTAUTH_SECRET?.length || 0,
      SETUP_SECRET_KEY_EXISTS: !!process.env.SETUP_SECRET_KEY,
    };

    // レスポンスを返す
    return NextResponse.json({
      success: true,
      message: 'Debug information',
      environment: envInfo,
      users: usersWithPasswordInfo,
      totalUsers: users.length,
    });
  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
