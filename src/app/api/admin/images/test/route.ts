import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

// 新しい形式でAPI設定を行います
export const maxDuration = 60; // 60秒
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * シンプルなテストAPIエンドポイント
 * 認証の確認とリクエスト情報の返却のみを行う
 */
export async function POST(request: Request) {
  console.log('テストAPIが呼び出されました');

  try {
    // リクエストのサイズを確認
    const contentLength = request.headers.get('content-length');
    let requestSize = 'unknown';
    if (contentLength) {
      const sizeInKB = parseInt(contentLength) / 1024;
      requestSize = `${sizeInKB.toFixed(2)}KB`;
      console.log(`リクエストサイズ: ${requestSize}`);
    }

    // Check authentication
    const session = await getServerSession();
    const isAuthenticated = !!session;
    console.log('認証状態:', isAuthenticated ? '認証済み' : '未認証');

    // Parse request body - サイズのみ取得して内容は処理しない
    const clonedRequest = request.clone();
    const text = await clonedRequest.text();
    const bodySize = `${(text.length / 1024).toFixed(2)}KB`;

    // レスポンスを返す
    return NextResponse.json({
      success: true,
      message: 'テストAPIは正常に動作しています',
      auth: {
        isAuthenticated,
        session: session ? '有効' : '無効'
      },
      request: {
        method: request.method,
        contentLength: requestSize,
        bodySize,
        contentType: request.headers.get('content-type') || 'なし'
      }
    });
  } catch (error) {
    console.error('テストAPIエラー:', error);
    const errorMessage = error instanceof Error ? error.message : '不明なエラー';
    const errorStack = error instanceof Error ? error.stack : '';
    
    return NextResponse.json({
      success: false,
      error: 'テストAPIでエラーが発生しました',
      details: {
        message: errorMessage,
        stack: process.env.NODE_ENV === 'development' ? errorStack : undefined
      }
    }, { status: 500 });
  }
} 