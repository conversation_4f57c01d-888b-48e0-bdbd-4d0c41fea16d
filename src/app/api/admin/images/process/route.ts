import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import sharp from 'sharp';
import { z } from 'zod';

// 新しい形式でAPI設定を行います
export const maxDuration = 60; // 60秒
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Maximum dimensions for original and thumbnail images
const MAX_ORIGINAL_WIDTH = 800;
const MAX_ORIGINAL_HEIGHT = 800;
const THUMBNAIL_WIDTH = 300;
const THUMBNAIL_HEIGHT = 300;

// Validation schema
const imageProcessSchema = z.object({
  image: z.string().min(1),
});

/**
 * Extract the mime type from a Base64 string
 */
const getMimeType = (base64String: string): string => {
  const match = base64String.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
  return match ? match[1] : '';
};

/**
 * Simple and robust image processing function
 * Returns both the original (unchanged) and thumbnail versions
 */
const processImage = async (
  base64Image: string
): Promise<{ original: string; thumbnail: string }> => {
  try {
    console.log('画像処理を開始します');

    // Extract the base64 data without the prefix
    let dataUrlParts;
    try {
      dataUrlParts = base64Image.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,(.*)$/);
      if (!dataUrlParts) {
        throw new Error('Invalid image format');
      }
    } catch (matchError) {
      console.error('Base64形式の解析に失敗:', matchError);
      throw new Error('Invalid image format: Cannot parse base64 data');
    }

    const mimeType = dataUrlParts[1];
    const base64Data = dataUrlParts[2];

    let buffer;
    try {
      buffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      console.error('Base64データのバッファ変換に失敗:', bufferError);
      throw new Error('Failed to convert base64 to buffer');
    }

    console.log(`画像形式: ${mimeType}, サイズ: ${buffer.length / 1024}KB`);

    // サイズチェックを除去（クライアント側とAPIリクエストで既にチェック済み）
    // 小さいファイルの場合でも処理を続行

    // Keep original image as is (no resizing)
    const original = base64Image;
    console.log('元の画像をそのまま使用します');

    // サムネイル生成を試みる（失敗してもオリジナル画像は返す）
    let thumbnail = '';
    try {
      // Process thumbnail only - メモリ使用量を最適化
      const thumbnailBuffer = await sharp(buffer, {
        limitInputPixels: 25000000, // 入力ピクセル数の制限
        failOn: 'none', // エラー時も処理を続行
        density: 72, // 解像度を下げる
        sequentialRead: true, // メモリ使用量を削減
      })
        .resize({
          width: THUMBNAIL_WIDTH,
          height: THUMBNAIL_HEIGHT,
          fit: 'inside',
          withoutEnlargement: true,
          kernel: 'nearest', // 最も高速で低メモリな方法
        })
        .jpeg({
          quality: 80,
          optimizeScans: true, // 最適化
          mozjpeg: false, // mozjpegは無効化（メモリ使用量が多い）
        })
        .toBuffer({ resolveWithObject: false });

      console.log(`サムネイルサイズ: ${thumbnailBuffer.length / 1024}KB`);

      // Convert thumbnail to base64
      thumbnail = `data:image/jpeg;base64,${thumbnailBuffer.toString('base64')}`;
    } catch (thumbnailError) {
      console.error('サムネイル生成に失敗しました:', thumbnailError);
      // サムネイル生成に失敗した場合は、オリジナル画像をサムネイルとして使用
      thumbnail = original;
    }

    console.log('画像処理が完了しました');

    return { original, thumbnail };
  } catch (error) {
    console.error('画像処理エラー:', error);
    throw error;
  }
};

export async function POST(request: Request) {
  console.log('画像処理APIが呼び出されました');

  try {
    // リクエストのサイズを確認
    const contentLength = request.headers.get('content-length');
    if (contentLength) {
      const sizeInMB = parseInt(contentLength) / (1024 * 1024);
      console.log(`リクエストサイズ: ${sizeInMB.toFixed(2)}MB`);

      // 5MBを超えるリクエストは拒否（Nginxの制限を考慮）
      if (sizeInMB > 5) {
        return NextResponse.json(
          { error: 'Request too large', message: `リクエストサイズが大きすぎます（${sizeInMB.toFixed(2)}MB）。5MB以下にしてください。` },
          { status: 413 }
        );
      }
    }

    // Check authentication
    let session;
    let isAuthenticated = false;
    try {
      session = await getServerSession();
      isAuthenticated = !!session;
      console.log('認証状態:', isAuthenticated ? '認証済み' : '未認証');
      
      // 認証チェックを一時的に無効化（テスト用）
      // if (!session) {
      //   console.log('認証エラー: 未認証のユーザー');
      //   return NextResponse.json(
      //     { error: 'Unauthorized' },
      //     { status: 401 }
      //   );
      // }
    } catch (authError) {
      console.error('認証チェック中にエラーが発生しました:', authError);
      // 認証エラーでも処理を続行（テスト用）
      console.log('認証エラーですが、処理を続行します');
    }

    // Parse request body
    let body;
    try {
      const clonedRequest = request.clone(); // リクエストをクローン
      const text = await clonedRequest.text(); // テキストとして取得

      // リクエストが大きすぎる場合は処理を中止
      if (text.length > 5 * 1024 * 1024) { // 5MB
        return NextResponse.json(
          { error: 'Request too large', message: 'リクエストサイズが大きすぎます。5MB以下にしてください。' },
          { status: 413 }
        );
      }

      try {
        body = JSON.parse(text);
      } catch (jsonError) {
        console.error('JSONパースエラー:', jsonError);
        return NextResponse.json(
          { error: 'Invalid JSON', message: 'JSONの解析に失敗しました。正しい形式で送信してください。' },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error('リクエスト本文の取得に失敗しました:', error);
      return NextResponse.json(
        { error: 'Request body error', message: 'リクエスト本文の取得に失敗しました。' },
        { status: 400 }
      );
    }

    // Validate request body
    const validation = imageProcessSchema.safeParse(body);
    if (!validation.success) {
      console.error('バリデーションエラー:', validation.error);
      return NextResponse.json(
        { error: 'Invalid input', message: '入力データが不正です。' },
        { status: 400 }
      );
    }

    const { image } = body;

    // Check image format
    if (!image || typeof image !== 'string' || !image.startsWith('data:image/')) {
      return NextResponse.json(
        { error: 'Invalid image format', message: '画像形式が不正です。Base64エンコードされた画像データを送信してください。' },
        { status: 400 }
      );
    }

    // Check image size (rough estimate before processing)
    try {
      const base64Data = image.replace(/^data:image\/\w+;base64,/, '');
      const sizeInBytes = (base64Data.length * 3) / 4;
      const sizeInKB = Math.round(sizeInBytes / 1024);
      console.log(`推定画像サイズ: ${sizeInKB}KB, Base64長さ: ${base64Data.length}バイト`);

      // デバッグ情報を追加
      console.log('画像情報:', {
        mimeType: image.split(';')[0].split(':')[1] || '不明',
        base64Length: image.length,
        dataLength: base64Data.length,
        estimatedSizeKB: sizeInKB
      });

      // サイズ制限を緩和（テスト用）- 800KBから1.5MBに変更
      const MAX_SIZE_KB = 1500; // 1.5MB
      if (sizeInKB > MAX_SIZE_KB) {
        return NextResponse.json(
          { error: 'Image too large', message: `画像サイズが大きすぎます（${sizeInKB}KB）。${MAX_SIZE_KB}KB以下の画像を選択してください。` },
          { status: 400 }
        );
      }
    } catch (sizeError) {
      console.error('画像サイズの計算中にエラーが発生しました:', sizeError);
      // エラーが発生しても処理を続行
      console.log('サイズ計算エラーですが、処理を続行します');
    }

    // Process image - 最小限の処理のみを行う
    try {
      // 画像処理を試みる
      let processedImage;
      try {
        processedImage = await processImage(image);
      } catch (processingError) {
        console.error('画像処理中にエラーが発生しました:', processingError);

        // エラーの詳細情報を含める
        const errorMessage = processingError instanceof Error ? processingError.message : '不明なエラー';
        const errorStack = processingError instanceof Error ? processingError.stack : '';
        console.error('エラー詳細:', errorMessage);
        console.error('スタックトレース:', errorStack);

        // サムネイル生成をスキップして、オリジナル画像のみを返す
        processedImage = {
          original: image,
          thumbnail: image // サムネイルもオリジナルと同じにする
        };

        console.log('画像処理に失敗しましたが、オリジナル画像をそのまま使用します');
      }

      // 結果を返す（処理に成功した場合も失敗した場合も）
      try {
        const response = NextResponse.json(processedImage);
        return response;
      } catch (jsonError) {
        console.error('JSONレスポンス生成エラー:', jsonError);
        // JSONエンコードエラーが発生した場合のフォールバック
        return new Response(JSON.stringify({
          original: image,
          thumbnail: image,
          error: true,
          message: 'JSONレスポンス生成に失敗しましたが、元の画像をそのまま返却します'
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      // 最終的なエラーハンドリング
      console.error('画像処理の最終段階でエラーが発生しました:', error);

      // エラーの詳細情報を含める
      const errorMessage = error instanceof Error ? error.message : '不明なエラー';
      const errorStack = error instanceof Error ? error.stack : '';
      console.error('エラー詳細:', errorMessage);
      console.error('スタックトレース:', errorStack);

      // 最終手段として、元の画像をそのまま返す
      try {
        return NextResponse.json({
          original: image,
          thumbnail: image,
          error: true,
          message: '画像処理に失敗しましたが、元の画像をそのまま使用します'
        });
      } catch (finalError) {
        // 本当に何も返せない場合のみエラーレスポンスを返す
        return NextResponse.json(
          {
            error: 'Image processing failed',
            message: `画像処理中に致命的なエラーが発生しました: ${errorMessage}`,
            details: process.env.NODE_ENV === 'development' ? errorStack : undefined
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error('APIエラー:', error);
    return NextResponse.json(
      { error: 'Server error' },
      { status: 500 }
    );
  }
}
