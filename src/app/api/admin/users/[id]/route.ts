import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema for update
const updateUserSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().email().optional(),
  password: z.string().min(8).optional().or(z.string().max(0)),
  bio: z.string().max(500).optional(),
  profileImage: z.string().optional(),
  detailedProfile: z.string().max(2000).optional(),
  isAdmin: z.boolean().optional(),
});

// Route parameter type

// GET /api/admin/users/[id] - Get a single user
export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('ユーザー詳細APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const user = await prisma.user.findUnique({
      where: {
        id: params.id,
      },
      select: {
        id: true,
        username: true,
        email: true,
        bio: true,
        profileImage: true,
        detailedProfile: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/users/[id] - Update a user
export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('ユーザー更新APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', { ...body, password: body.password ? '********' : undefined });

    // Validate request body
    const validation = updateUserSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if email or username is already taken by another user
    if (body.email || body.username) {
      const duplicateUser = await prisma.user.findFirst({
        where: {
          OR: [
            body.email ? { email: body.email } : {},
            body.username ? { username: body.username } : {},
          ],
          NOT: {
            id: params.id,
          },
        },
      });

      if (duplicateUser) {
        return NextResponse.json(
          { error: 'Email or username is already taken' },
          { status: 409 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (body.username) updateData.username = body.username;
    if (body.email) updateData.email = body.email;
    if (body.bio !== undefined) updateData.bio = body.bio;
    if (body.profileImage !== undefined) updateData.profileImage = body.profileImage;
    if (body.detailedProfile !== undefined) updateData.detailedProfile = body.detailedProfile;
    if (body.isAdmin !== undefined) updateData.isAdmin = body.isAdmin;
    if (body.password) {
      updateData.password = await bcrypt.hash(body.password, 10);
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: {
        id: params.id,
      },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        bio: true,
        profileImage: true,
        detailedProfile: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete a user
export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params;
  try {
    console.log('ユーザー削除APIが呼び出されました:', params.id);

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: {
        id: params.id,
      },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // トランザクションを使用して、ユーザーとその記事を削除
    await prisma.$transaction(async (tx) => {
      // まず、ユーザーが作成した記事を削除
      await tx.post.deleteMany({
        where: {
          authorId: params.id,
        },
      });

      // 次に、ユーザーを削除
      await tx.user.delete({
        where: {
          id: params.id,
        },
      });
    });

    return NextResponse.json(
      { message: 'ユーザーが正常に削除されました' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting user:', error);

    // Prismaのエラーかどうかをチェック
    if (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      error.code === 'P2003'
    ) {
      return NextResponse.json(
        { error: 'このユーザーは記事を投稿しています。ユーザーを削除する前に、記事を削除するか、別のユーザーに移行してください。' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'ユーザーの削除に失敗しました' },
      { status: 500 }
    );
  }
}
