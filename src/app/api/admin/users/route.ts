import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';
import { z } from 'zod';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Validation schema
const userSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8),
  bio: z.string().max(500).optional(),
  profileImage: z.string().optional(),
  detailedProfile: z.string().max(2000).optional(),
  isAdmin: z.boolean().optional(),
});

// GET /api/admin/users - Get all users
export async function GET(request: Request) {
  try {
    console.log('ユーザー一覧APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    console.log('ユーザーデータを取得します');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        bio: true,
        profileImage: true,
        detailedProfile: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    console.log(`${users.length}件のユーザーを取得しました`);
    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create a new user
export async function POST(request: Request) {
  try {
    console.log('ユーザー作成APIが呼び出されました');

    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      console.log('認証エラー:', auth.error);
      return createAuthErrorResponse(auth.error);
    }

    const body = await request.json();
    console.log('リクエストボディ:', { ...body, password: '********' });

    // Validate request body
    const validation = userSchema.safeParse(body);
    if (!validation.success) {
      console.log('バリデーションエラー:', validation.error);
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { username, email, password, bio, profileImage, detailedProfile, isAdmin } = body;

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username },
        ],
      },
    });

    if (existingUser) {
      console.log('同じメールアドレスまたはユーザー名のユーザーが既に存在します:', { email, username });
      return NextResponse.json(
        { error: 'User with this email or username already exists' },
        { status: 409 }
      );
    }

    // Hash password
    console.log('パスワードをハッシュ化します');
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    console.log('ユーザーを作成します:', { username, email });
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        bio,
        profileImage,
        detailedProfile,
        isAdmin: isAdmin ?? false,
      },
      select: {
        id: true,
        username: true,
        email: true,
        bio: true,
        profileImage: true,
        detailedProfile: true,
        isAdmin: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log('ユーザーが作成されました:', user.id);
    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
