import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// API設定
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    // 認証チェック
    const session = await getServerSession(authOptions);
    
    // 認証されていない場合は401エラーを返す
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 環境変数の状態を確認（機密情報は隠す）
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      DATABASE_URL_EXISTS: !!process.env.DATABASE_URL,
      DATABASE_URL_LENGTH: process.env.DATABASE_URL ? process.env.DATABASE_URL.length : 0,
      DATABASE_URL_PREVIEW: process.env.DATABASE_URL 
        ? `${process.env.DATABASE_URL.split('@')[0].split(':')[0]}:***@${process.env.DATABASE_URL.split('@')[1] || 'unknown'}`
        : 'not set',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
      PORT: process.env.PORT,
      // その他の環境変数（機密情報は含めない）
    };

    // プロセス情報
    const processInfo = {
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      memoryUsage: process.memoryUsage(),
    };

    // レスポンスを返す
    return NextResponse.json({
      success: true,
      message: 'Debug information',
      environment: envInfo,
      process: processInfo,
      session: {
        exists: !!session,
        user: session?.user ? {
          id: session.user.id,
          name: session.user.name,
          email: session.user.email,
        } : null,
      },
    });
  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
