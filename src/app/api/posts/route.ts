import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// このルートを動的に処理するように指定
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured') === 'true';
    const categorySlug = searchParams.get('category');
    const subCategorySlug = searchParams.get('subcategory');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // 検索条件の構築
    const where: any = {
      published: true,
    };

    // カテゴリーフィルター
    if (categorySlug) {
      where.category = {
        slug: categorySlug,
      };
    }

    // サブカテゴリーフィルター
    if (subCategorySlug) {
      where.subCategory = {
        slug: subCategorySlug,
      };
    }

    // 注目記事フィルター
    if (featured) {
      // featuredフラグが設定されている記事を取得
      where.featured = true;
    }

    const posts = await prisma.post.findMany({
      where,
      orderBy: {
        publishedAt: 'desc',
      },
      take: limit,
      skip,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        subCategory: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });

    const totalPosts = await prisma.post.count({
      where,
    });

    return NextResponse.json({
      posts,
      meta: {
        total: totalPosts,
        page,
        limit,
        totalPages: Math.ceil(totalPosts / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}
