import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// このルートを動的に処理するように指定
export const dynamic = 'force-dynamic';

export async function GET(
  request: Request,
  context: { params: Promise<{ slug: string }> }
) {
  const params = await context.params;
  try {
    const slug = params.slug;

    const post = await prisma.post.findUnique({
      where: {
        slug,
      },
      include: {
        category: true,
        subCategory: true,
        images: true,
        user: {
          select: {
            id: true,
            username: true,
            bio: true,
          },
        },
      },
    });

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // 関連記事を取得（同じカテゴリーの他の記事）
    const relatedPosts = await prisma.post.findMany({
      where: {
        categoryId: post.categoryId,
        id: {
          not: post.id, // 現在の記事を除外
        },
        published: true,
      },
      take: 3,
      orderBy: {
        publishedAt: 'desc',
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featuredImage: true,
        publishedAt: true,
        category: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    return NextResponse.json({
      post,
      relatedPosts,
    });
  } catch (error) {
    console.error('Error fetching post:', error);
    return NextResponse.json(
      { error: 'Failed to fetch post' },
      { status: 500 }
    );
  }
}
