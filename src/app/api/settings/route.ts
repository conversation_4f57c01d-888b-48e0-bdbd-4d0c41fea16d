import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// このルートを動的に処理するように指定
export const dynamic = 'force-dynamic';

// GET /api/settings - Get public settings
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const keys = searchParams.get('keys');

    // If keys parameter is provided, return only those settings
    if (keys) {
      const keyList = keys.split(',');
      const settings = await prisma.siteSettings.findMany({
        where: {
          key: {
            in: keyList,
          },
        },
      });

      // Convert to key-value object for easier consumption
      const settingsObject = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, string>);

      return NextResponse.json(settingsObject);
    }

    // Otherwise return all public settings
    const settings = await prisma.siteSettings.findMany({
      orderBy: {
        key: 'asc',
      },
    });

    // Convert to key-value object for easier consumption
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}
