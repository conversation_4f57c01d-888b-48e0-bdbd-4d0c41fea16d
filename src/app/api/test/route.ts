import { NextResponse } from 'next/server';

// API設定
export const maxDuration = 60; // 60秒
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    console.log("=== /api/test エンドポイントが呼び出されました ===");
    
    // メモリ使用状況をログに記録
    try {
      const memoryUsage = process.memoryUsage();
      console.log("メモリ使用状況:", {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`, // Resident Set Size
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`, // V8エンジンの合計ヒープサイズ
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`, // V8エンジンの使用中ヒープサイズ
        external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`, // V8エンジン外のメモリ使用量
      });
    } catch (memoryError) {
      console.log("メモリ使用状況の取得に失敗:", memoryError);
    }

    return NextResponse.json({
      success: true,
      message: 'Test API is working',
      timestamp: new Date().toISOString()
    });
  } catch (error: unknown) {
    console.error('Test API error:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({
      error: 'Test API failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
