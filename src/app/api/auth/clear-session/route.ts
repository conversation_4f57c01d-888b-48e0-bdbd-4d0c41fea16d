import { NextResponse } from 'next/server';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * セッションクリア関数
 * Next.js 15.3.2とNextAuth 4.24.11に対応
 */
async function clearSession(request: Request) {
  try {
    // NextAuth関連のCookie名（一般的に使用されるもの）
    const authCookieNames = [
      // 標準的なNextAuthクッキー
      'next-auth.session-token',
      'next-auth.callback-url',
      'next-auth.csrf-token',
      // セキュアバージョン
      '__Secure-next-auth.session-token',
      '__Secure-next-auth.callback-url',
      '__Secure-next-auth.csrf-token',
      // ホスト固定バージョン
      '__Host-next-auth.csrf-token',
      '__Host-next-auth.session-token',
      // ID関連
      'next-auth.pkce.code_verifier',
      'next-auth.pkce.state',
      // バックアップ用（セッションIDなど）
      '.authjs.session-token',
      'authjs.session-token',
      // セッション関連
      'sid',
      'session',
      'session.sig',
    ];
    
    console.log('Clear session endpoint called', {
      url: request.url,
      method: request.method,
      cookiesToClear: authCookieNames,
    });
    
    // レスポンスを作成
    const response = NextResponse.json({
      success: true,
      message: 'Session cleared',
      clearedCookies: authCookieNames,
    });
    
    // キャッシュ防止ヘッダーを追加
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('Surrogate-Control', 'no-store');
    
    // 各Cookieを削除するためのSet-Cookieヘッダーを追加
    for (const cookieName of authCookieNames) {
      // 複数のパスとドメイン設定でクッキーを削除
      // パス "/"
      response.headers.append('Set-Cookie', 
        `${cookieName}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Max-Age=0; HttpOnly; SameSite=Lax`
      );
      
      // パス "/admin"
      response.headers.append('Set-Cookie', 
        `${cookieName}=; Path=/admin; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Max-Age=0; HttpOnly; SameSite=Lax`
      );
      
      // 特定のホスト名（リクエストから取得）
      const url = new URL(request.url);
      if (url.hostname) {
        // 通常のドメイン
        response.headers.append('Set-Cookie', 
          `${cookieName}=; Domain=${url.hostname}; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Max-Age=0; HttpOnly; SameSite=Lax`
        );
        
        // サブドメインを含むドメイン
        const domain = url.hostname.split('.').slice(-2).join('.');
        if (domain && domain.includes('.')) {
          response.headers.append('Set-Cookie', 
            `${cookieName}=; Domain=.${domain}; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Max-Age=0; HttpOnly; SameSite=Lax`
          );
        }
      }
    }
    
    return response;
  } catch (error) {
    console.error('Error clearing session:', error);
    
    const errorResponse = NextResponse.json({
      success: false,
      error: 'Failed to clear session',
      message: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
    
    // エラーの場合もキャッシュ防止ヘッダーを追加
    errorResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    errorResponse.headers.set('Pragma', 'no-cache');
    errorResponse.headers.set('Expires', '0');
    
    return errorResponse;
  }
}

// すべてのHTTPメソッドをサポート
export async function GET(request: Request) {
  return clearSession(request);
}

export async function POST(request: Request) {
  return clearSession(request);
}

export async function OPTIONS(request: Request) {
  // OPTIONSリクエストに対応してCORSをサポート
  const response = NextResponse.json({ success: true });
  response.headers.set('Allow', 'GET, POST, OPTIONS');
  
  // キャッシュ防止ヘッダーを追加
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');
  
  return response;
}
