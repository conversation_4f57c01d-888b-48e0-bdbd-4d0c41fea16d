import { NextResponse } from 'next/server';

// API設定
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    // リクエストURLからエラー情報を取得
    const { searchParams } = new URL(request.url);
    const error = searchParams.get('error');
    
    console.log('Auth error endpoint called', {
      error,
      url: request.url,
      headers: Object.fromEntries(request.headers),
    });
    
    // エラー情報を返す
    return NextResponse.json({
      error: error || 'Unknown error',
      message: getErrorMessage(error),
    });
  } catch (error) {
    console.error('Error in auth error endpoint:', error);
    
    // エラーが発生した場合でも200 OKを返す
    return NextResponse.json({
      error: 'Internal error',
      message: 'An unexpected error occurred',
    }, { status: 200 });
  }
}

// エラーメッセージを取得する関数
function getErrorMessage(errorCode: string | null): string {
  switch (errorCode) {
    case 'CredentialsSignin':
      return 'ログイン情報が正しくありません。メールアドレスとパスワードを確認してください。';
    case 'SessionRequired':
      return 'このページにアクセスするにはログインが必要です。';
    case 'AccessDenied':
      return 'アクセスが拒否されました。';
    case 'Verification':
      return '認証に失敗しました。';
    case 'OAuthSignin':
    case 'OAuthCallback':
    case 'OAuthCreateAccount':
    case 'EmailCreateAccount':
    case 'Callback':
    case 'OAuthAccountNotLinked':
    case 'EmailSignin':
    case 'CredentialsSignup':
    case 'Default':
    default:
      return '認証中にエラーが発生しました。しばらくしてからもう一度お試しください。';
  }
}
