import { NextResponse } from 'next/server';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';
import { prisma } from '@/lib/prisma';

// API設定
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// GET /api/auth/session - Get current session information
export async function GET(request: Request) {
  // デバッグ情報を追加
  console.log('Custom session endpoint called', {
    url: request.url,
    method: request.method,
    headers: Object.fromEntries(request.headers),
  });
  try {
    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      return NextResponse.json(
        {
          authenticated: false,
          message: 'No active session'
        },
        { status: 200 }
      );
    }

    // Get user ID from token
    const userId = auth.userId;

    // Check if user exists in database
    let userExists = false;
    let userInfo = null;

    if (userId) {
      const user = await prisma.user.findUnique({
        where: {
          id: userId
        },
        select: {
          id: true,
          username: true,
          email: true,
          isAdmin: true
        }
      });

      userExists = !!user;
      userInfo = user;
    }

    return NextResponse.json({
      authenticated: true,
      session: {
        user: {
          id: userId,
          name: userInfo?.username,
          email: userInfo?.email
        }
      },
      userExists,
      userInfo,
      validSession: userExists,
      isAdmin: userInfo?.isAdmin || false
    });
  } catch (error) {
    console.error('Error fetching session:', error);

    // エラーの詳細情報をログに出力
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }

    // エラーが発生した場合でも200 OKを返す（クライアント側でのエラーハンドリングを改善）
    return NextResponse.json(
      {
        authenticated: false,
        error: 'Failed to fetch session information',
        userExists: false,
        userInfo: null,
        validSession: false
      },
      { status: 200 }
    );
  }
}
