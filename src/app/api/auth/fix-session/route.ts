import { NextResponse } from 'next/server';
import { authenticateRequest, createAuthErrorResponse } from '@/lib/auth-helpers';
import { prisma } from '@/lib/prisma';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// POST /api/auth/fix-session - Attempt to fix session by finding a valid user
export async function POST(request: Request) {
  try {
    // 認証の確認
    const auth = await authenticateRequest(request);
    console.log('認証結果:', auth);

    if (!auth.isAuthenticated) {
      return NextResponse.json(
        {
          success: false,
          message: 'No active session to fix'
        },
        { status: 400 }
      );
    }

    // Get user ID from token
    const userId = auth.userId;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: 'No user ID in session'
        },
        { status: 400 }
      );
    }

    // Check if user exists in database
    const user = await prisma.user.findUnique({
      where: {
        id: userId
      }
    });

    if (user) {
      return NextResponse.json({
        success: true,
        message: 'Session is already valid',
        user: {
          id: user.id,
          username: user.username,
          email: user.email
        }
      });
    }

    // User doesn't exist, try to find an admin user
    const adminUser = await prisma.user.findFirst({
      where: {
        isAdmin: true
      }
    });

    if (adminUser) {
      // We found an admin user, but we can't actually update the session here
      // The user will need to log out and log back in
      return NextResponse.json({
        success: false,
        message: 'Session contains invalid user ID. Please log out and log in again as an admin user.',
        suggestedUser: {
          username: adminUser.username,
          email: adminUser.email
        }
      });
    }

    // No admin user found, try to find any user
    const anyUser = await prisma.user.findFirst();

    if (anyUser) {
      return NextResponse.json({
        success: false,
        message: 'Session contains invalid user ID. Please log out and log in again.',
        suggestedUser: {
          username: anyUser.username,
          email: anyUser.email
        }
      });
    }

    // No users found at all
    return NextResponse.json({
      success: false,
      message: 'No valid users found in the database. Please create a user first.'
    });

  } catch (error) {
    console.error('Error fixing session:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fix session'
      },
      { status: 500 }
    );
  }
}
