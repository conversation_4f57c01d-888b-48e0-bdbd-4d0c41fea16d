import NextAuth from "next-auth";
import { authOptions } from '@/lib/auth';

// API設定 - 常に動的に実行され、キャッシュされないようにする
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// デバッグ情報を追加
console.log('NextAuth Route Handler Initialized', {
  NODE_ENV: process.env.NODE_ENV,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
});

// Next.js 15.3.2対応のNextAuthハンドラ
const handler = NextAuth(authOptions);

// 標準のHTTPメソッドをエクスポート
export { handler as GET, handler as POST };

// 追加のメソッド（CSRF対策などに必要）
export const HEAD = handler;
export const OPTIONS = handler;

// セッション管理に関連する追加メソッド
export const PUT = handler;
export const DELETE = handler;

// 特殊なケースのために追加のルート処理
export const PATCH = (req: Request) => {
  // キャッシュヘッダーを設定
  const response = handler(req);
  response.then((res: Response) => {
    res.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.headers.set('Pragma', 'no-cache');
    res.headers.set('Expires', '0');
    return res;
  });
  return response;
};
