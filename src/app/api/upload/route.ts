import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { optimizeImage, createThumbnail, base64ToBuffer } from '@/lib/imageUtils';

// API設定
export const maxDuration = 120; // 120秒に延長
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// 設定ファイルをインポート（Next.js App Routerでは直接使用しないが、設定を明示するために記述）
import './route.config';

export async function POST(request: Request) {
  try {
    console.log("=== /api/upload エンドポイントが呼び出されました ===");

    // 認証チェック
    const session = await getServerSession(authOptions);
    console.log("Session status:", session ? "Authenticated" : "Not authenticated");

    if (!session) {
      console.error("認証エラー: セッションがありません");
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log("認証成功: ユーザー", session.user?.name || session.user?.email || "不明");

    try {
      const formData = await request.formData();
      console.log("FormDataの取得に成功");

      const file = formData.get('file') as File;
      // 回転角度を取得（指定がない場合は0）
      const rotateAngle = formData.get('rotate') ? parseInt(formData.get('rotate') as string, 10) : 0;

      if (!file) {
        console.error("ファイルがアップロードされていません");
        return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
      }

      // リクエストヘッダーのサイズ確認
      const contentLength = request.headers.get('content-length');
      if (contentLength) {
        const sizeInMB = parseInt(contentLength) / (1024 * 1024);
        console.log(`リクエスト全体のサイズ: ${sizeInMB.toFixed(2)}MB`);

        // 5MBを超えるリクエストは拒否（Nginxの制限を考慮）
        if (sizeInMB > 5) {
          console.error(`リクエストサイズが大きすぎます: ${sizeInMB.toFixed(2)}MB`);
          return NextResponse.json({
            error: 'Request too large',
            message: `リクエストサイズが大きすぎます（${sizeInMB.toFixed(2)}MB）。5MB以下にしてください。`
          }, { status: 413 });
        }
      }

      console.log("アップロードされたファイル情報:", {
        fileName: file.name,
        fileType: file.type,
        fileSize: `${(file.size / 1024).toFixed(2)}KB`,
        fileSizeBytes: file.size,
        lastModified: file.lastModified ? new Date(file.lastModified).toISOString() : "不明",
        rotateAngle
      });

      // ファイルサイズの検証（5MB以下）
      if (file.size > 5 * 1024 * 1024) {
        console.error(`ファイルサイズが大きすぎます: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
        return NextResponse.json({
          error: 'File too large',
          message: `ファイルサイズが大きすぎます（${(file.size / (1024 * 1024)).toFixed(2)}MB）。5MB以下の画像を選択してください。`
        }, { status: 413 });
      }

      // ファイルタイプの検証
      const fileType = file.type;
      if (!fileType.startsWith('image/')) {
        console.error("アップロードされたファイルは画像ではありません:", fileType);
        return NextResponse.json({
          error: 'Invalid file type',
          message: 'アップロードされたファイルは画像ではありません。JPG、PNG、GIF、WEBPなどの画像ファイルを選択してください。'
        }, { status: 400 });
      }

      try {
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        console.log("変換されたバッファサイズ:", buffer.length);

        if (buffer.length === 0) {
          console.error("エラー: 空のバッファ（ファイルサイズが0）");
          return NextResponse.json({ error: 'Empty file uploaded' }, { status: 400 });
        }

        // メモリ使用状況をログに記録（開発環境用）
        try {
          const memoryUsage = process.memoryUsage();
          console.log("メモリ使用状況:", {
            rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`, // Resident Set Size
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`, // V8エンジンの合計ヒープサイズ
            heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`, // V8エンジンの使用中ヒープサイズ
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`, // V8エンジン外のメモリ使用量
          });
        } catch (memoryError) {
          console.log("メモリ使用状況の取得に失敗:", memoryError);
        }

        // 画像の最適化とサムネイル作成（回転を適用）
        console.log("画像の最適化を開始...");
        try {
          // 最適化画像の生成
          console.log("元画像の最適化を開始...");
          const imageData = await optimizeImage(buffer, rotateAngle);
          console.log("最適化された画像データ長:", imageData.length);

          // サムネイル画像の生成
          console.log("サムネイル画像の生成を開始...");
          const thumbnailData = await createThumbnail(buffer, rotateAngle);
          console.log("サムネイル画像データ長:", thumbnailData.length);

          // 結果の検証
          if (!imageData || imageData.length < 100) {
            throw new Error("最適化された画像データが無効です");
          }

          if (!thumbnailData || thumbnailData.length < 100) {
            throw new Error("サムネイル画像データが無効です");
          }

          const responseData = {
            success: true,
            imageData,
            thumbnailData
          };

          console.log("アップロード処理完了:", {
            success: true,
            imageDataLength: imageData.length,
            thumbnailDataLength: thumbnailData.length
          });

          return NextResponse.json(responseData);
        } catch (imageProcessError) {
          console.error("画像処理エラー:", imageProcessError);
          console.error("エラー詳細:", imageProcessError instanceof Error ? imageProcessError.stack : "スタックトレースなし");

          // クライアントに返すエラーメッセージ
          return NextResponse.json({
            error: 'Image processing failed',
            message: '画像の処理中にエラーが発生しました。別の画像を試すか、サイズの小さい画像を使用してください。',
            details: imageProcessError instanceof Error ? imageProcessError.message : String(imageProcessError)
          }, { status: 500 });
        }
      } catch (bufferError) {
        console.error("バッファ変換エラー:", bufferError);
        return NextResponse.json({
          error: 'Buffer conversion failed',
          details: bufferError instanceof Error ? bufferError.message : String(bufferError)
        }, { status: 500 });
      }
    } catch (formDataError) {
      console.error("FormData処理エラー:", formDataError);
      return NextResponse.json({
        error: 'FormData processing failed',
        details: formDataError instanceof Error ? formDataError.message : String(formDataError)
      }, { status: 500 });
    }
  } catch (error: unknown) {
    console.error('Upload error:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({
      error: 'Failed to process image',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
