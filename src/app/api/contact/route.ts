import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

// バリデーションスキーマ
const contactSchema = z.object({
  name: z.string().min(1, '名前は必須です').max(100),
  email: z.string().min(1, 'メールアドレスは必須です').email('有効なメールアドレスを入力してください'),
  subject: z.string().min(1, '件名は必須です').max(200),
  message: z.string().min(1, 'メッセージは必須です').max(5000),
});

// POST /api/contact - お問い合わせメッセージを送信
export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // バリデーション
    const validation = contactSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }
    
    const { name, email, subject, message } = body;
    
    // データベースに保存
    const contactMessage = await prisma.contactMessage.create({
      data: {
        name,
        email,
        subject,
        message,
      },
    });
    
    return NextResponse.json({
      success: true,
      message: 'お問い合わせを受け付けました',
      id: contactMessage.id,
    });
  } catch (error) {
    console.error('Error saving contact message:', error);
    return NextResponse.json(
      { error: 'お問い合わせの送信に失敗しました' },
      { status: 500 }
    );
  }
}
