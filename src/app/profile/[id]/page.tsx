import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import { formatDate } from '@/lib/utils';
import { prisma } from '@/lib/prisma';

// This is a wrapper component that handles the Promise-based params
export default async function ProfilePageWrapper(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;

  return <ProfilePage params={params} />;
}

// This is the actual component that renders the page
async function ProfilePage({ params }: { params: { id: string } }) {
  // ユーザー情報を取得
  const user = await prisma.user.findUnique({
    where: {
      id: params.id,
    },
    select: {
      id: true,
      username: true,
      bio: true,
      profileImage: true,
      detailedProfile: true,
      isAdmin: true,
      createdAt: true,
      posts: {
        where: {
          published: true,
        },
        orderBy: {
          publishedAt: 'desc',
        },
        take: 6,
        select: {
          id: true,
          title: true,
          slug: true,
          excerpt: true,
          featuredImage: true,
          publishedAt: true,
          category: {
            select: {
              name: true,
              slug: true,
            },
          },
        },
      },
    },
  });

  // ユーザーが見つからない場合
  if (!user) {
    return notFound();
  }

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="relative h-80">
          <div className="absolute inset-0 z-0">
            <div className="relative w-full h-full">
              <Image
                src="/images/dogu-figure.jpg"
                alt="プロフィール背景"
                fill
                className="object-cover brightness-50"
                priority
              />
            </div>
          </div>
          <div className="container-custom relative z-10 h-full flex flex-col justify-end pb-12">
            <div className="text-white max-w-3xl">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-serif font-medium mb-4">
                {user.username}のプロフィール
              </h1>
              <p className="text-xl text-primary-100">
                {user.isAdmin ? 'サイト管理者' : '投稿者'}
              </p>
            </div>
          </div>
        </section>

        {/* Profile Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/3 p-8 flex flex-col items-center text-center border-b md:border-b-0 md:border-r border-gray-200">
                  <div className="w-40 h-40 rounded-full mb-6 overflow-hidden relative profile-image">
                    <Image
                      src={user.profileImage || "/images/owl-icon.png"}
                      alt={user.username}
                      fill
                      sizes="(max-width: 768px) 160px, 160px"
                      className="object-cover"
                    />
                  </div>
                  <h2 className="text-2xl font-serif font-medium mb-2">{user.username}</h2>
                  <p className="text-primary-600 mb-4">
                    {user.isAdmin ? 'サイト管理者' : '投稿者'}
                  </p>
                  <div className="text-sm text-gray-500">
                    <p>登録日: {formatDate(user.createdAt)}</p>
                    <p>投稿数: {user.posts.length}</p>
                  </div>
                </div>
                <div className="md:w-2/3 p-8">
                  <h3 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">プロフィール</h3>
                  <div className="prose max-w-none">
                    <p className="mb-6">{user.bio || "紹介文はまだ登録されていません。"}</p>
                    
                    {user.detailedProfile && (
                      <div>
                        <h4 className="text-lg font-medium mb-3">詳細プロフィール</h4>
                        <div className="whitespace-pre-wrap">
                          {user.detailedProfile}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* User Posts Section */}
        {user.posts.length > 0 && (
          <section className="py-12 bg-gray-50">
            <div className="container-custom">
              <h2 className="text-2xl font-serif font-medium mb-8">{user.username}の記事</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {user.posts.map((post) => (
                  <div key={post.id} className="card h-full flex flex-col">
                    <div className="relative h-48 w-full">
                      <Image
                        src={post.featuredImage || "/images/dogu-figure.jpg"}
                        alt={post.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        className="object-cover"
                      />
                    </div>
                    <div className="p-4 flex-grow flex flex-col">
                      <div className="mb-2">
                        <Link
                          href={`/categories/${post.category.slug}`}
                          className="text-xs font-medium text-accent-600 hover:text-accent-800"
                        >
                          {post.category.name}
                        </Link>
                      </div>
                      <h3 className="text-lg font-serif font-medium mb-2">
                        <Link href={`/posts/${post.slug}`} className="text-primary-900 hover:text-accent-700">
                          {post.title}
                        </Link>
                      </h3>
                      {post.excerpt && (
                        <p className="text-primary-600 text-sm mb-3 flex-grow">
                          {post.excerpt.length > 100 ? `${post.excerpt.substring(0, 100)}...` : post.excerpt}
                        </p>
                      )}
                      <div className="mt-auto">
                        {post.publishedAt && (
                          <p className="text-xs text-gray-500 mb-2">
                            {formatDate(post.publishedAt)}
                          </p>
                        )}
                        <Link
                          href={`/posts/${post.slug}`}
                          className="inline-flex items-center text-accent-600 hover:text-accent-800 text-sm"
                        >
                          続きを読む
                          <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}
      </main>

      <Footer />
    </>
  );
}
