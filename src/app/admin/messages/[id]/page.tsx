'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { FiArrowLeft, FiTrash2 } from 'react-icons/fi';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

interface Message {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  isRead: boolean;
  readAt: string | null;
  createdAt: string;
}

export default function MessageDetailPage() {
  const router = useRouter();
  const params = useParams();
  const messageId = params.id as string;
  const [message, setMessage] = useState<Message | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchMessage = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/messages/${messageId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('メッセージが見つかりませんでした');
          }
          throw new Error('メッセージの取得に失敗しました');
        }

        const data = await response.json();
        setMessage(data);

        // メッセージを既読に設定
        if (!data.isRead) {
          markAsRead();
        }
      } catch (error) {
        console.error('Error fetching message:', error);
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    if (messageId) {
      fetchMessage();
    }
  }, [messageId]);

  const markAsRead = async () => {
    try {
      const response = await fetch(`/api/admin/messages/${messageId}/read`, {
        method: 'PATCH',
      });

      if (!response.ok) {
        console.error('Failed to mark message as read');
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('このメッセージを削除してもよろしいですか？')) {
      return;
    }

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/admin/messages/${messageId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('メッセージの削除に失敗しました');
      }

      toast.success('メッセージを削除しました');
      router.push('/admin/messages');
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error(error instanceof Error ? error.message : 'メッセージの削除に失敗しました');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <Link
          href="/admin/messages"
          className="inline-flex items-center text-primary-600 hover:text-primary-800"
        >
          <FiArrowLeft className="mr-2" />
          メッセージ一覧に戻る
        </Link>
      </div>
    );
  }

  if (!message) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <p className="text-gray-600 mb-4">メッセージが見つかりませんでした</p>
        <Link
          href="/admin/messages"
          className="inline-flex items-center text-primary-600 hover:text-primary-800"
        >
          <FiArrowLeft className="mr-2" />
          メッセージ一覧に戻る
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">メッセージ詳細</h1>
        <div className="flex space-x-4">
          <Link
            href="/admin/messages"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FiArrowLeft className="mr-2" />
            戻る
          </Link>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            <FiTrash2 className="mr-2" />
            削除
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h2 className="text-sm font-medium text-gray-500">送信者</h2>
              <p className="mt-1 text-lg">{message.name}</p>
            </div>
            <div>
              <h2 className="text-sm font-medium text-gray-500">メールアドレス</h2>
              <p className="mt-1 text-lg">{message.email}</p>
            </div>
            <div>
              <h2 className="text-sm font-medium text-gray-500">件名</h2>
              <p className="mt-1 text-lg">{message.subject}</p>
            </div>
            <div>
              <h2 className="text-sm font-medium text-gray-500">受信日時</h2>
              <p className="mt-1 text-lg">{formatDate(message.createdAt)}</p>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <h2 className="text-sm font-medium text-gray-500 mb-3">メッセージ内容</h2>
            <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap">
              {message.message}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
