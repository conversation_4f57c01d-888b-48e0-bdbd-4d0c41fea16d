'use client';

import { ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import AdminHeader from '@/components/admin/AdminHeader';
import AdminSidebar from '@/components/admin/AdminSidebar';
import { SidebarProvider } from '@/contexts/SidebarContext';
import { AdminAuthProvider } from '@/contexts/AdminAuthContext';

export default function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const pathname = usePathname();

  // 全体をAdminAuthProviderでラップ
  return (
    <AdminAuthProvider>
      {pathname === '/admin/login' ? (
        // ログインページの場合は、子コンポーネントのみを表示
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      ) : (
        // それ以外の管理画面ページの場合は、サイドバーとヘッダーを表示
        <SidebarProvider>
          <div className="min-h-screen bg-gray-50">
            <AdminHeader />
            <div className="flex">
              <AdminSidebar />
              <main className="flex-1 p-6">
                {children}
              </main>
            </div>
          </div>
        </SidebarProvider>
      )}
    </AdminAuthProvider>
  );
}
