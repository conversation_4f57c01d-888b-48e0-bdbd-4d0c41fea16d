'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { FaNewspaper, FaFolderOpen, FaUsers, FaPlus, FaEnvelope } from 'react-icons/fa';
import AdminProtectedRoute from '@/components/admin/AdminProtectedRoute';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAdminAuth();

  // 状態の初期化
  const [postsCount, setPostsCount] = useState<number>(0);
  const [categoriesCount, setCategoriesCount] = useState<number>(0);
  const [usersCount, setUsersCount] = useState<number>(0);
  const [messagesCount, setMessagesCount] = useState<number>(0);
  const [unreadMessagesCount, setUnreadMessagesCount] = useState<number>(0);
  const [recentPosts, setRecentPosts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 認証状態の確認
  useEffect(() => {
    console.log('ダッシュボード: 認証状態確認', { isAuthenticated, authLoading });

    // 認証されていない場合はログインページにリダイレクト
    if (!isAuthenticated && !authLoading) {
      console.log('認証されていません。ログインページにリダイレクトします。');
      router.push('/admin/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // データの取得
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // 認証されていない場合はデータ取得をスキップ
        if (!isAuthenticated) {
          console.log('認証されていないため、データ取得をスキップします');
          return;
        }

        setIsLoading(true);
        console.log('ダッシュボードデータの取得を開始します');

        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        console.log('トークンの有無:', !!token);

        if (!token) {
          console.error('認証トークンがありません');
          return;
        }

        // ダッシュボードデータを取得するAPIを呼び出す
        console.log('ダッシュボードAPIを呼び出します');
        const response = await fetch('/api/admin/dashboard', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          // キャッシュを無効化
          cache: 'no-store',
        });

        console.log('ダッシュボードAPIレスポンス:', { status: response.status, ok: response.ok });

        if (response.ok) {
          const data = await response.json();
          console.log('ダッシュボードデータを取得しました:', data);

          setPostsCount(data.postsCount);
          setCategoriesCount(data.categoriesCount);
          setUsersCount(data.usersCount);
          setMessagesCount(data.messagesCount);
          setUnreadMessagesCount(data.unreadMessagesCount);
          setRecentPosts(data.recentPosts);
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error('ダッシュボードデータの取得に失敗しました:', { status: response.status, error: errorData });

          // 認証エラーの場合はログインページにリダイレクト
          if (response.status === 401) {
            console.log('認証エラーのため、ログインページにリダイレクトします');
            router.push('/admin/login');
          }
        }
      } catch (error) {
        console.error('ダッシュボードデータの取得中にエラーが発生しました:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // 認証状態が変わったときにデータを再取得
    if (isAuthenticated && !authLoading) {
      fetchDashboardData();
    }
  }, [isAuthenticated, authLoading, router]);

  return (
    <AdminProtectedRoute>
      {isLoading ? (
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">ダッシュボード</h1>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                  <FaNewspaper className="text-xl" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">記事数</p>
                  <p className="text-2xl font-semibold text-gray-900">{postsCount}</p>
                </div>
              </div>
              <div className="mt-4">
                <Link
                  href="/admin/posts"
                  className="text-sm text-blue-500 hover:text-blue-700"
                >
                  すべての記事を表示
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 text-green-500">
                  <FaFolderOpen className="text-xl" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">カテゴリー数</p>
                  <p className="text-2xl font-semibold text-gray-900">{categoriesCount}</p>
                </div>
              </div>
              <div className="mt-4">
                <Link
                  href="/admin/categories"
                  className="text-sm text-green-500 hover:text-green-700"
                >
                  すべてのカテゴリーを表示
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                  <FaUsers className="text-xl" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">ユーザー数</p>
                  <p className="text-2xl font-semibold text-gray-900">{usersCount}</p>
                </div>
              </div>
              <div className="mt-4">
                <Link
                  href="/admin/users"
                  className="text-sm text-purple-500 hover:text-purple-700"
                >
                  すべてのユーザーを表示
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-amber-100 text-amber-500">
                  <FaEnvelope className="text-xl" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">お問い合わせ</p>
                  <p className="text-2xl font-semibold text-gray-900">{messagesCount}</p>
                  {unreadMessagesCount > 0 && (
                    <p className="text-sm font-medium text-red-500">未読: {unreadMessagesCount}</p>
                  )}
                </div>
              </div>
              <div className="mt-4">
                <Link
                  href="/admin/messages"
                  className="text-sm text-amber-500 hover:text-amber-700"
                >
                  すべてのメッセージを表示
                </Link>
              </div>
            </div>
          </div>

          {/* Recent Posts */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">最近の記事</h2>
              <Link
                href="/admin/posts/new"
                className="flex items-center text-sm text-primary-600 hover:text-primary-800"
              >
                <FaPlus className="mr-1" />
                新規作成
              </Link>
            </div>
            <div className="divide-y divide-gray-200">
              {recentPosts.length > 0 ? (
                recentPosts.map((post) => (
                  <div key={post.id} className="px-6 py-4">
                    <div className="flex justify-between">
                      <div>
                        <Link
                          href={`/admin/posts/${post.id}/edit`}
                          className="text-lg font-medium text-gray-900 hover:text-primary-600"
                        >
                          {post.title}
                        </Link>
                        <p className="text-sm text-gray-500">
                          作成者: {post.user.username} |
                          更新日: {new Date(post.updatedAt).toLocaleDateString('ja-JP')}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            post.published
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {post.published ? '公開中' : '下書き'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-4 text-center text-gray-500">
                  記事がありません
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </AdminProtectedRoute>
  );
}
