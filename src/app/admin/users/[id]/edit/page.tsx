'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import UserForm from '@/components/admin/UserForm';

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/admin/users/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('ユーザーの取得に失敗しました');
        }

        const data = await response.json();
        setUser(data);
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href="/admin/users"
            className="text-red-600 hover:text-red-800 font-medium"
          >
            ユーザー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">ユーザーが見つかりません</h1>
        <div className="mt-4">
          <Link
            href="/admin/users"
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            ユーザー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">ユーザー編集</h1>
        <p className="mt-1 text-sm text-gray-500">
          ユーザー情報を編集します。
        </p>
      </div>

      <UserForm user={user} isEditing />
    </div>
  );
}
