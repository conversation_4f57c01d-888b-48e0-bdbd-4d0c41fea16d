'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import PostForm from '@/components/admin/PostForm';

export default function EditPostPage() {
  const params = useParams();
  const router = useRouter();
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

  const [post, setPost] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/admin/posts/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('記事の取得に失敗しました');
        }

        const data = await response.json();

        // publishedAtをstring型に変換
        const formattedPost = {
          ...data,
          publishedAt: data.publishedAt ? new Date(data.publishedAt).toISOString() : null,
        };

        setPost(formattedPost);
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPost();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href="/admin/posts"
            className="text-red-600 hover:text-red-800 font-medium"
          >
            記事一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">記事が見つかりません</h1>
        <div className="mt-4">
          <Link
            href="/admin/posts"
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            記事一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">記事編集</h1>
        <p className="mt-1 text-sm text-gray-500">
          記事の内容を編集します。
        </p>
      </div>

      <PostForm post={post} isEditing />
    </div>
  );
}
