'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { FiPlus, FiEdit, FiTrash2, FiArrowLeft } from 'react-icons/fi';

export default function SubCategoriesPage() {
  const params = useParams();
  const router = useRouter();

  // Extract and type-check the parameter
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

  const [category, setCategory] = useState<any>(null);
  const [subCategories, setSubCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        // Fetch category
        const categoryResponse = await fetch(`/api/admin/categories/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!categoryResponse.ok) {
          if (categoryResponse.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('カテゴリーの取得に失敗しました');
        }

        const categoryData = await categoryResponse.json();
        // Extract category from the response
        setCategory(categoryData.category || categoryData);

        // Fetch subcategories
        const subCategoriesResponse = await fetch(`/api/admin/categories/${id}/subcategories`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!subCategoriesResponse.ok) {
          if (subCategoriesResponse.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('サブカテゴリーの取得に失敗しました');
        }

        const subCategoriesData = await subCategoriesResponse.json();
        setSubCategories(subCategoriesData);
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href="/admin/categories"
            className="text-red-600 hover:text-red-800 font-medium"
          >
            カテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">カテゴリーが見つかりません</h1>
        <div className="mt-4">
          <Link
            href="/admin/categories"
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            カテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Link
            href="/admin/categories"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
          >
            <FiArrowLeft className="mr-1" />
            カテゴリー一覧に戻る
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">
            {category.name} のサブカテゴリー
          </h1>
        </div>
        <Link
          href={`/admin/categories/${params.id}/subcategories/new`}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <FiPlus className="mr-2" />
          新規サブカテゴリー
        </Link>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {subCategories.length > 0 ? (
            subCategories.map((subCategory) => (
              <li key={subCategory.id}>
                <div className="px-6 py-4 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {subCategory.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      スラッグ: {subCategory.slug}
                    </p>
                    {subCategory.description && (
                      <p className="text-sm text-gray-500 mt-1">
                        {subCategory.description}
                      </p>
                    )}
                    <div className="mt-2 flex space-x-4 text-xs text-gray-500">
                      <span>記事数: {subCategory._count.posts}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      href={`/admin/categories/${params.id}/subcategories/${subCategory.id}/edit`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <FiEdit className="mr-1" />
                      編集
                    </Link>
                    <Link
                      href={`/admin/categories/${params.id}/subcategories/${subCategory.id}/delete`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <FiTrash2 className="mr-1" />
                      削除
                    </Link>
                  </div>
                </div>
              </li>
            ))
          ) : (
            <li className="px-6 py-4 text-center text-gray-500">
              サブカテゴリーが登録されていません
            </li>
          )}
        </ul>
      </div>
    </div>
  );
}
