'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiAlertTriangle, FiArrowLeft } from 'react-icons/fi';

export default function DeleteSubCategoryPage() {
  const params = useParams();
  const router = useRouter();

  // Extract and type-check the parameters
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  const subId = typeof params.subId === 'string' ? params.subId : Array.isArray(params.subId) ? params.subId[0] : '';

  const [subCategory, setSubCategory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSubCategory = async () => {
      try {
        const response = await fetch(`/api/admin/subcategories/${subId}`);

        if (!response.ok) {
          throw new Error('サブカテゴリーの取得に失敗しました');
        }

        const data = await response.json();
        setSubCategory(data);
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubCategory();
  }, [subId]);

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/subcategories/${subId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'サブカテゴリーの削除に失敗しました');
      }

      router.push(`/admin/categories/${id}/subcategories`);
      router.refresh();
    } catch (error) {
      setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href={`/admin/categories/${id}/subcategories`}
            className="text-red-600 hover:text-red-800 font-medium"
          >
            サブカテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!subCategory) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">サブカテゴリーが見つかりません</h1>
        <div className="mt-4">
          <Link
            href={`/admin/categories/${id}/subcategories`}
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            サブカテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Link
          href={`/admin/categories/${id}/subcategories`}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
        >
          <FiArrowLeft className="mr-1" />
          サブカテゴリー一覧に戻る
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">サブカテゴリー削除</h1>
      </div>

      <div className="bg-red-50 p-6 rounded-lg border border-red-200">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <FiAlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-red-800">
              サブカテゴリー「{subCategory.name}」を削除しますか？
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>
                このサブカテゴリーに関連付けられた記事のサブカテゴリー情報も削除されます。
                この操作は元に戻せません。
              </p>
              {subCategory._count?.posts > 0 && (
                <p className="mt-2 font-bold">
                  このサブカテゴリーには {subCategory._count.posts} 件の記事が関連付けられています。
                </p>
              )}
            </div>
            <div className="mt-4 flex space-x-3">
              <button
                type="button"
                onClick={handleDelete}
                disabled={isDeleting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                {isDeleting ? '削除中...' : '削除する'}
              </button>
              <Link
                href={`/admin/categories/${id}/subcategories`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                キャンセル
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
