'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiArrowLeft } from 'react-icons/fi';
import SubCategoryForm from '@/components/admin/SubCategoryForm';

export default function EditSubCategoryPage() {
  const params = useParams();
  const router = useRouter();

  // Extract and type-check the parameters
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  const subId = typeof params.subId === 'string' ? params.subId : Array.isArray(params.subId) ? params.subId[0] : '';

  const [category, setCategory] = useState<any>(null);
  const [subCategory, setSubCategory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        // Fetch category
        const categoryResponse = await fetch(`/api/admin/categories/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!categoryResponse.ok) {
          if (categoryResponse.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('カテゴリーの取得に失敗しました');
        }

        const categoryData = await categoryResponse.json();
        // Extract category from the response
        const categoryObj = categoryData.category || categoryData;

        // Fetch subcategory
        const subCategoryResponse = await fetch(`/api/admin/subcategories/${subId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!subCategoryResponse.ok) {
          if (subCategoryResponse.status === 401) {
            setError('認証エラー: 再ログインしてください');
            setIsLoading(false);
            return;
          }
          throw new Error('サブカテゴリーの取得に失敗しました');
        }

        const subCategoryData = await subCategoryResponse.json();

        // Check if subcategory belongs to category
        if (subCategoryData.categoryId !== id) {
          throw new Error('このサブカテゴリーは指定されたカテゴリーに属していません');
        }

        setCategory(categoryObj);
        setSubCategory(subCategoryData);
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id, subId]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href={`/admin/categories/${id}/subcategories`}
            className="text-red-600 hover:text-red-800 font-medium"
          >
            サブカテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!category || !subCategory) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">データが見つかりません</h1>
        <div className="mt-4">
          <Link
            href={`/admin/categories/${id}/subcategories`}
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            サブカテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Link
          href={`/admin/categories/${id}/subcategories`}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
        >
          <FiArrowLeft className="mr-1" />
          サブカテゴリー一覧に戻る
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">サブカテゴリー編集</h1>
        <p className="mt-1 text-sm text-gray-500">
          {category.name} のサブカテゴリー「{subCategory.name}」を編集します。
        </p>
      </div>

      <SubCategoryForm
        categoryId={id}
        subCategory={subCategory}
        isEditing={true}
      />
    </div>
  );
}
