'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiArrowLeft } from 'react-icons/fi';
import CategoryForm from '@/components/admin/CategoryForm';

export default function EditCategoryPage() {
  const params = useParams();
  const router = useRouter();

  // Extract and type-check the parameter
  const id = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';

  const [category, setCategory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          setError('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/admin/categories/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            setError('認証エラー: 再ログインしてください');
          } else {
            throw new Error('カテゴリーの取得に失敗しました');
          }
        } else {
          const data = await response.json();
          // Extract category from the response
          setCategory(data.category || data);
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : '予期せぬエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategory();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
        <p className="mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-red-800">エラー</h1>
        <p className="mt-2 text-red-700">{error}</p>
        <div className="mt-4">
          <Link
            href="/admin/categories"
            className="text-red-600 hover:text-red-800 font-medium"
          >
            カテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h1 className="text-xl font-bold text-yellow-800">カテゴリーが見つかりません</h1>
        <div className="mt-4">
          <Link
            href="/admin/categories"
            className="text-yellow-600 hover:text-yellow-800 font-medium"
          >
            カテゴリー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Link
          href="/admin/categories"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2"
        >
          <FiArrowLeft className="mr-1" />
          カテゴリー一覧に戻る
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">カテゴリー編集</h1>
        <p className="mt-1 text-sm text-gray-500">
          カテゴリー「{category.name}」を編集します。
        </p>
      </div>

      <CategoryForm
        category={category}
        isEditing={true}
      />
    </div>
  );
}
