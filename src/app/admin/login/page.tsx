'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

// このページを動的に処理するように指定
export const dynamic = 'force-dynamic';

export default function LoginPage() {
  const router = useRouter();
  const { login, isAuthenticated } = useAdminAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 既に認証されている場合はダッシュボードにリダイレクト
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/admin/dashboard');
    }
  }, [isAuthenticated, router]);

  // ログイン処理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      console.log('=== ログイン処理開始 ===');
      console.log('入力されたデータ:', { email, passwordLength: password.length });
      console.log('現在のURL:', window.location.href);
      console.log('現在の認証状態:', isAuthenticated);

      // 新しい認証システムを使用してログイン
      console.log('login関数を呼び出します...');
      const result = await login(email, password);
      console.log('=== login関数の結果 ===:', result);

      if (!result.success) {
        console.log('ログイン失敗:', result.error);
        setError(result.error || 'ログインに失敗しました。');
        setIsLoading(false);
      } else {
        console.log('=== ログイン成功 ===');
        console.log('認証状態の更新を待機...');
        
        // 認証状態の更新を待つ
        setTimeout(async () => {
          console.log('認証状態を再確認します...');
          
          // ローカルストレージとCookieの状態を確認
          const token = localStorage.getItem('adminToken');
          const cookieToken = document.cookie.split(';').find(row => row.trim().startsWith('adminToken='));
          
          console.log('保存されたトークン状況:', {
            localStorage: token ? 'あり' : 'なし',
            cookie: cookieToken ? 'あり' : 'なし'
          });
          
          console.log('ダッシュボードへリダイレクト開始...');
          
          try {
            console.log('Next.jsルーターでリダイレクト実行...');
            router.push('/admin/dashboard');
            
            // フォールバックリダイレクト
            setTimeout(() => {
              console.log('フォールバックリダイレクト実行');
              console.log('現在のURL:', window.location.href);
              if (window.location.pathname !== '/admin/dashboard') {
                window.location.href = '/admin/dashboard';
              }
            }, 1000);
            
          } catch (redirectError) {
            console.error('リダイレクト中にエラーが発生:', redirectError);
            window.location.href = '/admin/dashboard';
          }
        }, 100); // 100ms待機して認証状態の更新を待つ
      }
    } catch (error) {
      console.error('ログイン処理中に例外が発生:', error);
      console.error('エラーの詳細:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : 'スタックトレースなし'
      });
      setError(`ログイン中にエラーが発生しました: ${error instanceof Error ? error.message : String(error)}`);
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="relative w-20 h-20">
              <Image
                src="/images/dogu-logo.png"
                alt="造物者の空気感"
                fill
                sizes="80px"
                className="object-contain"
              />
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            管理者ログイン
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                メールアドレスまたはユーザー名
              </label>
              <input
                id="email-address"
                name="email"
                type="text"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="メールアドレスまたはユーザー名"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                パスワード
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="パスワード"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm text-center">
              {error}
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {isLoading ? 'ログイン中...' : 'ログイン'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
