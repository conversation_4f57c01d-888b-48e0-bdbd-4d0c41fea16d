'use client';

import { useState, useEffect } from 'react';
import { FiSave, FiLoader } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import AboutPageSettings from '@/components/admin/settings/AboutPageSettings';

interface Setting {
  id: string;
  key: string;
  value: string;
  description: string | null;
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<Setting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // 設定を取得
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // ローカルストレージからトークンを取得
        const token = localStorage.getItem('adminToken');
        if (!token) {
          console.error('認証トークンがありません');
          toast.error('認証情報が見つかりません。再ログインしてください。');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/admin/settings', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            toast.error('認証エラー: 再ログインしてください');
          } else {
            throw new Error('設定の取得に失敗しました');
          }
        } else {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
        toast.error('設定の取得に失敗しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // 設定値の変更
  const handleChange = (key: string, value: string) => {
    setSettings(prev =>
      prev.map(setting =>
        setting.key === key ? { ...setting, value } : setting
      )
    );
  };

  // 設定の保存
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // ローカルストレージからトークンを取得
      const token = localStorage.getItem('adminToken');
      if (!token) {
        console.error('認証トークンがありません');
        toast.error('認証情報が見つかりません。再ログインしてください。');
        setIsSaving(false);
        return;
      }

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('認証エラー: 再ログインしてください');
        } else {
          throw new Error('設定の保存に失敗しました');
        }
      } else {
        toast.success('設定を保存しました');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('設定の保存に失敗しました');
    } finally {
      setIsSaving(false);
    }
  };

  // 設定をグループ化
  const groupedSettings = {
    site: settings.filter(s => s.key.startsWith('site_')),
    profile: settings.filter(s => s.key.startsWith('profile_')),
    social: settings.filter(s => s.key.startsWith('twitter_') || s.key.startsWith('instagram_') || s.key.startsWith('youtube_')),
    contact: settings.filter(s => s.key.startsWith('contact_')),
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FiLoader className="animate-spin text-primary-600 text-2xl" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">サイト設定</h1>
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          {isSaving ? (
            <>
              <FiLoader className="animate-spin mr-2" />
              保存中...
            </>
          ) : (
            <>
              <FiSave className="mr-2" />
              設定を保存
            </>
          )}
        </button>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">サイト基本設定</h2>
          <div className="space-y-4">
            {groupedSettings.site.map(setting => (
              <div key={setting.id}>
                <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700">
                  {setting.description || setting.key}
                </label>
                <input
                  type="text"
                  id={setting.key}
                  name={setting.key}
                  value={setting.value}
                  onChange={(e) => handleChange(setting.key, e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">プロフィール設定</h2>
          <div className="space-y-4">
            {groupedSettings.profile.map(setting => (
              <div key={setting.id}>
                <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700">
                  {setting.description || setting.key}
                </label>
                <input
                  type="text"
                  id={setting.key}
                  name={setting.key}
                  value={setting.value}
                  onChange={(e) => handleChange(setting.key, e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">SNS設定</h2>
          <div className="space-y-4">
            {groupedSettings.social.map(setting => (
              <div key={setting.id}>
                <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700">
                  {setting.description || setting.key}
                </label>
                <input
                  type="text"
                  id={setting.key}
                  name={setting.key}
                  value={setting.value}
                  onChange={(e) => handleChange(setting.key, e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">お問い合わせ設定</h2>
          <div className="space-y-4">
            {groupedSettings.contact.map(setting => (
              <div key={setting.id}>
                <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700">
                  {setting.description || setting.key}
                </label>
                <input
                  type="text"
                  id={setting.key}
                  name={setting.key}
                  value={setting.value}
                  onChange={(e) => handleChange(setting.key, e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Aboutページコンテンツ設定</h2>
          <AboutPageSettings />
        </div>
      </div>
    </div>
  );
}
