import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import SnakeGame from '@/components/games/SnakeGame';

export default function SnakeGamePage() {
  return (
    <>
      <Header />

      <main className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8">
        <div className="container-custom">
          {/* ゲームヘッダー */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Link 
                href="/playground" 
                className="text-green-600 hover:text-green-800 flex items-center gap-2 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                プレイグラウンドに戻る
              </Link>
            </div>

            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="text-4xl">🐍</div>
              <div>
                <h1 className="text-4xl font-bold text-green-800 mb-2">Snake Game</h1>
                <div className="flex items-center justify-center gap-4 text-sm">
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                    Claude-4
                  </span>
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    初級
                  </span>
                </div>
              </div>
            </div>

            <p className="text-lg text-green-700 max-w-2xl mx-auto">
              経典的なヘビゲーム。矢印キーでヘビを操作し、餌を食べて成長させよう！<br />
              壁や自分の体にぶつからないよう注意しながら、ハイスコアを目指せ！
            </p>
          </div>

          {/* ゲームエリア */}
          <div className="max-w-4xl mx-auto">
            <SnakeGame />
          </div>

          {/* 操作説明 */}
          <div className="max-w-2xl mx-auto mt-12 bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">操作方法</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-4">🎮 基本操作</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">↑↓←→</span>
                    <span>矢印キーでヘビを移動</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">Space</span>
                    <span>ポーズ/再開</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">Enter</span>
                    <span>ゲーム開始/リスタート</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-4">🎯 ゲームルール</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• 赤い餌を食べてスコアを獲得</li>
                  <li>• 餌を食べるとヘビが1マス成長</li>
                  <li>• 壁にぶつかるとゲームオーバー</li>
                  <li>• 自分の体にぶつかるとゲームオーバー</li>
                  <li>• スコアが上がると移動速度が増加</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200 text-center">
              <p className="text-sm text-gray-500">
                💡 モバイルの場合は、画面下部のボタンで操作できます
              </p>
            </div>
          </div>

          {/* 開発情報 */}
          <div className="max-w-2xl mx-auto mt-8 bg-green-50 rounded-xl p-6 text-center">
            <h3 className="text-lg font-semibold text-green-800 mb-2">🤖 開発情報</h3>
            <p className="text-green-700 text-sm">
              このゲームは <strong>Claude-4</strong> を使用して作成されました。<br />
              Canvas API + React hooks + TypeScript で実装されています。
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
} 