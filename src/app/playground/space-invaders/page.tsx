import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import SpaceInvadersGame from '@/components/games/SpaceInvadersGame';

export default function SpaceInvadersGamePage() {
  return (
    <>
      <Header />

      <main className="min-h-screen bg-gradient-to-br from-indigo-50 to-indigo-100 py-8">
        <div className="container-custom px-4">
          {/* ゲームヘッダー */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Link 
                href="/playground" 
                className="text-indigo-600 hover:text-indigo-800 flex items-center gap-2 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                プレイグラウンドに戻る
              </Link>
            </div>

            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="text-3xl md:text-4xl">🚀</div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-indigo-800 mb-2">Space Invaders</h1>
                <div className="flex items-center justify-center gap-4 text-sm">
                  <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full font-medium">
                    Gemini
                  </span>
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    中級
                  </span>
                </div>
              </div>
            </div>

            <p className="text-base md:text-lg text-indigo-700 max-w-2xl mx-auto px-4">
              次々と現れるエイリアンを撃ち落とせ！<br className="hidden md:block" />
              迫りくる敵の攻撃をかわしながら、ハイスコアを目指そう。
            </p>
          </div>

          {/* ゲームエリア */}
          <div className="max-w-4xl mx-auto mb-8 md:mb-12 flex justify-center">
            <SpaceInvadersGame />
          </div>

          {/* 操作説明 */}
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-6 md:p-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">ゲームルール</h2>
            
            <div className="grid md:grid-cols-2 gap-6 md:gap-8">
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎮 基本操作</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">←→</span>
                    <span>矢印キー左右で自機を移動</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">スペース</span>
                    <span>スペースキーで弾を発射</span>
                  </li>
                  <li className="flex items-center gap-3 md:hidden">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs font-mono">Tap</span>
                    <span>画面下部のボタンで操作</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎯 ゲームルール</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li>• エイリアンを全て倒すとステージクリア</li>
                  <li>• エイリアンの攻撃に当たるとライフ減少</li>
                  <li>• ライフが0になるとゲームオーバー</li>
                  <li>• エイリアンが最下段に到達するとゲームオーバー</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-6 border-t border-gray-200 text-center">
              <p className="text-xs md:text-sm text-gray-500">
                💡 高得点を目指して、全てのエイリアンを撃退しよう！
              </p>
            </div>
          </div>

          {/* 開発情報 */}
          <div className="max-w-2xl mx-auto mt-6 md:mt-8 bg-indigo-50 rounded-xl p-4 md:p-6 text-center">
            <h3 className="text-base md:text-lg font-semibold text-indigo-800 mb-2">🤖 開発情報</h3>
            <p className="text-indigo-700 text-sm md:text-base">
              このゲームは <strong>Gemini</strong> を使用して作成されました。<br className="hidden md:block" />
              Canvas API + React hooks + TypeScript で実装されています。
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
} 