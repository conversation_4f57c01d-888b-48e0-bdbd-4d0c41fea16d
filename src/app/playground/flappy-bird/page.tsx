import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import FlappyBirdGame from '@/components/games/FlappyBirdGame';

export default function FlappyBirdGamePage() {
  return (
    <>
      <Header />

      <main className="min-h-screen bg-gradient-to-br from-cyan-50 to-blue-100 py-8">
        <div className="container-custom px-4">
          {/* ゲームヘッダー */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Link 
                href="/playground" 
                className="text-cyan-600 hover:text-cyan-800 flex items-center gap-2 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                プレイグラウンドに戻る
              </Link>
            </div>

            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="text-3xl md:text-4xl">🐦</div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-cyan-800 mb-2">Flappy Bird風</h1>
                <div className="flex items-center justify-center gap-4 text-sm">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                    Claude-4
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    初級
                  </span>
                </div>
              </div>
            </div>

            <p className="text-base md:text-lg text-cyan-700 max-w-2xl mx-auto px-4">
              シンプルだが中毒性のあるゲーム！スペースキーで鳥を飛ばし、<br className="hidden md:block" />
              パイプの隙間を通り抜けてハイスコアを目指そう！
            </p>
          </div>

          {/* ゲームエリア */}
          <div className="max-w-4xl mx-auto flex justify-center mb-8 md:mb-12">
            <FlappyBirdGame />
          </div>

          {/* 操作説明 */}
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-6 md:p-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">操作方法</h2>
            
            <div className="grid md:grid-cols-2 gap-6 md:gap-8">
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎮 基本操作</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">Space</span>
                    <span>鳥をジャンプさせる</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">Click</span>
                    <span>鳥をジャンプさせる</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">Tap</span>
                    <span>鳥をジャンプさせる（モバイル）</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎯 ゲームルール</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li>• パイプの隙間を通り抜ける</li>
                  <li>• パイプに触れるとゲームオーバー</li>
                  <li>• 地面に触れるとゲームオーバー</li>
                  <li>• 通過するたびにスコア+1</li>
                  <li>• ハイスコアは自動保存される</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4 text-center">🏆 攻略のコツ</h3>
              <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                <li>• 一定のリズムでジャンプする</li>
                <li>• パイプの隙間の中央を狙う</li>
                <li>• 焦らず冷静に操作する</li>
                <li>• 重力を意識してタイミングを調整</li>
              </ul>
            </div>
          </div>

          {/* 開発情報 */}
          <div className="max-w-2xl mx-auto mt-6 md:mt-8 bg-cyan-50 rounded-xl p-4 md:p-6 text-center">
            <h3 className="text-base md:text-lg font-semibold text-cyan-800 mb-2">🤖 開発情報</h3>
            <p className="text-cyan-700 text-sm md:text-base">
              このゲームは <strong>Claude 3.5 Sonnet</strong> を使用して作成されました。<br className="hidden md:block" />
              Canvas API + React hooks + TypeScript で実装され、オリジナルのFlappy Birdの楽しさを再現しています。
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
} 