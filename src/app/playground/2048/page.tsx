import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import Game2048 from '@/components/games/Game2048';

export default function Game2048Page() {
  return (
    <>
      <Header />

      <main className="min-h-screen bg-gradient-to-br from-purple-50 to-purple-100 py-8">
        <div className="container-custom px-4">
          {/* ゲームヘッダー */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Link 
                href="/playground" 
                className="text-purple-600 hover:text-purple-800 flex items-center gap-2 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                プレイグラウンドに戻る
              </Link>
            </div>

            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="text-3xl md:text-4xl">🔢</div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-purple-800 mb-2">2048</h1>
                <div className="flex items-center justify-center gap-4 text-sm">
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">
                    Claude-4
                  </span>
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    初級
                  </span>
                </div>
              </div>
            </div>

            <p className="text-base md:text-lg text-purple-700 max-w-2xl mx-auto px-4">
              数字タイルをスライドさせて合成し、2048を目指そう！<br className="hidden md:block" />
              同じ数字のタイルがぶつかると合体して倍になります。戦略的思考が鍵となる！
            </p>
          </div>

          {/* ゲームエリア */}
          <div className="max-w-4xl mx-auto mb-8 md:mb-12 flex justify-center">
            <Game2048 />
          </div>

          {/* 操作説明 */}
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-6 md:p-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">ゲームルール</h2>
            
            <div className="grid md:grid-cols-2 gap-6 md:gap-8">
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎮 基本操作</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">↑↓←→</span>
                    <span>矢印キーでタイルを移動</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">WASD</span>
                    <span>WASDキーでも操作可能</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">R</span>
                    <span>ゲームリスタート</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">U</span>
                    <span>一手戻す（Undo）</span>
                  </li>
                  <li className="flex items-center gap-3 md:hidden">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs font-mono">Tap</span>
                    <span>画面下部のボタンで操作</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎯 ゲームルール</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li>• 同じ数字のタイルをぶつけて合成</li>
                  <li>• 合成すると数字が倍になる</li>
                  <li>• 毎ターン新しいタイル（2か4）が出現</li>
                  <li>• 2048タイルを作成すると勝利</li>
                  <li>• 盤面が埋まると敗北</li>
                  <li>• スコアは合成した数字の合計</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">💡 攻略のコツ</h3>
              <div className="grid md:grid-cols-2 gap-4 text-xs md:text-sm text-gray-600">
                <div>
                  <p className="mb-2"><strong>角を活用:</strong> 大きな数字を角に配置</p>
                  <p className="mb-2"><strong>一方向重視:</strong> 主に一方向に動かす</p>
                </div>
                <div>
                  <p className="mb-2"><strong>順序を保つ:</strong> 数字を降順に並べる</p>
                  <p className="mb-2"><strong>空きを確保:</strong> 常に移動の余地を残す</p>
                </div>
              </div>
            </div>

            <div className="mt-4 md:mt-6 pt-4 border-t border-gray-200 text-center">
              <p className="text-xs md:text-sm text-gray-500">
                💡 モバイルの場合は、画面下部のボタンでプレイできます
              </p>
            </div>
          </div>

          {/* 開発情報 */}
          <div className="max-w-2xl mx-auto mt-6 md:mt-8 bg-purple-50 rounded-xl p-4 md:p-6 text-center">
            <h3 className="text-base md:text-lg font-semibold text-purple-800 mb-2">🤖 開発情報</h3>
            <p className="text-purple-700 text-sm md:text-base">
              このゲームは <strong>Claude-4</strong> を使用して作成されました。<br className="hidden md:block" />
              Canvas API + React hooks + TypeScript で実装され、スムーズなアニメーションを備えています。
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
} 