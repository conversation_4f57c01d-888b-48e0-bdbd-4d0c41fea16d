import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import TetrisRPGGame from '@/components/games/TetrisRPGGame';

export default function TetrisRPGGamePage() {
  return (
    <>
      <Header />

      <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
        <div className="container-custom px-4">
          {/* ゲームヘッダー */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Link 
                href="/playground" 
                className="text-blue-600 hover:text-blue-800 flex items-center gap-2 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                プレイグラウンドに戻る
              </Link>
            </div>

            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="text-3xl md:text-4xl">🧩</div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-blue-800 mb-2">Tetris RPG</h1>
                <div className="flex items-center justify-center gap-4 text-sm">
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">
                    Claude 3.7
                  </span>
                  <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full">
                    中級
                  </span>
                </div>
              </div>
            </div>

            <p className="text-base md:text-lg text-blue-700 max-w-2xl mx-auto px-4">
              クラシックなテトリスにRPG要素を追加！ブロックを消してレベルアップし、<br className="hidden md:block" />
              強力なスキルを解放しながらハイスコアを目指そう！
            </p>
          </div>

          {/* ゲームエリア */}
          <div className="max-w-4xl mx-auto flex justify-center mb-8 md:mb-12">
            <TetrisRPGGame />
          </div>

          {/* 操作説明 */}
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-6 md:p-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">操作方法</h2>
            
            <div className="grid md:grid-cols-2 gap-6 md:gap-8">
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🎮 基本操作</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">←→</span>
                    <span>ブロックを左右に移動</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">↑</span>
                    <span>ブロックを回転</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">↓</span>
                    <span>ソフトドロップ（少し速く落下）</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">Space</span>
                    <span>ハードドロップ（一気に落下）</span>
                  </li>
                  <li className="flex items-center gap-3 md:hidden">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs font-mono">Tap</span>
                    <span>画面下部のボタンで操作</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4">🏆 RPG要素</h3>
                <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                  <li className="flex items-center gap-3">
                    <span className="bg-gray-100 px-2 py-1 rounded text-xs md:text-sm font-mono">1-4</span>
                    <span>スキルを発動</span>
                  </li>
                  <li>• ブロックを消すと経験値を獲得</li>
                  <li>• レベルアップでスキルが解放</li>
                  <li>• レベル2：一列消去スキル</li>
                  <li>• レベル3：スローダウンスキル</li>
                  <li>• レベル4：ブロック変換スキル</li>
                  <li>• レベル5：ブロック破壊スキル</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 md:mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-4 text-center">🎯 ゲームルール</h3>
              <ul className="space-y-2 text-gray-600 text-sm md:text-base">
                <li>• 一度に複数の行を消すとボーナススコアを獲得</li>
                <li>• レベルが上がるとブロックの落下速度が上昇</li>
                <li>• スキルを使用後はクールダウン時間が必要</li>
                <li>• 画面の上端までブロックが積み上がるとゲームオーバー</li>
              </ul>
            </div>
          </div>

          {/* 開発情報 */}
          <div className="max-w-2xl mx-auto mt-6 md:mt-8 bg-indigo-50 rounded-xl p-4 md:p-6 text-center">
            <h3 className="text-base md:text-lg font-semibold text-indigo-800 mb-2">🤖 開発情報</h3>
            <p className="text-indigo-700 text-sm md:text-base">
              このゲームは <strong>Claude 3.7 Sonnet</strong> を使用して作成されました。<br className="hidden md:block" />
              Canvas API + React hooks + TypeScript で実装され、RPG要素を追加したユニークなテトリスとなっています。
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
} 