import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';

// ゲーム一覧データ
const games = [
  {
    id: 'snake',
    title: 'Snake Game',
    description: '経典的なヘビゲーム。餌を食べて長くなりながら、自分の体にぶつからないよう注意しよう！',
    difficulty: '初級',
    model: 'Claude-4',
    status: 'available', // 'available' | 'coming-soon'
    color: 'from-green-400 to-green-600',
    icon: '🐍'
  },
  {
    id: 'tetris-rpg',
    title: 'Tetris RPG',
    description: 'ブロックを組み合わせてラインを消去！レベルアップでスキルを獲得するRPG要素付き！',
    difficulty: '中級',
    model: 'Claude 3.7',
    status: 'available',
    color: 'from-blue-400 to-blue-600',
    icon: '🧩'
  },
  {
    id: 'pacman',
    title: 'Pac-Man風ゲーム',
    description: 'マップを移動してアイテムを集めよう。敵の動きを読んで高得点を狙え！',
    difficulty: '中級',
    model: 'Gemini',
    status: 'available', // 'available' | 'coming-soon'
    color: 'from-yellow-400 to-yellow-600',
    icon: '👻'
  },
  {
    id: '2048',
    title: '2048',
    description: '数字タイルをスライドさせて合成し、2048を目指すパズルゲーム。戦略的思考が必要！',
    difficulty: '初級',
    model: 'Claude-4',
    status: 'available', // 'available' | 'coming-soon'
    color: 'from-purple-400 to-purple-600',
    icon: '🔢'
  },
  {
    id: 'flappy-bird',
    title: 'Flappy Bird風',
    description: '障害物を避けながら飛び続ける。シンプルだが奥が深いゲーム！',
    difficulty: '初級',
    model: 'Claude-4',
    status: 'available',
    color: 'from-cyan-400 to-cyan-600',
    icon: '🐦'
  },
  {
    id: 'space-invaders',
    title: 'Space Invaders',
    description: '侵略者を撃退せよ！射撃と回避のバランスが勝利の鍵。',
    difficulty: '中級',
    model: 'Gemini',
    status: 'available', // 'available' | 'coming-soon'
    color: 'from-indigo-400 to-indigo-600',
    icon: '🚀'
  },
  {
    id: 'minesweeper',
    title: 'マインスイーパー',
    description: '地雷を避けながらマスを開いていくパズルゲーム。すべての安全なマスを開いてクリアしよう！',
    difficulty: '中級',
    model: 'o4-mini',
    status: 'available',
    color: 'from-emerald-400 to-emerald-600',
    icon: '💣'
  },
  {
    id: 'breakout',
    title: 'ブロック崩し',
    description: 'ボールを跳ね返してブロックを全て破壊しよう！パドルの操作が鍵を握るアクションゲーム。',
    difficulty: '中級',
    model: 'o4-mini',
    status: 'available',
    color: 'from-orange-400 to-orange-600',
    icon: '🏓'
  },
  {
    id: 'whack-a-mole',
    title: 'モグラ叩き',
    description: '6つの穴から出てくるもぐらをタッチで叩こう！3回逃すとゲームオーバー。',
    difficulty: '初級',
    model: 'GPT-4.1',
    status: 'available',
    color: 'from-pink-400 to-pink-600',
    icon: '🐹'
  }
];

export default function PlaygroundPage() {
  return (
    <>
      <Header />

      <main>
        {/* Hero Section with SVG Title */}
        <section className="relative bg-gradient-to-br from-slate-50 to-slate-200 py-16">
          <div className="container-custom">
            <div className="text-center mb-8">
              {/* SVGタイトルを表示 */}
              <div className="mb-8 flex justify-center">
                <div className="w-full max-w-4xl">
                  <Image
                    src="/playground_title.svg"
                    alt="Playground - Simple Games Collection"
                    width={800}
                    height={200}
                    className="w-full h-auto"
                    priority
                  />
                </div>
              </div>
              
              <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-8">
                懐かしのアーケードゲームをモダンな技術で再現。<br />
                各ゲームは異なるAIモデルを使って作成され、コーディングテストの題材としても活用できます。
              </p>
              
              <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-500">
                <span className="bg-white px-3 py-1 rounded-full shadow-sm">🎮 Canvas API</span>
                <span className="bg-white px-3 py-1 rounded-full shadow-sm">⚡ TypeScript</span>
                <span className="bg-white px-3 py-1 rounded-full shadow-sm">🎨 Modern UI/UX</span>
                <span className="bg-white px-3 py-1 rounded-full shadow-sm">📱 レスポンシブ対応</span>
              </div>
            </div>
          </div>
        </section>

        {/* Games Grid */}
        <section className="py-16 bg-white">
          <div className="container-custom">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-serif font-bold text-slate-800 mb-4">
                ゲーム一覧
              </h2>
              <p className="text-slate-600">
                クリックしてゲームを開始しよう！（順次追加予定）
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {games.map((game) => (
                <div
                  key={game.id}
                  className="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
                >
                  {/* ゲームカードのグラデーション背景 */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${game.color} opacity-10 group-hover:opacity-15 transition-opacity`}></div>
                  
                  {/* カード内容 */}
                  <div className="relative p-4 md:p-6">
                    {/* ステータスバッジ */}
                    <div className="flex justify-between items-start mb-4">
                      <div className="text-3xl md:text-4xl">{game.icon}</div>
                      <div className="flex flex-col gap-2">
                        {game.status === 'coming-soon' ? (
                          <span className="bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full">
                            準備中
                          </span>
                        ) : (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            プレイ可能
                          </span>
                        )}
                        <span className="bg-slate-100 text-slate-700 text-xs px-2 py-1 rounded-full">
                          {game.model}
                        </span>
                      </div>
                    </div>

                    {/* ゲーム情報 */}
                    <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-2 group-hover:text-slate-900">
                      {game.title}
                    </h3>
                    <p className="text-slate-600 text-sm mb-4 leading-relaxed">
                      {game.description}
                    </p>

                    {/* 難易度 */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-slate-500">難易度:</span>
                      <span className={`text-sm font-medium ${
                        game.difficulty === '初級' ? 'text-green-600' :
                        game.difficulty === '中級' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {game.difficulty}
                      </span>
                    </div>

                    {/* プレイボタン */}
                    {game.status === 'available' ? (
                      <Link
                        href={`/playground/${game.id}`}
                        className={`block w-full bg-gradient-to-r ${game.color} text-white text-center py-3 px-4 rounded-lg font-medium transition-all duration-200 transform group-hover:scale-105 shadow-md hover:shadow-lg text-sm md:text-base`}
                      >
                        プレイする
                      </Link>
                    ) : (
                      <div className="block w-full bg-slate-200 text-slate-500 text-center py-3 px-4 rounded-lg font-medium cursor-not-allowed text-sm md:text-base">
                        近日公開予定
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* 開発予定セクション */}
            <div className="mt-16 bg-slate-50 rounded-xl p-8 text-center">
              <h3 className="text-2xl font-serif font-bold text-slate-800 mb-4">
                開発予定
              </h3>
              <p className="text-slate-600 mb-6">
                以下のゲームも順次追加予定です。リクエストがありましたらお気軽にお知らせください！
              </p>
              <div className="flex flex-wrap justify-center gap-3">
                {['Frogger風', 'Memory Card Game', 'Tic-Tac-Toe', 'Whack-a-Mole', 'Catch Game'].map((game) => (
                  <span key={game} className="bg-white text-slate-600 px-3 py-2 rounded-lg shadow-sm text-sm">
                    {game}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* 技術情報セクション */}
        <section className="py-16 bg-slate-50">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center">
              <h3 className="text-2xl font-serif font-bold text-slate-800 mb-8">
                技術スタック
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
                  <div className="text-2xl md:text-3xl mb-2 md:mb-3">⚛️</div>
                  <h4 className="font-semibold text-slate-800 mb-1 md:mb-2 text-sm md:text-base">Next.js 14</h4>
                  <p className="text-xs md:text-sm text-slate-600">App Router、RSC対応</p>
                </div>
                <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
                  <div className="text-2xl md:text-3xl mb-2 md:mb-3">🎨</div>
                  <h4 className="font-semibold text-slate-800 mb-1 md:mb-2 text-sm md:text-base">Canvas API</h4>
                  <p className="text-xs md:text-sm text-slate-600">高性能2Dグラフィック</p>
                </div>
                <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
                  <div className="text-2xl md:text-3xl mb-2 md:mb-3">🔷</div>
                  <h4 className="font-semibold text-slate-800 mb-1 md:mb-2 text-sm md:text-base">TypeScript</h4>
                  <p className="text-xs md:text-sm text-slate-600">型安全な開発</p>
                </div>
                <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
                  <div className="text-2xl md:text-3xl mb-2 md:mb-3">🎭</div>
                  <h4 className="font-semibold text-slate-800 mb-1 md:mb-2 text-sm md:text-base">Tailwind CSS</h4>
                  <p className="text-xs md:text-sm text-slate-600">モダンなスタイリング</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
} 