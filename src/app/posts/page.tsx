import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import PostCard from '@/components/ui/PostCard';
import CategoryList from '@/components/ui/CategoryList';

// Import API functions
import { getCategories, getPosts, getRecentPosts, getSettings } from '@/lib/api';

// This is a wrapper component that handles the Promise-based params
export default async function PostsPageWrapper(props: {
  searchParams?: Promise<{ page?: string; limit?: string }>;
}) {
  const searchParams = props.searchParams ? await props.searchParams : undefined;

  return <PostsPage searchParams={searchParams} />;
}

// This is the actual component that renders the page
async function PostsPage({
  searchParams,
}: {
  searchParams?: { page?: string; limit?: string };
}) {
  // ページネーションパラメータの取得
  const page = searchParams?.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams?.limit ? parseInt(searchParams.limit) : 10;

  // データの取得
  const categoriesData = await getCategories();
  const { posts, meta } = await getPosts({ page, limit });
  const { posts: recentPostsData } = await getRecentPosts(3);
  const settings = await getSettings(['site_description']);

  // ページネーションの計算
  const totalPages = meta.totalPages;
  const hasPrev = page > 1;
  const hasNext = page < totalPages;

  // ページネーションリンクの生成
  const paginationLinks = [];
  const maxPagesToShow = 5;

  let startPage = Math.max(1, page - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    paginationLinks.push(i);
  }

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-primary-900 text-white py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">すべての記事</h1>
            <p className="text-lg text-primary-100 max-w-3xl">
              {settings.site_description ? `${settings.site_description}をご覧ください。` : '日本の美術、工芸、考古学、文化についての考察と発見の記録をご覧ください。'}
            </p>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                {/* Filter Controls */}
                <div className="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-wrap items-center gap-4">
                  <span className="text-primary-700 font-medium">並び替え:</span>
                  <select className="bg-white border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-accent-500">
                    <option value="newest">新しい順</option>
                    <option value="oldest">古い順</option>
                    <option value="title">タイトル順</option>
                  </select>

                  <span className="text-primary-700 font-medium ml-auto">表示:</span>
                  <select className="bg-white border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-accent-500">
                    <option value="6">6件</option>
                    <option value="12">12件</option>
                    <option value="24">24件</option>
                    <option value="all">すべて</option>
                  </select>
                </div>

                {/* Posts Grid */}
                <div className="space-y-6">
                  {posts && posts.length > 0 ? (
                    posts.map((post, index) => (
                      <PostCard key={post.id} post={post} imageIndex={index} />
                    ))
                  ) : (
                    <div className="bg-white rounded-lg shadow-md p-8 text-center">
                      <p className="text-primary-600 mb-4">記事はまだありません。</p>
                      <p className="text-sm text-gray-500 mb-6">新しい記事が追加されるまでお待ちください。</p>
                      <Link href="/" className="btn btn-primary">
                        トップページに戻る
                      </Link>
                    </div>
                  )}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <nav className="flex items-center space-x-2">
                      <Link
                        href={hasPrev ? `/posts?page=${page - 1}&limit=${limit}` : '#'}
                        className={`px-3 py-1 rounded-md bg-white border border-gray-300 text-primary-700 hover:bg-gray-50 ${!hasPrev ? 'opacity-50 cursor-not-allowed' : ''}`}
                        aria-disabled={!hasPrev}
                      >
                        前へ
                      </Link>

                      {paginationLinks.map((pageNum) => (
                        <Link
                          key={pageNum}
                          href={`/posts?page=${pageNum}&limit=${limit}`}
                          className={`px-3 py-1 rounded-md ${pageNum === page ? 'bg-accent-600 text-white' : 'bg-white border border-gray-300 text-primary-700 hover:bg-gray-50'}`}
                        >
                          {pageNum}
                        </Link>
                      ))}

                      {endPage < totalPages && (
                        <span className="px-2 text-gray-500">...</span>
                      )}

                      <Link
                        href={hasNext ? `/posts?page=${page + 1}&limit=${limit}` : '#'}
                        className={`px-3 py-1 rounded-md bg-white border border-gray-300 text-primary-700 hover:bg-gray-50 ${!hasNext ? 'opacity-50 cursor-not-allowed' : ''}`}
                        aria-disabled={!hasNext}
                      >
                        次へ
                      </Link>
                    </nav>
                  </div>
                )}
              </div>

              <div className="space-y-8">
                {/* Categories */}
                <CategoryList categories={categoriesData} />

                {/* Recent Posts */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">最新の記事</h2>
                  {recentPostsData && recentPostsData.length > 0 ? (
                    <ul className="space-y-4">
                      {recentPostsData.map(post => (
                        <li key={post.id} className="flex items-start">
                          <div className="relative w-16 h-16 rounded overflow-hidden flex-shrink-0 mr-3">
                            <Image
                              src={post.featuredImage || '/images/dogu-figure.jpg'}
                              alt={post.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h3 className="font-medium text-primary-900 leading-snug">
                              <Link href={`/posts/${post.slug}`} className="hover:text-accent-700">
                                {post.title.length > 40 ? `${post.title.substring(0, 40)}...` : post.title}
                              </Link>
                            </h3>
                            <span className="text-sm text-primary-500">
                              {post.publishedAt && new Date(post.publishedAt).toLocaleDateString('ja-JP')}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-primary-600 text-center py-4">最新の記事はまだありません。</p>
                  )}
                </div>

                {/* Tags Cloud */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">カテゴリー</h2>
                  <div className="flex flex-wrap gap-2">
                    {categoriesData && categoriesData.length > 0 ? (
                      categoriesData.map(category => (
                        <Link
                          key={category.id}
                          href={`/categories/${category.slug}`}
                          className="px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm hover:bg-primary-200"
                        >
                          {category.name}
                          {category._count?.posts > 0 && ` (${category._count.posts})`}
                        </Link>
                      ))
                    ) : (
                      <p className="text-primary-600 text-center py-4">カテゴリーはまだありません。</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
