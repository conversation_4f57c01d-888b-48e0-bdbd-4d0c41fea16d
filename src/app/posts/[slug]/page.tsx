import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import { formatDate } from '@/lib/utils';
import { processContentForDisplay } from '@/lib/contentUtils';

// Import API functions
import { getPost, getCategories } from '@/lib/api';

// This is a wrapper component that handles the Promise-based params
export default async function PostPageWrapper(props: {
  params: Promise<{ slug: string }>;
}) {
  const params = await props.params;

  return <PostPage params={params} />;
}

// This is the actual component that renders the page
async function PostPage({ params }: { params: { slug: string } }) {
  const data = await getPost(params.slug);
  const categories = await getCategories();

  // 記事が見つからない場合
  if (!data || !data.post) {
    return (
      <>
        <Header />
        <main>
          <section className="bg-primary-900 text-white py-12">
            <div className="container-custom">
              <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">記事が見つかりません</h1>
              <p className="text-lg text-primary-100 max-w-3xl">
                指定された記事は存在しないか、削除された可能性があります。
              </p>
            </div>
          </section>
          <section className="py-12">
            <div className="container-custom">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">指定された記事は見つかりませんでした。</p>
                    <Link href="/" className="btn btn-primary">
                      トップページに戻る
                    </Link>
                  </div>
                </div>
                <div className="space-y-8">
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">カテゴリー</h2>
                    <ul className="space-y-2">
                      {categories.map(category => (
                        <li key={category.id}>
                          <Link
                            href={`/categories/${category.slug}`}
                            className="text-primary-800 hover:text-accent-600 font-medium"
                          >
                            {category.name}
                          </Link>

                          {category.subCategories && category.subCategories.length > 0 && (
                            <ul className="ml-4 mt-2 space-y-1">
                              {category.subCategories.map(subCategory => (
                                <li key={subCategory.id}>
                                  <Link
                                    href={`/categories/${category.slug}/${subCategory.slug}`}
                                    className="text-primary-600 hover:text-accent-600 text-sm"
                                  >
                                    {subCategory.name}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </>
    );
  }

  const { post, relatedPosts } = data;

  return (
    <>
      <Header />

      <main className="pb-16">
        {/* Hero Section */}
        <section className="relative h-96">
          <div className="absolute inset-0 z-0">
            <div className="relative w-full h-full">
              <Image
                src={post.featuredImage || '/images/dogu-figure.jpg'}
                alt={post.title}
                fill
                sizes="100vw"
                className="object-cover brightness-50"
                priority
              />
            </div>
          </div>
          <div className="container-custom relative z-10 h-full flex flex-col justify-end pb-12">
            <div className="text-white max-w-3xl">
              <Link
                href={`/categories/${post.category.slug}`}
                className="inline-block mb-4 bg-accent-600 text-white px-3 py-1 rounded-full text-sm font-medium"
              >
                {post.category.name}
              </Link>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-serif font-medium mb-4">
                {post.title}
              </h1>
              <div className="flex items-center text-sm">
                <div className="flex items-center mr-6">
                  <div className="w-8 h-8 rounded-full overflow-hidden relative mr-2 profile-image">
                    <Image
                      src={post.user.profileImage || "/images/owl-icon.png"}
                      alt={post.user.username}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <span>{post.user.username}</span>
                </div>
                {post.publishedAt && (
                  <time dateTime={new Date(post.publishedAt).toISOString()}>
                    {formatDate(post.publishedAt)}
                  </time>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <article className="bg-white rounded-lg shadow-md p-6 md:p-8">
                  <div
                    className="prose prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: processContentForDisplay(post.content) }}
                  />
                </article>

                {/* Related Posts */}
                {relatedPosts && relatedPosts.length > 0 && (
                  <div className="mt-8">
                    <h2 className="text-2xl font-serif font-medium mb-6">関連記事</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {relatedPosts.map((relatedPost, index) => (
                        <div key={relatedPost.id} className="card h-full flex flex-col">
                          <div className="relative h-48 w-full">
                            <Image
                              src={relatedPost.featuredImage || `/images/${['dogu-figure.jpg', 'bird-icon.png', 'cat-icon.png'][index % 3]}`}
                              alt={relatedPost.title}
                              fill
                              sizes="(max-width: 768px) 100vw, 33vw"
                              className="object-cover"
                            />
                          </div>
                          <div className="p-4 flex-grow flex flex-col">
                            <h3 className="text-lg font-serif font-medium mb-2">
                              <Link href={`/posts/${relatedPost.slug}`} className="text-primary-900 hover:text-accent-700">
                                {relatedPost.title}
                              </Link>
                            </h3>
                            {relatedPost.excerpt && (
                              <p className="text-primary-600 text-sm mb-3 flex-grow">
                                {relatedPost.excerpt.length > 80 ? `${relatedPost.excerpt.substring(0, 80)}...` : relatedPost.excerpt}
                              </p>
                            )}
                            <Link
                              href={`/posts/${relatedPost.slug}`}
                              className="inline-flex items-center text-accent-600 hover:text-accent-800 text-sm mt-auto"
                            >
                              続きを読む
                              <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                              </svg>
                            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-8">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">著者について</h2>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-24 h-24 rounded-full mb-4 overflow-hidden relative profile-image">
                      <Image
                        src={post.user.profileImage || "/images/owl-icon.png"}
                        alt={post.user.username}
                        fill
                        sizes="(max-width: 768px) 96px, 96px"
                        className="object-cover"
                      />
                    </div>
                    <h3 className="font-medium text-lg mb-2">{post.user.username}</h3>
                    <p className="text-primary-600 mb-4">
                      {post.user.bio || "著者の紹介文はまだ登録されていません。"}
                    </p>
                    <Link href={`/profile/${post.user.id}`} className="text-accent-600 hover:text-accent-800">
                      詳しいプロフィール
                    </Link>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">カテゴリー</h2>
                  <ul className="space-y-2">
                    <li>
                      <Link
                        href={`/categories/${post.category.slug}`}
                        className="text-primary-800 hover:text-accent-600 font-medium"
                      >
                        {post.category.name}
                      </Link>
                    </li>
                    {post.subCategory && (
                      <li>
                        <Link
                          href={`/categories/${post.category.slug}/${post.subCategory.slug}`}
                          className="text-primary-800 hover:text-accent-600 ml-3"
                        >
                          {post.subCategory.name}
                        </Link>
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
