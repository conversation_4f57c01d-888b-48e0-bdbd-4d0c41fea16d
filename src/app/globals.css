@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-primary-800 bg-gray-50;
    overflow-x: hidden; /* 横スクロールを防止 */
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-medium;
  }
  h1 {
    @apply text-3xl md:text-4xl;
  }
  h2 {
    @apply text-2xl md:text-3xl;
  }
  h3 {
    @apply text-xl md:text-2xl;
  }
  a {
    @apply text-accent-700 hover:text-accent-900 transition-colors;
  }

  /* モバイル表示の調整 */
  @media (max-width: 768px) {
    img {
      max-width: 100%;
      height: auto;
    }

    /* 画像の最大サイズを制限 */
    .profile-image img {
      max-width: 120px;
      max-height: 120px;
    }

    /* 管理画面のモバイル表示調整 */
    .admin-form-grid {
      grid-template-columns: 1fr !important;
    }

    /* 管理画面のテーブルをレスポンシブ対応 */
    table {
      display: block;
      width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    /* テーブルのセル幅を調整 */
    th, td {
      white-space: nowrap;
      padding: 0.5rem !important;
    }

    /* アクションボタンのセルを小さく */
    th:last-child, td:last-child {
      width: 1%;
      white-space: nowrap;
    }

    /* アクションボタンを縦に並べる */
    td:last-child > div {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    /* モバイル表示時のフォーム要素 */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    textarea,
    select {
      font-size: 16px !important; /* iOSでズームインを防止 */
    }

    /* モバイル表示時のボタン */
    button {
      touch-action: manipulation;
    }
  }
}

@layer components {
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }
  .btn-primary {
    @apply bg-accent-600 text-white hover:bg-accent-700;
  }
  .btn-secondary {
    @apply bg-primary-100 text-primary-800 hover:bg-primary-200;
  }
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  /* 記事コンテンツのスタイル */
  .prose {
    @apply text-primary-800;
  }
  .prose h2 {
    @apply text-2xl font-serif font-medium mt-8 mb-4 pb-2 border-b border-gray-200;
  }
  .prose h3 {
    @apply text-xl font-serif font-medium mt-6 mb-3;
  }
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  .prose ul, .prose ol {
    @apply mb-4 pl-5;
  }
  .prose li {
    @apply mb-2;
  }
  .prose img {
    @apply rounded-lg my-6 mx-auto;
  }
  .prose a {
    @apply text-accent-600 hover:text-accent-800 underline;
  }
  .prose blockquote {
    @apply pl-4 border-l-4 border-primary-300 italic my-6 text-primary-600;
  }
}
