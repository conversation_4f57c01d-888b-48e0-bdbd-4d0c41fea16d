import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import PostCard from '@/components/ui/PostCard';
import CategoryList from '@/components/ui/CategoryList';

// Import API functions
import { getCategories, getCategoryPosts } from '@/lib/api';

// This is a wrapper component that handles the Promise-based params
export default async function CategoryPageWrapper(props: {
  params: Promise<{ slug: string }>;
  searchParams?: Promise<{ page?: string; limit?: string }>;
}) {
  const params = await props.params;
  const searchParams = props.searchParams ? await props.searchParams : undefined;

  return <CategoryPage params={params} searchParams={searchParams} />;
}

// This is the actual component that renders the page
async function CategoryPage({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams?: { page?: string; limit?: string };
}) {
  // ページネーションパラメータの取得
  const page = searchParams?.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams?.limit ? parseInt(searchParams.limit) : 10;

  // データの取得
  const categoriesData = await getCategories();
  const category = categoriesData.find(cat => cat.slug === params.slug);

  // カテゴリーが見つからない場合は空のカテゴリーを表示
  if (!category) {
    return (
      <>
        <Header />
        <main>
          <section className="bg-primary-900 text-white py-12">
            <div className="container-custom">
              <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">カテゴリーが見つかりません</h1>
              <p className="text-lg text-primary-100 max-w-3xl">
                指定されたカテゴリーは存在しないか、削除された可能性があります。
              </p>
            </div>
          </section>
          <section className="py-12">
            <div className="container-custom">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">指定されたカテゴリーは見つかりませんでした。</p>
                    <Link href="/categories" className="btn btn-primary">
                      カテゴリー一覧に戻る
                    </Link>
                  </div>
                </div>
                <div className="space-y-8">
                  <CategoryList categories={categoriesData} />
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </>
    );
  }

  const { posts, meta } = await getCategoryPosts(params.slug, page, limit);

  // ページネーションの計算
  const totalPages = meta.totalPages;
  const hasPrev = page > 1;
  const hasNext = page < totalPages;

  // ページネーションリンクの生成
  const paginationLinks = [];
  const maxPagesToShow = 5;

  let startPage = Math.max(1, page - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    paginationLinks.push(i);
  }

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-primary-900 text-white py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">{category.name}</h1>
            {category.description && (
              <p className="text-lg text-primary-100 max-w-3xl">
                {category.description}
              </p>
            )}
          </div>
        </section>

        {/* Content Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-serif font-medium mb-8">{category.name}の記事</h2>

                {posts && posts.length > 0 ? (
                  <div className="space-y-6">
                    {posts.map((post, index) => (
                      <PostCard key={post.id} post={post} imageIndex={index} />
                    ))}
                  </div>
                ) : (
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">このカテゴリーにはまだ記事がありません。</p>
                    <p className="text-sm text-gray-500 mb-6">新しい記事が追加されるまでお待ちください。</p>
                    <Link href="/" className="btn btn-primary">
                      トップページに戻る
                    </Link>
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <nav className="flex items-center space-x-2">
                      <Link
                        href={hasPrev ? `/categories/${params.slug}?page=${page - 1}&limit=${limit}` : '#'}
                        className={`px-3 py-1 rounded-md bg-white border border-gray-300 text-primary-700 hover:bg-gray-50 ${!hasPrev ? 'opacity-50 cursor-not-allowed' : ''}`}
                        aria-disabled={!hasPrev}
                      >
                        前へ
                      </Link>

                      {paginationLinks.map((pageNum) => (
                        <Link
                          key={pageNum}
                          href={`/categories/${params.slug}?page=${pageNum}&limit=${limit}`}
                          className={`px-3 py-1 rounded-md ${pageNum === page ? 'bg-accent-600 text-white' : 'bg-white border border-gray-300 text-primary-700 hover:bg-gray-50'}`}
                        >
                          {pageNum}
                        </Link>
                      ))}

                      {endPage < totalPages && (
                        <span className="px-2 text-gray-500">...</span>
                      )}

                      <Link
                        href={hasNext ? `/categories/${params.slug}?page=${page + 1}&limit=${limit}` : '#'}
                        className={`px-3 py-1 rounded-md bg-white border border-gray-300 text-primary-700 hover:bg-gray-50 ${!hasNext ? 'opacity-50 cursor-not-allowed' : ''}`}
                        aria-disabled={!hasNext}
                      >
                        次へ
                      </Link>
                    </nav>
                  </div>
                )}
              </div>

              <div className="space-y-8">
                <CategoryList categories={categoriesData} />

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">サブカテゴリー</h2>
                  {category.subCategories && category.subCategories.length > 0 ? (
                    <ul className="space-y-2">
                      {category.subCategories.map(subCategory => (
                        <li key={subCategory.id}>
                          <Link
                            href={`/categories/${category.slug}/${subCategory.slug}`}
                            className="text-primary-800 hover:text-accent-600"
                          >
                            {subCategory.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-primary-600">サブカテゴリーはありません。</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
