import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import PostCard from '@/components/ui/PostCard';
import CategoryList from '@/components/ui/CategoryList';

// Import API functions
import { getCategories, getSubCategoryPosts } from '@/lib/api';

// This is a wrapper component that handles the Promise-based params
export default async function SubCategoryPageWrapper(props: {
  params: Promise<{ slug: string; subSlug: string }>;
  searchParams?: Promise<{ page?: string; limit?: string }>;
}) {
  const params = await props.params;
  const searchParams = props.searchParams ? await props.searchParams : undefined;

  return <SubCategoryPage params={params} searchParams={searchParams} />;
}

// This is the actual component that renders the page
async function SubCategoryPage({
  params,
  searchParams,
}: {
  params: { slug: string; subSlug: string };
  searchParams?: { page?: string; limit?: string };
}) {
  // ページネーションパラメータの取得
  const page = searchParams?.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams?.limit ? parseInt(searchParams.limit) : 10;

  // データの取得
  const categoriesData = await getCategories();
  const category = categoriesData.find(cat => cat.slug === params.slug);

  // カテゴリーが見つからない場合
  if (!category) {
    return (
      <>
        <Header />
        <main>
          <section className="bg-primary-900 text-white py-12">
            <div className="container-custom">
              <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">カテゴリーが見つかりません</h1>
              <p className="text-lg text-primary-100 max-w-3xl">
                指定されたカテゴリーは存在しないか、削除された可能性があります。
              </p>
            </div>
          </section>
          <section className="py-12">
            <div className="container-custom">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">指定されたカテゴリーは見つかりませんでした。</p>
                    <Link href="/categories" className="btn btn-primary">
                      カテゴリー一覧に戻る
                    </Link>
                  </div>
                </div>
                <div className="space-y-8">
                  <CategoryList categories={categoriesData} />
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </>
    );
  }

  const subCategory = category.subCategories?.find(sub => sub.slug === params.subSlug);

  // サブカテゴリーが見つからない場合
  if (!subCategory) {
    return (
      <>
        <Header />
        <main>
          <section className="bg-primary-900 text-white py-12">
            <div className="container-custom">
              <Link href={`/categories/${category.slug}`} className="text-primary-200 hover:text-white mb-2 inline-block">
                {category.name}
              </Link>
              <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">サブカテゴリーが見つかりません</h1>
              <p className="text-lg text-primary-100 max-w-3xl">
                指定されたサブカテゴリーは存在しないか、削除された可能性があります。
              </p>
            </div>
          </section>
          <section className="py-12">
            <div className="container-custom">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">指定されたサブカテゴリーは見つかりませんでした。</p>
                    <Link href={`/categories/${category.slug}`} className="btn btn-primary">
                      {category.name}カテゴリーに戻る
                    </Link>
                  </div>
                </div>
                <div className="space-y-8">
                  <CategoryList categories={categoriesData} />

                  {category.subCategories && category.subCategories.length > 0 && (
                    <div className="bg-white rounded-lg shadow-md p-6">
                      <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">
                        {category.name}のサブカテゴリー
                      </h2>
                      <ul className="space-y-2">
                        {category.subCategories.map(sub => (
                          <li key={sub.id}>
                            <Link
                              href={`/categories/${category.slug}/${sub.slug}`}
                              className="text-primary-800 hover:text-accent-600"
                            >
                              {sub.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </>
    );
  }

  const { posts, meta } = await getSubCategoryPosts(params.slug, params.subSlug, page, limit);

  // ページネーションの計算
  const totalPages = meta.totalPages;
  const hasPrev = page > 1;
  const hasNext = page < totalPages;

  // ページネーションリンクの生成
  const paginationLinks = [];
  const maxPagesToShow = 5;

  let startPage = Math.max(1, page - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    paginationLinks.push(i);
  }

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-primary-900 text-white py-12">
          <div className="container-custom">
            <div className="flex flex-col">
              <Link href={`/categories/${category.slug}`} className="text-primary-200 hover:text-white mb-2">
                {category.name}
              </Link>
              <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">{subCategory.name}</h1>
              {subCategory.description && (
                <p className="text-lg text-primary-100 max-w-3xl">
                  {subCategory.description}
                </p>
              )}
            </div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-12">
          <div className="container-custom">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-serif font-medium mb-8">{subCategory.name}の記事</h2>

                {posts && posts.length > 0 ? (
                  <div className="space-y-6">
                    {posts.map((post, index) => (
                      <PostCard key={post.id} post={post} imageIndex={index} />
                    ))}
                  </div>
                ) : (
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-primary-600 mb-4">このサブカテゴリーにはまだ記事がありません。</p>
                    <p className="text-sm text-gray-500 mb-6">新しい記事が追加されるまでお待ちください。</p>
                    <div className="flex justify-center space-x-4">
                      <Link href={`/categories/${category.slug}`} className="btn btn-secondary">
                        {category.name}カテゴリーに戻る
                      </Link>
                      <Link href="/" className="btn btn-primary">
                        トップページに戻る
                      </Link>
                    </div>
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <nav className="inline-flex rounded-md shadow">
                      {hasPrev && (
                        <Link
                          href={`/categories/${params.slug}/${params.subSlug}?page=${page - 1}&limit=${limit}`}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        >
                          <span className="sr-only">前へ</span>
                          &laquo;
                        </Link>
                      )}

                      {paginationLinks.map((pageNum) => (
                        <Link
                          key={pageNum}
                          href={`/categories/${params.slug}/${params.subSlug}?page=${pageNum}&limit=${limit}`}
                          className={`relative inline-flex items-center px-4 py-2 border ${
                            pageNum === page
                              ? 'border-primary-500 bg-primary-50 text-primary-600'
                              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                          } text-sm font-medium`}
                        >
                          {pageNum}
                        </Link>
                      ))}

                      {hasNext && (
                        <Link
                          href={`/categories/${params.slug}/${params.subSlug}?page=${page + 1}&limit=${limit}`}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        >
                          <span className="sr-only">次へ</span>
                          &raquo;
                        </Link>
                      )}
                    </nav>
                  </div>
                )}
              </div>

              <div className="space-y-8">
                <CategoryList categories={categoriesData} />

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-serif font-medium mb-4 pb-2 border-b border-gray-200">
                    {category.name}のサブカテゴリー
                  </h2>
                  {category.subCategories && category.subCategories.length > 0 ? (
                    <ul className="space-y-2">
                      {category.subCategories.map(sub => (
                        <li key={sub.id}>
                          <Link
                            href={`/categories/${category.slug}/${sub.slug}`}
                            className={`text-primary-800 hover:text-accent-600 ${
                              sub.id === subCategory.id ? 'font-bold' : ''
                            }`}
                          >
                            {sub.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-primary-600">サブカテゴリーはありません。</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
