import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import { getCategories } from '@/lib/api';

export default async function CategoriesPage() {
  // カテゴリーデータを取得
  const categories = await getCategories();

  return (
    <>
      <Header />

      <main>
        {/* Hero Section */}
        <section className="bg-primary-900 text-white py-12">
          <div className="container-custom">
            <h1 className="text-3xl md:text-4xl font-serif font-medium mb-4">カテゴリー一覧</h1>
            <p className="text-lg text-primary-100 max-w-3xl">
              記事のカテゴリー一覧です。興味のあるカテゴリーを選んでください。
            </p>
          </div>
        </section>

        {/* Categories Section */}
        <section className="py-12">
          <div className="container-custom">
            {categories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categories.map((category) => (
                  <div key={category.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="p-6">
                      <h2 className="text-xl font-serif font-medium mb-3">
                        <Link href={`/categories/${category.slug}`} className="text-primary-900 hover:text-accent-700">
                          {category.name}
                        </Link>
                      </h2>
                      {category.description && (
                        <p className="text-primary-600 mb-4">
                          {category.description}
                        </p>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500">
                          記事数: {category._count?.posts || 0}
                        </span>
                        <Link
                          href={`/categories/${category.slug}`}
                          className="inline-flex items-center text-accent-600 hover:text-accent-800"
                        >
                          記事を見る
                          <svg className="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                          </svg>
                        </Link>
                      </div>

                      {/* サブカテゴリーがある場合は表示 */}
                      {category.subCategories && category.subCategories.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <h3 className="text-sm font-medium text-gray-700 mb-2">サブカテゴリー:</h3>
                          <ul className="space-y-1">
                            {category.subCategories.map((subCategory) => (
                              <li key={subCategory.id}>
                                <Link
                                  href={`/categories/${category.slug}/${subCategory.slug}`}
                                  className="text-sm text-primary-600 hover:text-accent-600"
                                >
                                  {subCategory.name}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow-md">
                <p className="text-primary-600">カテゴリーはまだありません。</p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
}
