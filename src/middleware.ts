import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Edge Runtimeでは jsonwebtoken が使えないため、簡易的なトークン存在チェックのみを行う
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  console.log('Middleware called for path:', pathname);

  // ログインページ自体へのアクセスの場合は何もしない
  if (pathname === '/admin/login') {
    console.log('ログインページへのアクセス - 認証チェックをスキップ');
    return NextResponse.next();
  }

  // Check if the path is for admin routes (except login)
  const isAdminRoute = pathname.startsWith('/admin') && !pathname.startsWith('/admin/login');

  if (isAdminRoute) {
    console.log('管理者ルートへのアクセス:', pathname);

    // APIエンドポイントの場合は、Authorizationヘッダーでの認証はAPIルート内で行うため、ここではスキップ
    if (pathname.startsWith('/api/admin/')) {
      console.log('APIエンドポイントへのアクセス - ミドルウェアでの認証チェックをスキップ');
      return NextResponse.next();
    }

    // クライアントサイドのルートの場合は、クッキーからトークンを取得
    const token = request.cookies.get('adminToken')?.value;
    console.log('Cookieからトークンを取得:', token ? '取得成功' : '取得失敗');

    // トークンがない場合はログインページにリダイレクト
    if (!token) {
      const url = new URL('/admin/login', request.url);
      console.log('トークンがないため、ログインページにリダイレクト:', url.toString());
      return NextResponse.redirect(url);
    }

    // Edge Runtimeでは詳細な検証はできないため、トークンの存在チェックのみを行う
    // 詳細な検証はクライアントサイドとAPIルート内で行う
    console.log('トークンが存在するため、アクセスを許可します');
    return NextResponse.next();
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/admin/:path*'],
};
