import { getServerSession } from 'next-auth/next';
import { NextAuthOptions } from 'next-auth';
import { redirect } from 'next/navigation';
import CredentialsProvider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';
import { DefaultSession } from 'next-auth';

// NextAuth.jsのSession型を拡張
declare module "next-auth" {
  interface Session {
    lastUpdated?: string;
    user: {
      id: string;
      email: string;
      name: string;
    } & DefaultSession["user"]
  }
}

// JWT型を拡張
declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    email?: string;
    name?: string;
    lastLogin?: string;
  }
}

// 環境変数のログ出力（デバッグ用）
console.log('NextAuth環境変数:', {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
  NODE_ENV: process.env.NODE_ENV,
});

// 環境に基づいて適切なセキュリティ設定を決定
const isProduction = process.env.NODE_ENV === 'production';
const useSecureCookies = isProduction;
const cookieDomain = undefined; // デフォルトではブラウザに任せる

// サイトURLを決定（リダイレクト用）
const siteUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

// Get the auth options from the NextAuth configuration
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        console.log('認証処理開始 - authorize関数が呼び出されました');
        console.log('環境変数:', {
          NODE_ENV: process.env.NODE_ENV,
          NEXTAUTH_URL: process.env.NEXTAUTH_URL,
          NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
          DATABASE_URL_EXISTS: !!process.env.DATABASE_URL,
        });

        if (!credentials?.email || !credentials?.password) {
          console.log('認証エラー: メールアドレス/ユーザー名またはパスワードが提供されていません');
          return null;
        }

        console.log('認証試行:', {
          email_or_username: credentials.email,
          password_length: credentials.password.length
        });

        try {
          // データベース接続の確認
          console.log('データベース接続を確認中...');
          await prisma.$queryRaw`SELECT 1 as connection_test`;
          console.log('データベース接続成功');

          // 通常のユーザー認証ロジック（メールアドレスで検索）
          console.log('メールアドレスでユーザーを検索中:', credentials.email);
          let user = await prisma.user.findUnique({
            where: {
              email: credentials.email,
            },
          });

          // メールアドレスで見つからない場合は、ユーザー名で検索
          if (!user) {
            console.log('メールアドレスでユーザーが見つかりませんでした。ユーザー名で検索します:', credentials.email);
            user = await prisma.user.findUnique({
              where: {
                username: credentials.email,
              },
            });
          }

          if (!user) {
            console.log('ユーザーが見つかりませんでした:', credentials.email);
            return null;
          }

          console.log('ユーザーが見つかりました:', {
            id: user.id,
            username: user.username,
            email: user.email,
            isAdmin: user.isAdmin,
            password_hash_length: user.password.length,
            password_hash_preview: `${user.password.substring(0, 10)}...`
          });

          try {
            console.log('パスワード検証を開始します');
            console.log('入力されたパスワード長:', credentials.password.length);
            console.log('保存されているハッシュ長:', user.password.length);

            const isPasswordValid = await bcrypt.compare(
              credentials.password,
              user.password
            );

            console.log('パスワード検証結果:', isPasswordValid);

            if (!isPasswordValid) {
              console.log('パスワードが一致しません');
              return null;
            }

            console.log('認証成功、ユーザー情報を返します');
            return {
              id: user.id,
              email: user.email,
              name: user.username,
            };
          } catch (error) {
            console.error('パスワード検証中にエラーが発生しました:', error);
            return null;
          }
        } catch (dbError) {
          console.error('データベース操作中にエラーが発生しました:', dbError);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 8 * 60 * 60, // 8時間に短縮（より頻繁な再認証を要求）
  },
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
    signOut: '/admin/login',
  },
  // リバースプロキシ環境でも正しくURLを解決するための設定
  useSecureCookies: useSecureCookies,
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: useSecureCookies,
        domain: cookieDomain,
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: useSecureCookies,
        domain: cookieDomain,
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: useSecureCookies,
        domain: cookieDomain,
      },
    },
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email || undefined;
        token.name = user.name || undefined;
        
        // 最後のログイン時間を記録
        token.lastLogin = new Date().toISOString();
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        
        // セッション有効期限の更新時間を追加
        session.lastUpdated = new Date().toISOString();
      }
      return session;
    },
    // リダイレクトURLを処理するコールバック
    async redirect({ url, baseUrl }) {
      console.log('Redirect callback called with:', { url, baseUrl });
      console.log('環境変数:', {
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
      });

      // サイトのベースURLを取得
      const siteUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_SITE_URL || baseUrl;
      console.log('Using site URL:', siteUrl);

      // ログイン後は常にダッシュボードにリダイレクト
      if (url.includes('/admin/login') || url.includes('callbackUrl')) {
        console.log('Login URL detected, redirecting to dashboard');
        return `${siteUrl}/admin/dashboard`;
      }

      // 相対URLの場合は、絶対URLに変換
      if (url.startsWith('/')) {
        const absoluteUrl = `${siteUrl}${url}`;
        console.log('Converting relative URL to absolute:', absoluteUrl);
        return absoluteUrl;
      }

      try {
        // URLオブジェクトを使用して解析
        const parsedUrl = new URL(url);
        console.log('Parsed URL:', {
          protocol: parsedUrl.protocol,
          hostname: parsedUrl.hostname,
          port: parsedUrl.port,
          pathname: parsedUrl.pathname,
        });

        // 許可されたホスト名のリスト
        const allowedHosts = [
          'zobutsusha-629524443569.us-central1.run.app',
          'www.zobutsusha.com',
          'zobutsusha.com',
        ];

        // 許可されたホスト名かどうかをチェック
        if (allowedHosts.includes(parsedUrl.hostname)) {
          console.log('Allowed host detected, returning full URL');
          return url;
        }

        // 許可されていないホストの場合は、ダッシュボードにリダイレクト
        console.log('Unauthorized host detected, redirecting to dashboard');
        return `${siteUrl}/admin/dashboard`;
      } catch (error) {
        console.error('Error parsing URL:', error);
        // エラーが発生した場合はデフォルトのダッシュボードにリダイレクト
        return `${siteUrl}/admin/dashboard`;
      }
    },
  },
  debug: process.env.NODE_ENV === 'development',
  // セッションの自動クリーンアップと不正使用検出
  events: {
    async signOut({ token }) {
      console.log('ユーザーがサインアウトしました', { token: token?.sub });
    },
    async session({ session, token }) {
      // セッションが取得されるたびに呼び出される（不正使用の検出などに使用可能）
      console.log('セッションが取得されました', { userId: token?.sub });
    },
  },
};

// Get the session on the server
export const getSession = async () => {
  return await getServerSession(authOptions);
};

// Check if the user is authenticated
export const isAuthenticated = async () => {
  const session = await getSession();
  return !!session;
};

// Redirect if not authenticated
export const requireAuth = async () => {
  const isAuthed = await isAuthenticated();

  if (!isAuthed) {
    redirect('/admin/login');
  }
};

// Redirect if authenticated
export const redirectIfAuthenticated = async (redirectTo: string = '/admin/dashboard') => {
  const isAuthed = await isAuthenticated();

  if (isAuthed) {
    redirect(redirectTo);
  }
};
