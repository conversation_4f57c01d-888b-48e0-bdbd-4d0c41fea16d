/**
 * 記事内容の画像表示を修正するユーティリティ関数
 */

/**
 * Markdown形式の画像参照をHTML img要素に変換する
 * 例: ![画像](data:image/png;base64,...) -> <img src="data:image/png;base64,..." alt="画像" />
 */
export function convertMarkdownImagesToHtml(content: string): string {
  // Markdown形式の画像参照を検出する正規表現
  const markdownImageRegex = /!\[(.*?)\]\((data:image\/[^;]+;base64,[^)]+)\)/g;

  // Markdown形式の画像参照をHTML img要素に置換
  return content.replace(markdownImageRegex, (match, alt, src) => {
    return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto;" />`;
  });
}

/**
 * 記事内容を表示用に処理する
 * - Markdown形式の画像参照をHTML img要素に変換
 * - その他の必要な処理を追加可能
 */
export function processContentForDisplay(content: string): string {
  if (!content) return '';

  // 画像参照を処理
  let processedContent = convertMarkdownImagesToHtml(content);

  // Markdownの基本的な書式を変換
  // 太字
  processedContent = processedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // 斜体
  processedContent = processedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 見出し (## のみ対応)
  processedContent = processedContent.replace(/^## (.*?)$/gm, '<h3>$1</h3>');

  // リスト
  processedContent = processedContent.replace(/^- (.*?)$/gm, '<li>$1</li>');
  processedContent = processedContent.replace(/(<li>.*?<\/li>\n?)+/g, '<ul>$&</ul>');

  // 番号付きリスト
  processedContent = processedContent.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
  processedContent = processedContent.replace(/(<li>.*?<\/li>\n?)+/g, '<ol>$&</ol>');

  // 引用
  processedContent = processedContent.replace(/^> (.*?)$/gm, '<blockquote>$1</blockquote>');

  // リンク
  processedContent = processedContent.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

  // 段落
  processedContent = processedContent.replace(/(?:\r\n|\r|\n){2,}/g, '</p><p>');
  processedContent = `<p>${processedContent}</p>`;

  return processedContent;
}
