'use client';

// Maximum dimensions for original and thumbnail images
const MAX_ORIGINAL_WIDTH = 1200;
const MAX_ORIGINAL_HEIGHT = 1200;
const THUMBNAIL_WIDTH = 300;
const THUMBNAIL_HEIGHT = 300;

/**
 * Convert a file to a Base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

/**
 * Extract the mime type from a Base64 string
 */
export const getMimeType = (base64String: string): string => {
  const match = base64String.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
  return match ? match[1] : '';
};

/**
 * Calculate the size of a Base64 image in KB
 */
export const getBase64Size = (base64String: string): number => {
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');
  const sizeInBytes = (base64Data.length * 3) / 4;
  return Math.round(sizeInBytes / 1024); // Convert to KB
};

/**
 * Resize an image in the browser using canvas
 */
export const resizeImageInBrowser = async (
  base64Image: string,
  maxWidth: number,
  maxHeight: number
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;

          if (width > height) {
            width = maxWidth;
            height = Math.round(maxWidth / aspectRatio);
          } else {
            height = maxHeight;
            width = Math.round(maxHeight * aspectRatio);
          }
        }

        // Create canvas and resize
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        // Draw image on canvas
        ctx.drawImage(img, 0, 0, width, height);

        // Get resized image as base64
        const mimeType = getMimeType(base64Image) || 'image/jpeg';
        const resizedBase64 = canvas.toDataURL(mimeType, 0.9); // 0.9 quality

        resolve(resizedBase64);
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = base64Image;
    } catch (error) {
      console.error('Error resizing image in browser:', error);
      resolve(base64Image); // Return original if resize fails
    }
  });
};

/**
 * Process an image for storage using browser-based resizing
 */
/**
 * シンプルで堅牢な画像圧縮関数
 */
export const compressImageForMobile = async (
  base64Image: string,
  maxSizeKB: number = 800 // 800KB
): Promise<string> => {
  // 既に十分小さい場合はそのまま返す
  const currentSize = getBase64Size(base64Image);
  if (currentSize <= maxSizeKB) {
    return base64Image;
  }

  console.log(`圧縮前の画像サイズ: ${currentSize}KB`);

  return new Promise((resolve, reject) => {
    try {
      const img = new Image();

      img.onload = () => {
        try {
          // 元の画像サイズを取得
          let { width, height } = img;
          console.log(`元の画像サイズ: ${width}x${height}`);

          // 解像度を下げる（800pxを上限に）
          const maxDimension = 800;
          if (width > height && width > maxDimension) {
            height = Math.round((height * maxDimension) / width);
            width = maxDimension;
          } else if (height > maxDimension) {
            width = Math.round((width * maxDimension) / height);
            height = maxDimension;
          }

          console.log(`リサイズ後のサイズ: ${width}x${height}`);

          // キャンバスを作成して描画
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            throw new Error('Could not get canvas context');
          }

          // 画像を描画
          ctx.drawImage(img, 0, 0, width, height);

          // サイズに基づいて品質を調整
          let quality = 0.8; // デフォルト品質

          if (currentSize > maxSizeKB * 2) {
            quality = 0.6;
          }
          if (currentSize > maxSizeKB * 5) {
            quality = 0.4;
          }
          if (currentSize > maxSizeKB * 10) {
            quality = 0.2;
          }

          console.log(`使用する圧縮品質: ${quality}`);

          // 常にJPEGに変換して圧縮率を高める
          const compressedBase64 = canvas.toDataURL('image/jpeg', quality);

          // 圧縮後のサイズをチェック
          const newSize = getBase64Size(compressedBase64);
          console.log(`圧縮後のサイズ: ${newSize}KB (${Math.round((newSize / currentSize) * 100)}%)`);

          resolve(compressedBase64);
        } catch (canvasError) {
          console.error('Canvas処理エラー:', canvasError);
          // エラーが発生した場合は元の画像を返す
          resolve(base64Image);
        }
      };

      img.onerror = (e) => {
        console.error('画像の読み込みに失敗:', e);
        // エラーが発生した場合は元の画像を返す
        resolve(base64Image);
      };

      img.src = base64Image;
    } catch (error) {
      console.error('画像圧縮エラー:', error);
      // エラーが発生した場合は元の画像を返す
      resolve(base64Image);
    }
  });
};

export const processImageInBrowser = async (
  base64Image: string
): Promise<{ original: string; thumbnail: string }> => {
  // Resize original image if needed
  const original = await resizeImageInBrowser(
    base64Image,
    MAX_ORIGINAL_WIDTH,
    MAX_ORIGINAL_HEIGHT
  );

  // Create thumbnail
  const thumbnail = await resizeImageInBrowser(
    base64Image,
    THUMBNAIL_WIDTH,
    THUMBNAIL_HEIGHT
  );

  return { original, thumbnail };
};
