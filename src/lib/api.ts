// APIからデータを取得するための関数
import { prisma } from '@/lib/prisma';

// 設定を取得する関数
export async function getSettings(keys?: string[]) {
  try {
    const where = keys ? { key: { in: keys } } : {};

    const settings = await prisma.siteSettings.findMany({
      where,
      orderBy: {
        key: 'asc',
      },
    });

    // Convert to key-value object for easier consumption
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    return settingsObject;
  } catch (error) {
    console.error('Error fetching settings:', error);
    return {};
  }
}

// カテゴリーを取得する関数
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        subCategories: {
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
          },
        },
        _count: {
          select: {
            posts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// 記事を取得する関数
export async function getPosts({
  page = 1,
  limit = 10,
  category = null,
  subcategory = null,
  featured = false,
}: {
  page?: number;
  limit?: number;
  category?: string | null;
  subcategory?: string | null;
  featured?: boolean;
} = {}) {
  try {
    // 検索条件の構築
    const where: any = {
      published: true,
    };

    // カテゴリーフィルター
    if (category) {
      where.category = {
        slug: category,
      };
    }

    // サブカテゴリーフィルター
    if (subcategory) {
      where.subCategory = {
        slug: subcategory,
      };
    }

    // 注目記事フィルター
    if (featured) {
      // featuredフラグが設定されている記事を取得
      where.featured = true;
    }

    const skip = (page - 1) * limit;

    const posts = await prisma.post.findMany({
      where,
      orderBy: {
        publishedAt: 'desc',
      },
      take: limit,
      skip,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        subCategory: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            profileImage: true,
            isAdmin: true,
          },
        },
      },
    });

    const totalPosts = await prisma.post.count({
      where,
    });

    return {
      posts,
      meta: {
        total: totalPosts,
        page,
        limit,
        totalPages: Math.ceil(totalPosts / limit),
      },
    };
  } catch (error) {
    console.error('Error fetching posts:', error);
    return { posts: [], meta: { total: 0, page, limit, totalPages: 0 } };
  }
}

// 単一の記事を取得する関数
export async function getPost(slug: string) {
  try {
    const post = await prisma.post.findUnique({
      where: {
        slug,
      },
      include: {
        category: true,
        subCategory: true,
        images: true,
        user: {
          select: {
            id: true,
            username: true,
            bio: true,
            profileImage: true,
            detailedProfile: true,
            isAdmin: true,
          },
        },
      },
    });

    if (!post) {
      return null;
    }

    // 関連記事を取得（同じカテゴリーの他の記事）
    const relatedPosts = await prisma.post.findMany({
      where: {
        categoryId: post.categoryId,
        id: {
          not: post.id, // 現在の記事を除外
        },
        published: true,
      },
      take: 3,
      orderBy: {
        publishedAt: 'desc',
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featuredImage: true,
        publishedAt: true,
        category: {
          select: {
            name: true,
            slug: true,
          },
        },
      },
    });

    return {
      post,
      relatedPosts,
    };
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

// 注目記事を取得する関数
export async function getFeaturedPosts(limit = 3) {
  return getPosts({ limit, featured: true });
}

// 最新記事を取得する関数
export async function getRecentPosts(limit = 6) {
  return getPosts({ limit });
}

// カテゴリー別の記事を取得する関数
export async function getCategoryPosts(categorySlug: string, page = 1, limit = 10) {
  return getPosts({ category: categorySlug, page, limit });
}

// サブカテゴリー別の記事を取得する関数
export async function getSubCategoryPosts(categorySlug: string, subCategorySlug: string, page = 1, limit = 10) {
  return getPosts({ category: categorySlug, subcategory: subCategorySlug, page, limit });
}
