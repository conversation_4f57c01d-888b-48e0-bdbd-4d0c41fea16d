import sharp from 'sharp';

// 最大ファイルサイズ（1.5MB）をバイト単位で定義 - 800KBから引き上げ
const MAX_FILE_SIZE_BYTES = 1.5 * 1024 * 1024;

// 画像サイズの定義
const MAX_ORIGINAL_WIDTH = 1200;
const MAX_ORIGINAL_HEIGHT = 1200;
const THUMBNAIL_WIDTH = 300;
const THUMBNAIL_HEIGHT = 300;

// Sharpのグローバル設定
sharp.cache(false); // キャッシュを無効化してメモリ使用量を削減

/**
 * Extract the mime type from a Base64 string
 */
export const getMimeType = (base64String: string): string => {
  const match = base64String.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,/);
  return match ? match[1] : '';
};

/**
 * 画像をリサイズ、回転して最大サイズ800KBに制限しBase64形式に変換する
 * @param buffer 画像バッファ
 * @param options リサイズと回転のオプション
 * @returns Base64エンコードされた画像データ
 */
export async function resizeAndConvertToBase64(
  buffer: Buffer,
  options: {
    width: number,
    height: number,
    rotate?: number
  }
) {
  try {
    console.log(`処理開始: 入力バッファサイズ ${buffer.length / 1024}KB`);

    // メモリ使用量を最適化するSharpオプション
    let sharpInstance = sharp(buffer, {
      limitInputPixels: 25000000, // 入力ピクセル数の制限
      failOn: 'none', // エラー時も処理を続行
      density: 72, // 解像度を下げる
      sequentialRead: true, // メモリ使用量を削減
    });

    // メタデータを保持するオプションを設定
    sharpInstance = sharpInstance.withMetadata();

    // 回転角度が指定されている場合は回転を適用
    if (options.rotate !== undefined && options.rotate !== 0) {
      console.log(`画像を${options.rotate}度回転します`);
      // 回転を適用し、メタデータに回転情報を保存
      sharpInstance = sharpInstance.rotate(options.rotate);
    }

    // リサイズを適用
    console.log(`リサイズ: ${options.width}x${options.height}`);
    sharpInstance = sharpInstance.resize(options.width, options.height, {
      fit: 'inside',
      withoutEnlargement: true,
      kernel: 'nearest', // 最も高速で低メモリな方法
    });

    // 画像サイズが最大値を超えないようにクオリティを調整する
    let quality = 80;
    console.log(`JPEG変換開始: 品質=${quality}`);
    let outputBuffer = await sharpInstance.jpeg({
      quality,
      optimizeScans: true, // 最適化
      mozjpeg: false, // mozjpegは無効化（メモリ使用量が多い）
    }).toBuffer();

    console.log(`初回変換後のサイズ: ${outputBuffer.length / 1024}KB`);

    // 画像サイズが大きすぎる場合は、サイズに収まるまでクオリティを下げる
    let attempts = 0;
    while (outputBuffer.length > MAX_FILE_SIZE_BYTES && quality > 10 && attempts < 5) {
      attempts++;
      quality -= 10; // より大きなステップで品質を下げる
      console.log(`サイズ超過: 品質を${quality}に下げて再試行 (${attempts}/5)`);

      outputBuffer = await sharpInstance.jpeg({
        quality,
        optimizeScans: true,
        mozjpeg: false,
      }).toBuffer();

      console.log(`再圧縮後のサイズ: ${outputBuffer.length / 1024}KB`);
    }

    // 最終結果をログに出力
    console.log(`最終画像サイズ: ${outputBuffer.length / 1024}KB (品質: ${quality})`);

    return `data:image/jpeg;base64,${outputBuffer.toString('base64')}`;
  } catch (error) {
    console.error('Image processing error:', error);
    console.error('Error details:', error instanceof Error ? error.stack : String(error));

    // より詳細なエラーメッセージを提供
    if (error instanceof Error) {
      throw new Error(`Failed to process image: ${error.message}`);
    } else {
      throw new Error(`Failed to process image: Unknown error`);
    }
  }
}

/**
 * サムネイル画像を作成する
 * @param buffer 元の画像バッファ
 * @param rotate 回転角度（オプション）
 * @returns Base64エンコードされたサムネイル画像
 */
export async function createThumbnail(buffer: Buffer, rotate?: number) {
  return resizeAndConvertToBase64(buffer, {
    width: THUMBNAIL_WIDTH,
    height: THUMBNAIL_HEIGHT,
    rotate
  });
}

/**
 * 画像を最適化する
 * @param buffer 元の画像バッファ
 * @param rotate 回転角度（オプション）
 * @returns Base64エンコードされた最適化画像
 */
export async function optimizeImage(buffer: Buffer, rotate?: number) {
  return resizeAndConvertToBase64(buffer, {
    width: MAX_ORIGINAL_WIDTH,
    height: MAX_ORIGINAL_HEIGHT,
    rotate
  });
}

/**
 * Base64文字列からバッファに変換する
 * @param base64String Base64エンコードされた文字列
 * @returns バッファ
 */
export function base64ToBuffer(base64String: string): Buffer {
  // Base64のヘッダー部分を除去
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');
  // Base64をバッファに変換
  return Buffer.from(base64Data, 'base64');
}

/**
 * Calculate the size of a Base64 image in KB
 */
export const getBase64Size = (base64String: string): number => {
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');
  const sizeInBytes = (base64Data.length * 3) / 4;
  return Math.round(sizeInBytes / 1024); // Convert to KB
};
