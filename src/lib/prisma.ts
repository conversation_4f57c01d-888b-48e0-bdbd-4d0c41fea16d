import { PrismaClient } from '@prisma/client';

// データベース接続の詳細をログに出力（デバッグ用）
console.log('DATABASE_URL environment variable exists:', !!process.env.DATABASE_URL);
if (process.env.DATABASE_URL) {
  // 接続文字列の一部を安全にログ出力（パスワードは隠す）
  const dbUrlParts = process.env.DATABASE_URL.split('@');
  if (dbUrlParts.length > 1) {
    console.log('Database host part:', dbUrlParts[1]);
  } else {
    console.log('DATABASE_URL format is unexpected');
  }
} else {
  console.error('DATABASE_URL is not defined! Database connections will fail.');
}

// ビルド時かどうかを判断（NODE_ENV=productionかつDATABASE_URLがdummyで始まる場合はビルド時と判断）
const isBuildTime =
  process.env.NODE_ENV === 'production' &&
  process.env.DATABASE_URL?.startsWith('postgresql://dummy');

// Prismaクライアントのグローバルインスタンス
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Prismaクライアントの初期化
export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    // 接続タイムアウトを設定（Cloud Run環境用）
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

// 開発環境ではグローバルインスタンスを再利用
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// 接続テスト（デバッグ用）
async function testConnection() {
  // ビルド時は接続テストをスキップ
  if (isBuildTime) {
    console.log('Build time detected, skipping database connection test');
    return;
  }

  try {
    // 簡単なクエリを実行して接続をテスト
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('Database connection test successful:', result);
  } catch (error) {
    console.error('Database connection test failed:', error);
  }
}

// 本番環境以外で接続テストを実行
if (process.env.NODE_ENV !== 'production') {
  testConnection();
}
