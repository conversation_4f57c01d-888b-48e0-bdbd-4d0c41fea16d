import { prisma } from '@/lib/prisma';

export async function getAdminUser() {
  try {
    // Find a user with isAdmin flag set to true
    const adminUser = await prisma.user.findFirst({
      where: {
        isAdmin: true,
      },
      select: {
        id: true,
        username: true,
        bio: true,
        profileImage: true,
      },
    });

    return adminUser;
  } catch (error) {
    console.error('Error fetching admin user:', error);
    return null;
  }
}
