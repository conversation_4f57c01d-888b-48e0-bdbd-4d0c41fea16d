import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

// APIリクエストからトークンを取得する関数
export function getTokenFromRequest(request: Request | NextRequest): string | null {
  // Authorizationヘッダーからトークンを取得
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.split(' ')[1];
  }
  
  // Cookieからトークンを取得（NextRequestの場合）
  if ('cookies' in request) {
    return request.cookies.get('adminToken')?.value || null;
  }
  
  return null;
}

// トークンを検証する関数
export async function verifyToken(token: string): Promise<{ 
  isValid: boolean; 
  userId?: string;
  isAdmin?: boolean;
  error?: string;
}> {
  try {
    // トークンの検証
    const decoded = verify(
      token, 
      process.env.NEXTAUTH_SECRET || 'fallback-secret-key'
    ) as {
      id: string;
      isAdmin: boolean;
    };
    
    // ユーザーIDの取得
    const userId = decoded.id;
    
    // データベースでユーザーを確認
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        id: true,
        isAdmin: true,
      },
    });
    
    if (!user) {
      return { isValid: false, error: 'User not found' };
    }
    
    return { isValid: true, userId: user.id, isAdmin: user.isAdmin };
  } catch (error) {
    console.error('トークン検証エラー:', error);
    return { isValid: false, error: 'Invalid token' };
  }
}

// APIリクエストの認証を確認する関数
export async function authenticateRequest(request: Request | NextRequest): Promise<{
  isAuthenticated: boolean;
  userId?: string;
  isAdmin?: boolean;
  error?: string;
}> {
  // トークンの取得
  const token = getTokenFromRequest(request);
  
  if (!token) {
    return { isAuthenticated: false, error: 'No token provided' };
  }
  
  // トークンの検証
  const verification = await verifyToken(token);
  
  return {
    isAuthenticated: verification.isValid,
    userId: verification.userId,
    isAdmin: verification.isAdmin,
    error: verification.error,
  };
}

// 認証エラーレスポンスを生成する関数
export function createAuthErrorResponse(error: string = 'Unauthorized'): NextResponse {
  return NextResponse.json(
    { error },
    { status: 401 }
  );
}

// 管理者権限エラーレスポンスを生成する関数
export function createAdminErrorResponse(): NextResponse {
  return NextResponse.json(
    { error: 'Admin privileges required' },
    { status: 403 }
  );
}
