import imageCompression from 'browser-image-compression';

// 目標ファイルサイズ（1.5MB）をバイト単位で定義 - 800KBから引き上げ
const TARGET_FILE_SIZE_BYTES = 1.5 * 1024 * 1024;
const FALLBACK_TARGET_SIZE_BYTES = 800 * 1024; // フォールバック時の目標サイズ

/**
 * Base64文字列のサイズをKB単位で取得する
 * @param base64 Base64文字列
 * @returns サイズ（KB）
 */
export const getBase64Size = (base64: string): number => {
  // Base64のヘッダー部分（data:image/jpeg;base64,など）を除去
  const base64Data = base64.split(',')[1] || base64.replace(/^data:image\/\w+;base64,/, '');

  // Base64のサイズを計算（Base64は4文字で3バイトを表現）
  const sizeInBytes = Math.ceil((base64Data.length * 3) / 4);

  // KB単位に変換して返す
  return Math.round(sizeInBytes / 1024);
};

/**
 * 画像ファイルを圧縮する共通ユーティリティ関数
 * @param file 圧縮する画像ファイル
 * @param onCompressionStart 圧縮開始時のコールバック関数（オプション）
 * @param onCompressionEnd 圧縮終了時のコールバック関数（オプション）
 * @returns 圧縮された画像ファイル
 */
export const compressImage = async (
  file: File,
  onCompressionStart?: () => void,
  onCompressionEnd?: () => void
): Promise<File> => {
  if (onCompressionStart) onCompressionStart();

  try {
    // ファイルサイズをログに記録
    const originalSizeKB = Math.round(file.size / 1024);
    console.log('元のサイズ: ', originalSizeKB, 'KB');

    // 既に十分小さい場合はそのまま返す（500KB以下）
    if (file.size <= 500 * 1024) {
      console.log('ファイルサイズが十分小さいため、圧縮をスキップします');
      return file;
    }

    // ファイルサイズに応じて圧縮レベルを調整
    let quality = 0.8; // デフォルトの品質
    let maxWidth = 1920; // デフォルトの最大幅

    // 大きなファイルほど強く圧縮
    if (file.size > 5 * 1024 * 1024) { // 5MB超
      quality = 0.6;
      maxWidth = 1600;
    } else if (file.size > 2 * 1024 * 1024) { // 2MB超
      quality = 0.7;
      maxWidth = 1800;
    }

    // 圧縮オプションを設定
    const options = {
      maxSizeMB: 1.5, // 最大サイズを1.5MBに制限（サーバー側でさらに圧縮）
      maxWidthOrHeight: maxWidth, // 最大幅または高さを制限
      useWebWorker: true, // WebWorkerを使用して処理を非同期化
      initialQuality: quality, // 初期品質
    };

    // 圧縮実行
    let compressedFile = await imageCompression(file, options);

    // サイズ変化をログに記録
    const compressedSizeKB = Math.round(compressedFile.size / 1024);
    console.log('圧縮後サイズ: ', compressedSizeKB, 'KB');
    console.log('圧縮率: ', Math.round((compressedFile.size / file.size) * 100), '%');

    // 目標サイズより大きい場合は再圧縮を試みる
    if (compressedFile.size > TARGET_FILE_SIZE_BYTES && compressedFile.size > 500 * 1024) {
      console.log('目標サイズに達していないため、さらに圧縮を試みます');

      // より強い圧縮設定
      const secondOptions = {
        maxSizeMB: 0.8, // 目標サイズを800KBに設定
        maxWidthOrHeight: 1200, // 解像度をさらに下げる
        useWebWorker: true,
        initialQuality: 0.6, // 品質を下げる
      };

      try {
        // 再圧縮
        const recompressedFile = await imageCompression(compressedFile, secondOptions);
        const recompressedSizeKB = Math.round(recompressedFile.size / 1024);
        console.log('再圧縮後サイズ: ', recompressedSizeKB, 'KB');
        console.log('再圧縮率: ', Math.round((recompressedFile.size / compressedFile.size) * 100), '%');

        compressedFile = recompressedFile;
      } catch (recompressError) {
        console.error('再圧縮エラー:', recompressError);
        // 再圧縮に失敗した場合は最初の圧縮結果を使用
      }
    }

    return compressedFile;
  } catch (error) {
    console.error('画像圧縮エラー:', error);
    throw error;
  } finally {
    if (onCompressionEnd) onCompressionEnd();
  }
};

// 注: getBase64Size関数は上部で既に定義されています

/**
 * Base64文字列をFileオブジェクトに変換する
 * @param base64String Base64エンコードされた文字列
 * @param filename ファイル名
 * @returns Fileオブジェクト
 */
export const base64ToFile = (base64String: string, filename: string): File => {
  const arr = base64String.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
};

/**
 * ファイルをBase64文字列に変換する
 * @param file 変換するファイル
 * @returns Base64エンコードされた文字列
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

/**
 * Canvas APIを使用して画像を圧縮する（フォールバック用）
 * @param file 圧縮する画像ファイル
 * @param maxWidth 最大幅
 * @param maxHeight 最大高さ
 * @param quality 品質（0〜1）
 * @returns 圧縮された画像のBase64文字列
 */
export const compressImageWithCanvas = async (
  file: File,
  maxWidth = 1200,
  maxHeight = 1200,
  quality = 0.8
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // ファイルサイズをログに記録
      const originalSizeKB = Math.round(file.size / 1024);
      console.log('Canvas圧縮前のサイズ: ', originalSizeKB, 'KB');

      // 既に十分小さい場合はそのまま返す（500KB以下）
      if (file.size <= 500 * 1024) {
        console.log('ファイルサイズが十分小さいため、Canvas圧縮をスキップします');
        fileToBase64(file).then(resolve).catch(reject);
        return;
      }

      const img = new Image();
      img.onload = () => {
        try {
          // 画像の元のサイズを取得
          let width = img.width;
          let height = img.height;

          // アスペクト比を維持しながらリサイズ
          if (width > maxWidth || height > maxHeight) {
            const aspectRatio = width / height;
            if (width > height) {
              width = maxWidth;
              height = Math.round(width / aspectRatio);
            } else {
              height = maxHeight;
              width = Math.round(height * aspectRatio);
            }
          }

          // Canvasを作成
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }

          // 画像を描画
          ctx.drawImage(img, 0, 0, width, height);

          // ファイルサイズに応じて品質を調整
          let adjustedQuality = quality;
          if (originalSizeKB > 5000) { // 5MB超
            adjustedQuality = 0.5;
          } else if (originalSizeKB > 2000) { // 2MB超
            adjustedQuality = 0.6;
          } else if (originalSizeKB > 1000) { // 1MB超
            adjustedQuality = 0.7;
          }

          // 圧縮して出力
          const base64 = canvas.toDataURL('image/jpeg', adjustedQuality);

          // 圧縮後のサイズをログに記録
          const compressedSizeKB = getBase64Size(base64);
          console.log('Canvas圧縮後のサイズ: ', compressedSizeKB, 'KB');
          console.log('Canvas圧縮率: ', Math.round((compressedSizeKB / originalSizeKB) * 100), '%');

          // 目標サイズより大きい場合は再圧縮を試みる
          if (compressedSizeKB * 1024 > FALLBACK_TARGET_SIZE_BYTES && compressedSizeKB > 500) {
            console.log('目標サイズに達していないため、さらにCanvas圧縮を試みます');

            // より強い圧縮設定
            const lowerQuality = adjustedQuality * 0.7;
            const secondBase64 = canvas.toDataURL('image/jpeg', lowerQuality);

            const secondSizeKB = getBase64Size(secondBase64);
            console.log('Canvas再圧縮後のサイズ: ', secondSizeKB, 'KB');
            console.log('Canvas再圧縮率: ', Math.round((secondSizeKB / compressedSizeKB) * 100), '%');

            resolve(secondBase64);
          } else {
            resolve(base64);
          }
        } catch (canvasError) {
          console.error('Canvas処理エラー:', canvasError);
          // エラーが発生した場合は元のファイルをBase64に変換して返す
          fileToBase64(file).then(resolve).catch(reject);
        }
      };

      img.onerror = (error) => {
        console.error('画像の読み込みに失敗:', error);
        // エラーが発生した場合は元のファイルをBase64に変換して返す
        fileToBase64(file).then(resolve).catch(reject);
      };

      // FileをURLに変換して画像を読み込む
      img.src = URL.createObjectURL(file);
    } catch (error) {
      console.error('Canvas圧縮エラー:', error);
      // エラーが発生した場合は元のファイルをBase64に変換して返す
      fileToBase64(file).then(resolve).catch(reject);
    }
  });
};
