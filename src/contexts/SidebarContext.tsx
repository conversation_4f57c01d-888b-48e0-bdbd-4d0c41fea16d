'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

type SidebarContextType = {
  isOpen: boolean;
  toggle: () => void;
  open: () => void;
  close: () => void;
};

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  const toggle = () => {
    console.log('サイドバーのトグルが呼び出されました。現在の状態:', isOpen);
    setIsOpen(prev => !prev);
  };

  const open = () => {
    console.log('サイドバーを開く関数が呼び出されました');
    setIsOpen(true);
    console.log('サイドバーの状態を true に設定しました');
  };

  const close = () => {
    console.log('サイドバーを閉じる関数が呼び出されました');
    setIsOpen(false);
  };

  return (
    <SidebarContext.Provider value={{ isOpen, toggle, open, close }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
