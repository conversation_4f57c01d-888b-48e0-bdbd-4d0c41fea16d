'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 認証ユーザーの型定義
type AdminUser = {
  id: string;
  username: string;
  email: string;
  isAdmin: boolean;
};

// 認証コンテキストの型定義
type AdminAuthContextType = {
  user: AdminUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
};

// 認証コンテキストの作成
const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

// 認証プロバイダーコンポーネント
export function AdminAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AdminUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 初期化時に認証状態を確認
  useEffect(() => {
    const initAuth = async () => {
      await checkAuth();
      setIsLoading(false);
    };

    initAuth();
  }, []);

  // Cookieを取得する関数
  const getCookie = (name: string): string | null => {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  };

  // Cookieを設定する関数
  const setCookie = (name: string, value: string, days: number = 1) => {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = `expires=${date.toUTCString()}`;
    
    // 本番環境（HTTPS）の場合はSecure属性を追加
    const isProduction = window.location.protocol === 'https:';
    const secureFlag = isProduction ? 'Secure;' : '';
    const sameSite = isProduction ? 'SameSite=Lax' : 'SameSite=Strict';
    
    document.cookie = `${name}=${value};${expires};path=/;${secureFlag}${sameSite}`;
  };

  // Cookieを削除する関数
  const deleteCookie = (name: string) => {
    const isProduction = window.location.protocol === 'https:';
    const secureFlag = isProduction ? 'Secure;' : '';
    const sameSite = isProduction ? 'SameSite=Lax' : 'SameSite=Strict';
    
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;${secureFlag}${sameSite}`;
  };

  // 認証状態の確認
  const checkAuth = async (): Promise<boolean> => {
    try {
      console.log('認証状態チェック開始');
      
      // ローカルストレージからトークンを取得
      let token = localStorage.getItem('adminToken');
      console.log('ローカルストレージからのトークン:', token ? 'あり' : 'なし');

      // ローカルストレージにトークンがない場合はCookieから取得
      if (!token) {
        token = getCookie('adminToken');
        console.log('Cookieからのトークン:', token ? 'あり' : 'なし');

        // Cookieにトークンがある場合はローカルストレージにも保存
        if (token) {
          localStorage.setItem('adminToken', token);
          console.log('Cookieからトークンをローカルストレージに復元');
        } else {
          console.log('トークンが見つかりません');
          setUser(null);
          setIsAuthenticated(false);
          return false;
        }
      } else {
        // ローカルストレージにトークンがある場合はCookieにも保存
        setCookie('adminToken', token, 1);
        console.log('ローカルストレージからトークンをCookieに同期');
      }

      console.log('トークン検証API呼び出し開始');
      // トークンの検証
      const response = await fetch('/api/admin/auth/verify', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('トークン検証APIレスポンス:', { 
        status: response.status, 
        ok: response.ok,
        url: response.url 
      });

      const data = await response.json();
      console.log('トークン検証結果:', { 
        authenticated: data.authenticated, 
        hasUser: !!data.user,
        error: data.error 
      });

      if (data.authenticated && data.user) {
        console.log('認証成功:', data.user.username);
        setUser(data.user);
        setIsAuthenticated(true);
        return true;
      } else {
        console.log('認証失敗:', data.error);
        // 認証失敗時はローカルストレージとCookieをクリア
        localStorage.removeItem('adminToken');
        deleteCookie('adminToken');
        setUser(null);
        setIsAuthenticated(false);
        return false;
      }
    } catch (error) {
      console.error('認証確認中にエラーが発生しました:', error);
      localStorage.removeItem('adminToken');
      deleteCookie('adminToken');
      setUser(null);
      setIsAuthenticated(false);
      return false;
    }
  };

  // ログイン処理
  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      console.log('AdminAuthContext: ログイン処理開始', { email });

      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      console.log('ログインAPIレスポンス:', { status: response.status, ok: response.ok });
      const data = await response.json();
      console.log('ログインAPIデータ:', { success: data.success, hasToken: !!data.token, hasUser: !!data.user });

      if (response.ok && data.success) {
        // トークンをローカルストレージに保存
        localStorage.setItem('adminToken', data.token);
        console.log('トークンをローカルストレージに保存しました');

        // トークンをCookieにも保存（ミドルウェアでの認証用）
        setCookie('adminToken', data.token, 1); // 1日間有効
        console.log('トークンをCookieに保存しました');

        setUser(data.user);
        setIsAuthenticated(true);
        console.log('認証状態を更新しました:', { isAuthenticated: true, user: data.user });
        return { success: true };
      } else {
        console.log('ログイン失敗:', data.error);
        return {
          success: false,
          error: data.error || 'ログインに失敗しました。メールアドレスとパスワードを確認してください。'
        };
      }
    } catch (error) {
      console.error('ログイン中にエラーが発生しました:', error);
      return {
        success: false,
        error: 'ログイン処理中にエラーが発生しました。もう一度お試しください。'
      };
    } finally {
      setIsLoading(false);
    }
  };

  // ログアウト処理
  const logout = () => {
    // ローカルストレージからトークンを削除
    localStorage.removeItem('adminToken');

    // Cookieからトークンを削除
    deleteCookie('adminToken');

    setUser(null);
    setIsAuthenticated(false);

    // ログアウト後にログインページにリダイレクト
    window.location.href = '/admin/login';
  };

  // コンテキスト値の作成
  const contextValue: AdminAuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return (
    <AdminAuthContext.Provider value={contextValue}>
      {children}
    </AdminAuthContext.Provider>
  );
}

// カスタムフック
export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
}
