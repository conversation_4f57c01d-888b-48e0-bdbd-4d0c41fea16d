import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('Start seeding...');

  // プロダクション環境では管理者ユーザーの自動作成をスキップ
  // 実際のユーザーアカウントを使用してください
  /*
  // 管理者ユーザーの確認と作成
  const existingAdmin = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  let admin;
  if (!existingAdmin) {
    // 存在しない場合のみ作成
    const hashedPassword = await bcrypt.hash('admin123', 10);
    admin = await prisma.user.create({
      data: {
        id: 'admin-seed-user', // 明示的にIDを設定
        email: '<EMAIL>',
        username: 'admin',
        password: hashedPassword,
        isAdmin: true, // 管理者権限を付与
      },
    });
    console.log(`Created admin user with id: ${admin.id}`);
  } else {
    admin = existingAdmin;
    console.log(`Admin user already exists with id: ${admin.id} (skipping update)`);
  }
  */

  // カテゴリーの作成
  const categories = [
    {
      name: '考古学',
      slug: 'archaeology',
      description: '日本の考古学に関する記事。縄文時代から古墳時代までの遺跡や遺物について考察します。',
      subCategories: [
        { name: '縄文時代', slug: 'jomon', description: '縄文時代の遺跡や遺物に関する記事' },
        { name: '弥生時代', slug: 'yayoi', description: '弥生時代の遺跡や遺物に関する記事' },
        { name: '古墳時代', slug: 'kofun', description: '古墳時代の遺跡や遺物に関する記事' },
      ],
    },
    {
      name: '工芸',
      slug: 'crafts',
      description: '日本の伝統工芸に関する記事。陶芸、染織、木工などの技術や歴史について紹介します。',
      subCategories: [
        { name: '陶芸', slug: 'pottery', description: '陶芸に関する記事' },
        { name: '染織', slug: 'textile', description: '染織に関する記事' },
        { name: '木工', slug: 'woodwork', description: '木工に関する記事' },
      ],
    },
    {
      name: '美術',
      slug: 'fine-arts',
      description: '日本の美術に関する記事。日本画から現代アートまで、様々な表現形式について考察します。',
      subCategories: [
        { name: '日本画', slug: 'nihonga', description: '日本画に関する記事' },
        { name: '現代アート', slug: 'contemporary', description: '現代アートに関する記事' },
      ],
    },
    {
      name: '文学',
      slug: 'literature',
      description: '日本の文学に関する記事。古典から現代文学まで、様々な作品や作家について考察します。',
      subCategories: [
        { name: '古典', slug: 'classics', description: '古典文学に関する記事' },
        { name: '現代文学', slug: 'modern', description: '現代文学に関する記事' },
      ],
    },
    {
      name: '博物館学',
      slug: 'museology',
      description: '博物館学に関する記事。展示方法や収集・保存の技術、博物館の役割などについて考察します。',
      subCategories: [],
    },
  ];

  // カテゴリーとサブカテゴリーの作成
  for (const categoryData of categories) {
    const { subCategories, ...categoryInfo } = categoryData;

    // 既存チェック
    let category = await prisma.category.findUnique({
      where: { slug: categoryInfo.slug },
    });

    if (!category) {
      category = await prisma.category.create({
        data: categoryInfo,
      });
      console.log(`Created category: ${category.name}`);
    } else {
      console.log(`Category already exists: ${category.name}`);
    }

    // サブカテゴリーの作成
    for (const subCategoryData of subCategories) {
      let subCategory = await prisma.subCategory.findFirst({
        where: {
          slug: subCategoryData.slug,
          categoryId: category.id,
        },
      });

      if (!subCategory) {
        subCategory = await prisma.subCategory.create({
          data: {
            ...subCategoryData,
            categoryId: category.id,
          },
        });
        console.log(`Created subcategory: ${subCategory.name}`);
      } else {
        console.log(`Subcategory already exists: ${subCategory.name}`);
      }
    }
  }

  // 記事の作成
  const posts = [
    {
      title: '土偶の謎：縄文時代の精神世界を探る',
      slug: 'dogu-mystery',
      content: `
        <p>縄文時代の土偶には、当時の人々の精神性や世界観が込められています。本記事では、土偶の形状や模様から読み取れる縄文人の思想について考察します。</p>

        <h2>土偶とは何か</h2>
        <p>土偶は、縄文時代に作られた人型の土製品です。その多くは女性の姿を模しており、豊穣や多産を祈願する目的で作られたと考えられています。しかし、その形状や模様は地域や時代によって多様であり、単なる祈願の道具以上の意味を持っていたことが示唆されています。</p>

        <h2>土偶に込められた世界観</h2>
        <p>土偶の多くは、目や口などの顔の部分が大きく誇張されています。これは、見ることや話すことの重要性を示していると考えられます。また、体の各部分には複雑な模様が施されており、これらは当時の人々の宇宙観や自然観を表現していると解釈されています。</p>

        <h2>現代における土偶の意義</h2>
        <p>現代の私たちにとって、土偶は単なる考古学的遺物ではなく、人類の精神文化の発展を理解する上で重要な手がかりとなります。土偶に込められた思想や美意識は、現代のアートやデザインにも影響を与え続けています。</p>
      `,
      excerpt: '縄文時代の土偶には、当時の人々の精神性や世界観が込められています。本記事では、土偶の形状や模様から読み取れる縄文人の思想について考察します。',
      featuredImage: '/images/dogu-figure.jpg',
      published: true,
      featured: true,
      publishedAt: new Date('2023-04-15'),
      categorySlug: 'archaeology',
      subCategorySlug: 'jomon',
    },
    {
      title: '日本の伝統工芸：失われゆく技術と現代における価値',
      slug: 'traditional-crafts',
      content: `
        <p>日本各地に伝わる伝統工芸は、現代社会においてどのような価値を持つのでしょうか。職人の減少と技術継承の課題について考えます。</p>

        <h2>伝統工芸の現状</h2>
        <p>日本の伝統工芸は、長い歴史の中で培われてきた技術と美意識の結晶です。しかし、現代では職人の高齢化や後継者不足により、多くの技術が失われる危機に瀕しています。また、生活様式の変化により、伝統的な工芸品の需要も減少しています。</p>

        <h2>伝統と革新</h2>
        <p>伝統工芸が現代社会で生き残るためには、伝統を守りながらも現代のニーズに合わせた革新が必要です。一部の工芸家は、伝統的な技法を現代的なデザインに取り入れることで、新たな価値を創造しています。</p>

        <h2>伝統工芸の未来</h2>
        <p>伝統工芸の未来は、単なる保存や復元ではなく、現代社会における新たな役割を見出すことにあります。デジタル技術との融合や、サステナビリティの観点からの再評価など、様々な可能性が模索されています。</p>
      `,
      excerpt: '日本各地に伝わる伝統工芸は、現代社会においてどのような価値を持つのでしょうか。職人の減少と技術継承の課題について考えます。',
      featuredImage: '/images/cat-icon.png',
      published: true,
      publishedAt: new Date('2023-05-20'),
      categorySlug: 'crafts',
      subCategorySlug: 'pottery',
    },
    {
      title: '現代アートにおける「わび・さび」の影響',
      slug: 'wabi-sabi-in-modern-art',
      content: `
        <p>日本の美意識である「わび・さび」は、現代アートにどのような影響を与えているのでしょうか。国内外のアーティストの作品を通して探ります。</p>

        <h2>わび・さびとは</h2>
        <p>「わび」と「さび」は、日本の美意識を表す言葉です。「わび」は質素で簡素な美しさを、「さび」は時間の経過による風合いや味わいを意味します。これらの概念は、茶道や禅の思想と深く結びついています。</p>

        <h2>現代アートへの影響</h2>
        <p>20世紀以降、多くの西洋のアーティストが日本の美意識に影響を受けてきました。特に、ミニマリズムやアブストラクト・エクスプレッショニズムの作家たちは、わび・さびの概念を自らの作品に取り入れています。</p>

        <h2>日本の現代アーティスト</h2>
        <p>日本の現代アーティストの中にも、伝統的な美意識を現代的な表現で再解釈する作家が多くいます。彼らは、グローバル化する美術界の中で、日本独自の美意識を新たな形で発信しています。</p>
      `,
      excerpt: '日本の美意識である「わび・さび」は、現代アートにどのような影響を与えているのでしょうか。国内外のアーティストの作品を通して探ります。',
      featuredImage: '/images/bird-icon.png',
      published: true,
      publishedAt: new Date('2023-06-10'),
      categorySlug: 'fine-arts',
      subCategorySlug: 'contemporary',
    },
    {
      title: '博物館の未来：デジタル技術がもたらす新しい鑑賞体験',
      slug: 'future-of-museums',
      content: `
        <p>VRやARなどのデジタル技術は、博物館の展示方法や鑑賞体験をどのように変えていくのでしょうか。国内外の先進的な事例を紹介します。</p>

        <h2>デジタル技術の活用</h2>
        <p>近年、多くの博物館がVR（仮想現実）やAR（拡張現実）などのデジタル技術を活用した展示を導入しています。これらの技術により、来館者はより没入感のある体験ができるようになりました。</p>

        <h2>オンライン展示の可能性</h2>
        <p>COVID-19パンデミックを機に、多くの博物館がオンライン展示を充実させました。これにより、地理的・時間的制約を超えて、世界中の人々が文化遺産にアクセスできるようになっています。</p>

        <h2>課題と展望</h2>
        <p>デジタル技術の活用には、技術的・経済的な課題もあります。また、実物を見る体験の価値をどう考えるかという本質的な問いも投げかけられています。今後は、リアルとデジタルのハイブリッドな展示が主流になっていくでしょう。</p>
      `,
      excerpt: 'VRやARなどのデジタル技術は、博物館の展示方法や鑑賞体験をどのように変えていくのでしょうか。国内外の先進的な事例を紹介します。',
      featuredImage: '/images/owl-icon.png',
      published: true,
      featured: true,
      publishedAt: new Date('2023-07-05'),
      categorySlug: 'museology',
      subCategorySlug: null,
    },
    {
      title: '日本の古典文学に見る自然観：『方丈記』を中心に',
      slug: 'nature-in-hojoki',
      content: `
        <p>鴨長明の『方丈記』に描かれる自然観と、現代の環境問題への示唆について考察します。古典文学から学ぶ自然との共生の知恵とは。</p>

        <h2>『方丈記』とは</h2>
        <p>『方丈記』は、鎌倉時代初期に鴨長明によって書かれた随筆です。無常観を基調としながら、自然災害や社会の変化、そして最終的に長明自身が選んだ隠遁生活について描かれています。</p>

        <h2>『方丈記』に見る自然観</h2>
        <p>長明は、自然の美しさと同時に、その破壊的な力についても鋭い観察眼で描写しています。特に、地震や洪水などの自然災害に対する人間の無力さと、それでも自然と共に生きていかなければならない現実が、現代にも通じるメッセージとして読み取れます。</p>

        <h2>現代への示唆</h2>
        <p>環境問題が深刻化する現代において、『方丈記』に描かれる自然との向き合い方は、多くの示唆を与えてくれます。特に、物質的な豊かさよりも精神的な充足を重視する長明の生き方は、持続可能な社会を考える上でのヒントとなるでしょう。</p>
      `,
      excerpt: '鴨長明の『方丈記』に描かれる自然観と、現代の環境問題への示唆について考察します。古典文学から学ぶ自然との共生の知恵とは。',
      featuredImage: '/images/bird-icon.png',
      published: true,
      publishedAt: new Date('2023-07-15'),
      categorySlug: 'literature',
      subCategorySlug: 'classics',
    },
    {
      title: '民藝運動再考：柳宗悦の思想と現代デザインへの影響',
      slug: 'mingei-movement',
      content: `
        <p>柳宗悦が提唱した民藝運動の本質と、現代のデザイン思想への影響について考察します。「用の美」という概念は現代社会にどのような意味を持つのでしょうか。</p>

        <h2>民藝運動とは</h2>
        <p>民藝運動は、1920年代に柳宗悦によって提唱された、日本の伝統的な民衆的工芸（民藝）を再評価する運動です。柳は、無名の職人によって作られた日常的な道具の中に、真の美を見出しました。</p>

        <h2>「用の美」の思想</h2>
        <p>柳が提唱した「用の美」とは、実用性と美しさが一体となった美意識です。装飾のための装飾ではなく、使用することで初めて完成する美しさを重視しました。この考え方は、現代のミニマリズムやサステナブルデザインにも通じるものがあります。</p>

        <h2>現代デザインへの影響</h2>
        <p>民藝運動の思想は、日本国内だけでなく、世界のデザイン界にも大きな影響を与えています。特に、シンプルで機能的なデザインを重視する北欧デザインとの親和性が指摘されています。現代では、大量生産・大量消費の社会への反省から、民藝の思想が再評価されています。</p>
      `,
      excerpt: '柳宗悦が提唱した民藝運動の本質と、現代のデザイン思想への影響について考察します。「用の美」という概念は現代社会にどのような意味を持つのでしょうか。',
      featuredImage: '/images/cat-icon.png',
      published: true,
      publishedAt: new Date('2023-07-25'),
      categorySlug: 'crafts',
      subCategorySlug: 'woodwork',
    },
  ];

  // 記事の作成
  for (const postData of posts) {
    const { categorySlug, subCategorySlug, ...postInfo } = postData;

    // カテゴリーの取得
    const category = await prisma.category.findUnique({
      where: { slug: categorySlug },
    });

    if (!category) {
      console.log(`Category with slug ${categorySlug} not found. Skipping post: ${postInfo.title}`);
      continue;
    }

    // サブカテゴリーの取得（存在する場合）
    let subCategory = null;
    if (subCategorySlug) {
      subCategory = await prisma.subCategory.findFirst({
        where: {
          slug: subCategorySlug,
          categoryId: category.id,
        },
      });

      if (!subCategory) {
        console.log(`SubCategory with slug ${subCategorySlug} not found. Creating post without subcategory: ${postInfo.title}`);
      }
    }

    // 既存の記事を確認
    const existingPost = await prisma.post.findUnique({
      where: { slug: postInfo.slug },
    });

    if (!existingPost) {
      // 存在しない場合のみ作成
      const post = await prisma.post.create({
        data: {
          ...postInfo,
          categoryId: category.id,
          subCategoryId: subCategory?.id || null,
          authorId: admin.id,
        },
      });
      console.log(`Created post: ${post.title}`);
    } else {
      console.log(`Post already exists: ${postInfo.title} (skipping update)`);
    }
  }

  // サイト設定の作成
  console.log('Creating site settings...');

  const siteSettings = [
    {
      key: 'site_title',
      value: '造物者の空気感',
      description: 'サイトのタイトル',
    },
    {
      key: 'site_description',
      value: '日本の美術、工芸、考古学、文化についての考察と発見の記録',
      description: 'サイトの説明',
    },
    {
      key: 'site_logo',
      value: '/images/dogu-logo.png',
      description: 'サイトのロゴ画像',
    },
    {
      key: 'profile_name',
      value: '造物者',
      description: 'プロフィール名',
    },
    {
      key: 'profile_image',
      value: '/images/owl-icon.png',
      description: 'プロフィール画像',
    },
    {
      key: 'profile_description',
      value: '美術史研究者。日本の伝統工芸と現代アートの接点に関心を持つ。',
      description: 'プロフィールの説明',
    },
    {
      key: 'profile_title',
      value: '美術史研究者 / 工芸愛好家',
      description: '肩書き',
    },
    {
      key: 'twitter_url',
      value: 'https://twitter.com/example',
      description: 'Twitter URL',
    },
    {
      key: 'instagram_url',
      value: 'https://instagram.com/example',
      description: 'Instagram URL',
    },
    {
      key: 'youtube_url',
      value: 'https://youtube.com/example',
      description: 'YouTube URL',
    },
    {
      key: 'contact_email',
      value: '<EMAIL>',
      description: 'お問い合わせメールアドレス',
    },
    {
      key: 'contact_description',
      value: '通常、お問い合わせから2営業日以内にご返信いたします。',
      description: 'お問い合わせの説明',
    },
  ];

  for (const setting of siteSettings) {
    // 既存の設定を確認
    const existingSetting = await prisma.siteSettings.findUnique({
      where: { key: setting.key },
    });

    if (!existingSetting) {
      // 存在しない場合のみ作成
      await prisma.siteSettings.create({
        data: setting,
      });
      console.log(`Created site setting: ${setting.key}`);
    } else {
      console.log(`Site setting already exists: ${setting.key} (skipping update)`);
    }
  }

  console.log('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
