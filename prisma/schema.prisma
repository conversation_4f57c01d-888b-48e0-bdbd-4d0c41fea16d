generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
  engineType    = "binary"
}

generator seed {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
  engineType    = "binary"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Category {
  id            String        @id @default(cuid())
  name          String        @unique
  slug          String        @unique
  description   String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  posts         Post[]
  subCategories SubCategory[]
}

model ContactMessage {
  id        String    @id @default(cuid())
  name      String
  email     String
  subject   String
  message   String
  isRead    Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  readAt    DateTime?
}

model Image {
  id        String   @id @default(cuid())
  filename  String
  alt       String?
  data      String
  thumbnail String?
  postId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
}

model Post {
  id            String       @id @default(cuid())
  title         String
  slug          String       @unique
  content       String
  excerpt       String?
  featuredImage String?
  published     Boolean      @default(false)
  publishedAt   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  authorId      String
  categoryId    String
  subCategoryId String?
  featured      Boolean      @default(false)
  images        Image[]
  user          User         @relation(fields: [authorId], references: [id])
  category      Category     @relation(fields: [categoryId], references: [id])
  subCategory   SubCategory? @relation(fields: [subCategoryId], references: [id])
}

model SiteSettings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model SubCategory {
  id          String   @id @default(cuid())
  name        String
  slug        String
  description String?
  categoryId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  posts       Post[]
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([slug, categoryId])
}

model User {
  id              String   @id @default(cuid())
  username        String   @unique
  email           String   @unique
  password        String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  bio             String?
  detailedProfile String?
  isAdmin         Boolean  @default(false)
  profileImage    String?
  posts           Post[]
}
