# NeonDB有償プランへのマイグレーションガイド

## 概要
このガイドでは、NeonDBのFreeプランから有償プランへデータを維持したままマイグレーションする手順を説明します。

## 新しい接続情報
```
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

## マイグレーション手順

### ステップ1: 現在のデータをエクスポート

1. **現在のDATABASE_URLを環境変数に設定**
   ```bash
   export DATABASE_URL="現在のNeonDB_FreeプランのURL"
   ```

2. **データをエクスポート**
   ```bash
   node migration-script.js export
   ```
   
   このコマンドで`data-export.json`ファイルが作成されます。

### ステップ2: 新しいデータベースの準備

1. **新しいDATABASE_URLに変更**
   ```bash
   export DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
   ```

2. **Prismaクライアントを再生成**
   ```bash
   npx prisma generate
   ```

3. **新しいデータベースにマイグレーション実行**
   ```bash
   npx prisma migrate deploy
   ```

### ステップ3: データのインポート

1. **データをインポート**
   ```bash
   node migration-script.js import
   ```

### ステップ4: 本番環境の設定更新

#### Cloud Runの場合（Secret Managerを使用）

1. **Secret Managerで新しいDATABASE_URLを更新**
   ```bash
   echo "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" | gcloud secrets versions add DATABASE_URL --data-file=-
   ```

2. **Cloud Runサービスを再デプロイ**
   ```bash
   gcloud run deploy zobutsusha \
     --image=gcr.io/zobutsushacom/zobutsusha:latest \
     --region=us-central1 \
     --set-secrets=DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,SETUP_SECRET_KEY=SETUP_SECRET_KEY:latest \
     --set-env-vars=NODE_ENV=production,PORT=8080,NEXTAUTH_URL=https://your-service-url.run.app
   ```

#### 直接環境変数を設定する場合

Cloud Runコンソールで以下の環境変数を更新：
- `DATABASE_URL`: `postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require`

### ステップ5: 動作確認

1. **データベース接続の確認**
   ```bash
   npx prisma db pull
   ```

2. **アプリケーションの動作確認**
   - ローカルで起動してデータが正しく表示されることを確認
   - 本番環境でもデータが正しく表示されることを確認

## 注意事項

### バックアップの重要性
- エクスポートしたデータ（`data-export.json`）は安全な場所に保管してください
- 万が一に備えて、PostgreSQLのpg_dumpも取得することを推奨します

### ダウンタイムの最小化
1. メンテナンスモードページを準備
2. DNS TTLを短く設定（事前準備）
3. マイグレーション中はメンテナンスページを表示
4. 完了後に新しい環境にトラフィックを切り替え

### pg_dumpを使った追加バックアップ
```bash
# 現在のデータベースの完全バックアップ
pg_dump "現在のDATABASE_URL" > backup_$(date +%Y%m%d_%H%M%S).sql

# 新しいデータベースにリストア（必要に応じて）
psql "新しいDATABASE_URL" < backup_20241201_143000.sql
```

## トラブルシューティング

### エラー: P2002 - Unique constraint failed
データの重複が原因の場合、以下を確認：
1. 新しいデータベースが空であることを確認
2. 既存データがある場合は先にクリア

### エラー: Connection timeout
接続設定を確認：
1. DATABASE_URLが正しいことを確認
2. ネットワーク接続を確認
3. NeonDBの接続制限を確認

### パフォーマンスの改善
新しい有償プランでは以下の機能が利用可能：
- より多くの接続数
- より大きなストレージ
- 高速なクエリ実行

## 完了後のクリーンアップ
1. 古いNeonDBプロジェクトの削除
2. 一時ファイル（`data-export.json`）の安全な削除
3. 環境変数の更新確認 