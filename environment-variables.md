# Required Environment Variables for Cloud Run

Below is a list of environment variables that need to be configured in your Google Cloud Run service. These should be set in the Cloud Run console or through the gcloud command line.

## Essential Variables

| Variable Name | Description | Example Value | Notes |
|---------------|-------------|--------------|-------|
| `DATABASE_URL` | PostgreSQL connection string for NeonDB | `postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require` | **SENSITIVE** - Use Secret Manager |
| `NEXTAUTH_SECRET` | Secret key for NextAuth.js | `your-random-secret-key` | **SENSITIVE** - Use Secret Manager |
| `NEXTAUTH_URL` | Base URL of the application | `https://your-cloud-run-url.run.app` | Should match your Cloud Run service URL |
| `SETUP_SECRET_KEY` | Secret key for initial admin setup | `your-setup-secret-key` | **SENSITIVE** - Use Secret Manager |
| `NODE_ENV` | Environment mode | `production` | Should always be set to production |
| `PORT` | Port the application listens on | `8080` | Cloud Run typically uses port 8080 |

## Optional Variables

| Variable Name | Description | Example Value | Notes |
|---------------|-------------|--------------|-------|
| `NEXT_PUBLIC_SITE_NAME` | Site name for metadata | `造物者の空気感` | Public variable |
| `NEXT_PUBLIC_ARTIST_NAME` | Artist name for metadata | `Artist Name` | Public variable |
| `NEXT_PUBLIC_ARTIST_PROFESSION` | Artist profession for metadata | `現代日本画アーティスト` | Public variable |

## Setting Environment Variables in Cloud Run

You can set these environment variables in the Cloud Run console:

1. Go to Cloud Run in the Google Cloud Console
2. Select your service
3. Click "Edit and Deploy New Revision"
4. Scroll down to "Container, Networking, Security"
5. Expand "Variables & Secrets"
6. Add each environment variable

For sensitive information like `DATABASE_URL`, `NEXTAUTH_SECRET`, and `SETUP_SECRET_KEY`, it's recommended to use Secret Manager:

1. Create secrets in Secret Manager
2. Reference these secrets in your Cloud Run configuration

## Using Secret Manager with gcloud

```bash
# Create secrets
gcloud secrets create DATABASE_URL --data-file=/path/to/database_url.txt
gcloud secrets create NEXTAUTH_SECRET --data-file=/path/to/nextauth_secret.txt
gcloud secrets create SETUP_SECRET_KEY --data-file=/path/to/setup_secret_key.txt

# Update Cloud Run to use secrets
gcloud run deploy zobutsusha \
  --image=gcr.io/zobutsushacom/zobutsusha:latest \
  --region=us-central1 \
  --set-secrets=DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,SETUP_SECRET_KEY=SETUP_SECRET_KEY:latest \
  --set-env-vars=NODE_ENV=production,PORT=8080,NEXTAUTH_URL=https://your-service-url.run.app
```
