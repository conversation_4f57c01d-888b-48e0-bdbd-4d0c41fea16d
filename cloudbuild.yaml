steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/zobutsusha:$COMMIT_SHA', '.']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/zobutsusha:$COMMIT_SHA']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'zobutsusha'
      - '--image'
      - 'gcr.io/$PROJECT_ID/zobutsusha:$COMMIT_SHA'
      - '--region'
      - 'asia-northeast1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '4Gi'
      - '--cpu'
      - '2'
      - '--min-instances'
      - '1'
      - '--max-instances'
      - '10'
      - '--timeout'
      - '300s'
      - '--concurrency'
      - '80'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - 'DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
      - '--set-env-vars'
      - 'NEXTAUTH_SECRET=gguuP9eT0lNi7JKWcq2TtyEqWVnU7lDS4squl96CqWU='
      - '--set-env-vars'
      - 'SETUP_SECRET_KEY=il55587azrh0F0QlDWiPDSOHuy0uawa/UMr3plpaKmA='
      - '--set-env-vars'
      - 'NODE_ENV=production'
      - '--set-env-vars'
      - 'NEXTAUTH_URL=https://www.zobutsusha.com'
      - '--set-env-vars'
      - 'NEXT_PUBLIC_SITE_URL=https://www.zobutsusha.com'

# Store images in Google Container Registry
images:
  - 'gcr.io/$PROJECT_ID/zobutsusha:$COMMIT_SHA'

# Set a longer timeout for the build (30 minutes)
timeout: 1800s

options:
  logging: CLOUD_LOGGING_ONLY
