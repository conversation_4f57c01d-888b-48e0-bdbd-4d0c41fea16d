#!/bin/bash

# Update subcategories route
sed -i '' 's/interface RouteParams {/\/\/ Route parameter type/g' src/app/api/admin/subcategories/[id]/route.ts
sed -i '' 's/export async function GET(request: Request, { params }: RouteParams) {/export async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/subcategories/[id]/route.ts
sed -i '' 's/export async function PUT(request: Request, { params }: RouteParams) {/export async function PUT(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/subcategories/[id]/route.ts
sed -i '' 's/export async function DELETE(request: Request, { params }: RouteParams) {/export async function DELETE(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/subcategories/[id]/route.ts

# Update messages route
sed -i '' 's/interface RouteParams {/\/\/ Route parameter type/g' src/app/api/admin/messages/[id]/route.ts
sed -i '' 's/export async function GET(request: Request, { params }: RouteParams) {/export async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/messages/[id]/route.ts
sed -i '' 's/export async function DELETE(request: Request, { params }: RouteParams) {/export async function DELETE(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/messages/[id]/route.ts

# Update posts route
sed -i '' 's/interface RouteParams {/\/\/ Route parameter type/g' src/app/api/admin/posts/[id]/route.ts
sed -i '' 's/export async function GET(request: Request, { params }: RouteParams) {/export async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/posts/[id]/route.ts
sed -i '' 's/export async function PUT(request: Request, { params }: RouteParams) {/export async function PUT(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/posts/[id]/route.ts
sed -i '' 's/export async function DELETE(request: Request, { params }: RouteParams) {/export async function DELETE(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/posts/[id]/route.ts

# Update users route
sed -i '' 's/interface RouteParams {/\/\/ Route parameter type/g' src/app/api/admin/users/[id]/route.ts
sed -i '' 's/export async function GET(request: Request, { params }: RouteParams) {/export async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/users/[id]/route.ts
sed -i '' 's/export async function PUT(request: Request, { params }: RouteParams) {/export async function PUT(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/users/[id]/route.ts
sed -i '' 's/export async function DELETE(request: Request, { params }: RouteParams) {/export async function DELETE(\n  request: Request,\n  { params }: { params: { id: string } }\n) {/g' src/app/api/admin/users/[id]/route.ts
