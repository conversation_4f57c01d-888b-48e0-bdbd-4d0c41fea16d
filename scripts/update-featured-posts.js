const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // 全記事を取得
    const posts = await prisma.post.findMany();

    console.log('注目記事（featured）の設定を更新します...');

    // 注目記事の設定
    const featuredPosts = [
      'art-japanese-art-mystery', // 土偶の謎：縄文時代の精神世界を探る
      'technology-ai-tech-museums', // 博物館の未来：デジタル技術がもたらす新しい鑑賞体験
      'music-j-pop-crafts', // 日本の伝統工芸：失われゆく技術と現代における価値
    ];

    // すべての記事のfeaturedフラグをリセット
    await prisma.post.updateMany({
      data: {
        featured: false
      }
    });
    console.log('すべての記事のfeaturedフラグをリセットしました。');

    // 指定した記事のfeaturedフラグを設定
    for (const slug of featuredPosts) {
      const post = posts.find(p => p.slug === slug);
      if (!post) {
        console.log(`記事「${slug}」が見つかりませんでした。`);
        continue;
      }

      await prisma.post.update({
        where: { id: post.id },
        data: {
          featured: true
        }
      });

      console.log(`記事「${post.title}」を注目記事に設定しました。`);
    }

    console.log('注目記事の更新が完了しました。');

    // 注目記事の確認
    const updatedFeaturedPosts = await prisma.post.findMany({
      where: {
        featured: true
      },
      select: {
        title: true,
        slug: true,
        featured: true
      }
    });

    console.log('\n=== 注目記事一覧 ===');
    updatedFeaturedPosts.forEach(post => {
      console.log(`- ${post.title} (${post.slug})`);
    });

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
