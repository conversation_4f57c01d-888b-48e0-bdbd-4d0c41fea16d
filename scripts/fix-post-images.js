const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Markdown形式の画像参照をHTML img要素に変換する
 */
function convertMarkdownImagesToHtml(content) {
  // Markdown形式の画像参照を検出する正規表現
  const markdownImageRegex = /!\[(.*?)\]\((data:image\/[^;]+;base64,[^)]+)\)/g;
  
  // Markdown形式の画像参照をHTML img要素に置換
  return content.replace(markdownImageRegex, (match, alt, src) => {
    return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto;" />`;
  });
}

/**
 * 全ての記事の内容を修正する
 */
async function fixAllPostImages() {
  try {
    console.log('記事内の画像表示を修正しています...');
    
    // 全ての記事を取得
    const posts = await prisma.post.findMany();
    
    let updatedCount = 0;
    
    // 各記事の内容を処理
    for (const post of posts) {
      // Markdown形式の画像参照を検出
      if (post.content && post.content.includes('![画像](data:image')) {
        console.log(`記事「${post.title}」の画像を修正しています...`);
        
        // 内容を修正
        const updatedContent = convertMarkdownImagesToHtml(post.content);
        
        // 記事を更新
        await prisma.post.update({
          where: { id: post.id },
          data: { content: updatedContent },
        });
        
        updatedCount++;
      }
    }
    
    console.log(`${updatedCount}件の記事を修正しました。`);
    
  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
fixAllPostImages();
