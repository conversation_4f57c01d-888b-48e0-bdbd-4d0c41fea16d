#!/bin/bash

# Nginxの設定を更新するスクリプト
# 画像アップロードの問題を解決するために、client_max_body_sizeを設定します

# 色の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Nginxの設定を確認・更新します...${NC}"

# Nginxの設定ファイルのパスを検出
if [ -f /opt/homebrew/etc/nginx/nginx.conf ]; then
    # macOS (Homebrew) - Apple Silicon
    NGINX_CONF="/opt/homebrew/etc/nginx/nginx.conf"
    NGINX_RESTART_CMD="brew services restart nginx"
elif [ -f /usr/local/etc/nginx/nginx.conf ]; then
    # macOS (Homebrew) - Intel
    NGINX_CONF="/usr/local/etc/nginx/nginx.conf"
    NGINX_RESTART_CMD="brew services restart nginx"
elif [ -f /etc/nginx/nginx.conf ]; then
    # Linux
    NGINX_CONF="/etc/nginx/nginx.conf"
    NGINX_RESTART_CMD="sudo systemctl restart nginx"
else
    echo -e "${RED}Nginxの設定ファイルが見つかりません。${NC}"
    exit 1
fi

echo "Nginxの設定ファイル: $NGINX_CONF"

# client_max_body_sizeの設定を確認
if grep -q "client_max_body_size" $NGINX_CONF; then
    CURRENT_SIZE=$(grep -o "client_max_body_size [0-9]*[mM];" $NGINX_CONF | grep -o "[0-9]*[mM]")
    echo -e "${GREEN}現在のclient_max_body_size: $CURRENT_SIZE${NC}"

    # 数値部分だけを取得
    CURRENT_SIZE_NUM=$(echo $CURRENT_SIZE | grep -o "[0-9]*")

    # 5M未満の場合は更新
    if [[ $CURRENT_SIZE_NUM -lt 5 ]]; then
        echo -e "${YELLOW}client_max_body_sizeを5Mに更新します...${NC}"

        # バックアップを作成
        cp $NGINX_CONF ${NGINX_CONF}.bak

        # 設定を更新
        sed -i.bak "s/client_max_body_size [0-9]*[mM];/client_max_body_size 5M;/g" $NGINX_CONF

        echo -e "${GREEN}設定を更新しました。Nginxを再起動します...${NC}"
        eval $NGINX_RESTART_CMD
    else
        echo -e "${GREEN}client_max_body_sizeは既に十分な値に設定されています。${NC}"
    fi
else
    echo -e "${YELLOW}client_max_body_sizeの設定が見つかりません。追加します...${NC}"

    # バックアップを作成
    cp $NGINX_CONF ${NGINX_CONF}.bak

    # httpブロック内に設定を追加
    if grep -q "http {" $NGINX_CONF; then
        sed -i.bak "/http {/a\\    client_max_body_size 5M;" $NGINX_CONF
        echo -e "${GREEN}設定を追加しました。Nginxを再起動します...${NC}"
        eval $NGINX_RESTART_CMD
    else
        echo -e "${RED}httpブロックが見つかりません。手動で設定を追加してください。${NC}"
        echo "以下の行を追加してください:"
        echo "client_max_body_size 5M;"
    fi
fi

echo -e "${GREEN}Nginxの設定確認・更新が完了しました。${NC}"
