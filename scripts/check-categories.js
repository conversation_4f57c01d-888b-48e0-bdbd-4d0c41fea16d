const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // カテゴリーとサブカテゴリーの取得
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true,
      },
    });

    console.log('=== カテゴリーとサブカテゴリーの一覧 ===');
    categories.forEach(category => {
      console.log(`カテゴリー: ${category.name} (${category.slug})`);
      if (category.subCategories.length > 0) {
        console.log('  サブカテゴリー:');
        category.subCategories.forEach(subCategory => {
          console.log(`    - ${subCategory.name} (${subCategory.slug})`);
        });
      } else {
        console.log('  サブカテゴリーなし');
      }
      console.log('');
    });

    // 記事の取得
    const posts = await prisma.post.findMany({
      include: {
        category: true,
        subCategory: true,
      },
    });

    console.log('=== 記事のカテゴリー/サブカテゴリー設定 ===');
    posts.forEach(post => {
      console.log(`記事: ${post.title} (${post.slug})`);
      console.log(`  カテゴリー: ${post.category.name} (${post.category.slug})`);
      console.log(`  サブカテゴリー: ${post.subCategory ? post.subCategory.name + ' (' + post.subCategory.slug + ')' : 'なし'}`);
      console.log('');
    });

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
