const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // 全カテゴリーとサブカテゴリーを取得
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true,
      },
    });

    // 全記事を取得
    const posts = await prisma.post.findMany();

    console.log('記事のカテゴリーとサブカテゴリーを更新します...');

    // 新しいカテゴリーとサブカテゴリーの割り当て
    const newAssignments = [
      // 芸術カテゴリーに記事を割り当て
      {
        title: '土偶の謎：縄文時代の精神世界を探る',
        categorySlug: 'art',
        subCategorySlug: 'japanese-art'
      },
      // 音楽カテゴリーに記事を割り当て
      {
        title: '日本の伝統工芸：失われゆく技術と現代における価値',
        categorySlug: 'music',
        subCategorySlug: 'j-pop'
      },
      // スポーツカテゴリーに記事を割り当て
      {
        title: '現代アートにおける「わび・さび」の影響',
        categorySlug: 'sports',
        subCategorySlug: 'sports-viewing'
      },
      // テクノロジーカテゴリーに記事を割り当て
      {
        title: '博物館の未来：デジタル技術がもたらす新しい鑑賞体験',
        categorySlug: 'technology',
        subCategorySlug: 'ai-tech'
      },
      // 生活カテゴリーに記事を割り当て
      {
        title: '日本の古典文学に見る自然観：『方丈記』を中心に',
        categorySlug: 'daily-life',
        subCategorySlug: 'society'
      },
      // 民藝運動の記事は工芸カテゴリーのままにする
      {
        title: '民藝運動再考：柳宗悦の思想と現代デザインへの影響',
        categorySlug: 'crafts',
        subCategorySlug: 'woodwork'
      }
    ];

    // 記事を更新
    for (const assignment of newAssignments) {
      // 記事を検索
      const post = posts.find(p => p.title === assignment.title);
      if (!post) {
        console.log(`記事「${assignment.title}」が見つかりませんでした。`);
        continue;
      }

      // カテゴリーを検索
      const category = categories.find(c => c.slug === assignment.categorySlug);
      if (!category) {
        console.log(`カテゴリー「${assignment.categorySlug}」が見つかりませんでした。`);
        continue;
      }

      // サブカテゴリーを検索
      let subCategory = null;
      if (assignment.subCategorySlug) {
        subCategory = category.subCategories.find(sc => sc.slug === assignment.subCategorySlug);
        if (!subCategory) {
          console.log(`サブカテゴリー「${assignment.subCategorySlug}」が見つかりませんでした。`);
          continue;
        }
      }

      // 記事を更新
      await prisma.post.update({
        where: { id: post.id },
        data: {
          categoryId: category.id,
          subCategoryId: subCategory ? subCategory.id : null,
          // スラッグも更新
          slug: `${category.slug}-${subCategory ? subCategory.slug + '-' : ''}${post.slug.split('-').pop()}`
        }
      });

      console.log(`記事「${post.title}」を更新しました。`);
      console.log(`  カテゴリー: ${category.name} (${category.slug})`);
      console.log(`  サブカテゴリー: ${subCategory ? subCategory.name + ' (' + subCategory.slug + ')' : 'なし'}`);
      console.log('');
    }

    console.log('更新が完了しました。');

  } catch (error) {
    console.error('エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
