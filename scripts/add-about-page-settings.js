/**
 * About ページの設定を追加するスクリプト
 * 
 * 使用方法:
 * node scripts/add-about-page-settings.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// About ページのデフォルト設定
const aboutPageSettings = [
  {
    key: 'about_section_intro',
    value: '「造物者の空気感」へようこそ。このブログでは、趣味としての日本の美術、音楽、スポーツ観戦や、その他生活についての考察や発見を記録しています。',
    description: 'About ページの「はじめまして」セクションの内容',
  },
  {
    key: 'about_section_bio',
    value: '高校時代から勉強もせずコンビニエンスストアでアルバイト。大学に入ってからもコンビニエンスストアでアルバイトをしながら、仲間とマージャンの日々を過ごしました。社会人になってからは、スーパーマーケット会社に勤務し、アメリカに1年間留学。帰国後、システム設計、開発、導入などの業務に25年間携わってきました。\n\nそろそろ穏やかに、気ままな生活をしたいと思い独立。現在、未婚妻の芸術活動を支援する傍ら、AIを使った開発などを楽しんでいます。',
    description: 'About ページの「経歴」セクションの内容',
  },
  {
    key: 'about_section_blog',
    value: 'このブログは、私のこれまでの思い出や思い入れのある事項、また、新しく学んだことなどを無作為に記録するためのブログです。',
    description: 'About ページの「ブログについて」セクションの内容',
  },
  {
    key: 'about_section_contact',
    value: 'ご質問やご意見がありましたら、[お問い合わせフォーム](/contact)からお気軽にご連絡ください。',
    description: 'About ページの「連絡先」セクションの内容',
  },
];

async function main() {
  console.log('About ページの設定を追加しています...');

  for (const setting of aboutPageSettings) {
    // 既存の設定を確認
    const existingSetting = await prisma.siteSettings.findUnique({
      where: { key: setting.key },
    });

    if (existingSetting) {
      console.log(`設定 "${setting.key}" は既に存在します。スキップします。`);
    } else {
      // 新しい設定を作成
      await prisma.siteSettings.create({
        data: setting,
      });
      console.log(`設定 "${setting.key}" を追加しました。`);
    }
  }

  console.log('About ページの設定の追加が完了しました。');
}

main()
  .catch((e) => {
    console.error('エラーが発生しました:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
