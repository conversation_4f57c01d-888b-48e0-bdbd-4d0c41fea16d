const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const sourceDir = path.join(__dirname, '../public/images');
const targetDir = path.join(__dirname, '../public/images/optimized');

// 出力ディレクトリが存在しない場合は作成
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 画像ファイルの一覧を取得
const imageFiles = fs.readdirSync(sourceDir).filter(file => {
  const ext = path.extname(file).toLowerCase();
  return ['.jpg', '.jpeg', '.png', '.webp'].includes(ext);
});

// 各画像を最適化
async function optimizeImages() {
  console.log('画像の最適化を開始します...');
  
  for (const file of imageFiles) {
    // 元のファイルパスと出力先のファイルパス
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);
    
    // ディレクトリの場合はスキップ
    if (fs.statSync(sourcePath).isDirectory()) continue;
    
    try {
      console.log(`最適化中: ${file}`);
      
      // ファイル拡張子を取得
      const ext = path.extname(file).toLowerCase();
      
      // 画像処理
      let sharpInstance = sharp(sourcePath);
      
      // PNGの場合
      if (ext === '.png') {
        await sharpInstance
          .resize(800) // 最大幅を800pxに制限
          .png({ quality: 80, compressionLevel: 9 })
          .toFile(targetPath);
      } 
      // JPG/JPEGの場合
      else if (ext === '.jpg' || ext === '.jpeg') {
        await sharpInstance
          .resize(800) // 最大幅を800pxに制限
          .jpeg({ quality: 80 })
          .toFile(targetPath);
      }
      // WEBPの場合
      else if (ext === '.webp') {
        await sharpInstance
          .resize(800) // 最大幅を800pxに制限
          .webp({ quality: 80 })
          .toFile(targetPath);
      }
      
      console.log(`最適化完了: ${file}`);
    } catch (error) {
      console.error(`エラー (${file}):`, error);
    }
  }
  
  console.log('すべての画像の最適化が完了しました。');
}

// 実行
optimizeImages();
