# Nginxの設定確認

画像アップロードで500エラーが発生する場合、Nginxの設定を確認してください。

## client_max_body_sizeの設定

Nginxの設定ファイル（通常は `/etc/nginx/nginx.conf` または `/etc/nginx/conf.d/default.conf`）に以下の設定を追加してください：

```nginx
http {
    # 他の設定...
    
    # リクエストボディサイズの制限を5MBに設定
    client_max_body_size 5M;
    
    # 他の設定...
}
```

または、特定のサーバーブロックにのみ適用する場合：

```nginx
server {
    # 他の設定...
    
    # リクエストボディサイズの制限を5MBに設定
    client_max_body_size 5M;
    
    # 他の設定...
}
```

## バッファサイズの設定

大きなリクエストを処理するために、バッファサイズも調整することをお勧めします：

```nginx
http {
    # 他の設定...
    
    # クライアントリクエストボディのバッファサイズ
    client_body_buffer_size 1M;
    
    # 他の設定...
}
```

## 設定変更後の再起動

設定を変更した後は、Nginxを再起動してください：

```bash
sudo systemctl restart nginx
# または
sudo service nginx restart
```

## ログの確認

エラーが発生した場合は、Nginxのエラーログを確認してください：

```bash
sudo tail -f /var/log/nginx/error.log
```

## Node.jsの設定

Node.jsサーバー側でも、リクエストボディサイズの制限を設定している場合があります。Next.jsのAPI Routeでは、`config`オブジェクトで設定できます：

```typescript
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '5mb',
    },
  },
};
```

この設定は既に`src/app/api/admin/images/process/route.ts`に追加されています。
