const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true
      }
    });
    console.log('Categories in database:');
    console.log(JSON.stringify(categories, null, 2));
  } catch (error) {
    console.error('Error fetching categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
