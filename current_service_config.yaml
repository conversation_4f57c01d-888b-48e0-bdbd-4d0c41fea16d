apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    run.googleapis.com/urls: '["https://zobutsusha-629524443569.us-central1.run.app","https://zobutsusha-kd52h63gkq-uc.a.run.app"]'
  labels:
    cloud.googleapis.com/location: us-central1
  name: zobutsusha
  namespace: '629524443569'
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '10'
        run.googleapis.com/client-name: gcloud
        run.googleapis.com/client-version: 523.0.0
        run.googleapis.com/startup-cpu-boost: 'true'
      labels:
        client.knative.dev/nonce: xdirwnyzeg
        run.googleapis.com/startupProbeType: Default
    spec:
      containerConcurrency: 80
      containers:
      - env:
        - name: DATABASE_URL
          value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
        - name: NEXTAUTH_SECRET
          value: gguuP9eT0lNi7JKWcq2TtyEqWVnU7lDS4squl96CqWU=
        - name: SETUP_SECRET_KEY
          value: il55587azrh0F0QlDWiPDSOHuy0uawa/UMr3plpaKmA=
        - name: NODE_ENV
          value: production
        - name: NEXTAUTH_URL
          value: https://www.zobutsusha.com
        - name: NEXT_PUBLIC_SITE_URL
          value: https://www.zobutsusha.com
        image: gcr.io/zobutsushacom/zobutsusha:8887cb0dc43b5d7a0c6d4de4d3f20877bebd949c
        name: zobutsusha-1
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '1'
            memory: 1Gi
        startupProbe:
          failureThreshold: 1
          periodSeconds: 240
          tcpSocket:
            port: 8080
          timeoutSeconds: 240
      serviceAccountName: <EMAIL>
      timeoutSeconds: 300
  traffic:
  - latestRevision: true
    percent: 100
