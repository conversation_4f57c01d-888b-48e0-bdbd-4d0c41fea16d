const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // ユーザー情報を取得（パスワードを含む）
    const users = await prisma.user.findMany();
    
    // パスワードを除いた情報を表示
    const sanitizedUsers = users.map(user => {
      const { password, ...rest } = user;
      return {
        ...rest,
        passwordLength: password ? password.length : 0,
        passwordStart: password ? password.substring(0, 10) + '...' : null
      };
    });
    
    console.log('Users in database:');
    console.log(JSON.stringify(sanitizedUsers, null, 2));
    
    // ユーザー数を表示
    console.log(`Total users: ${users.length}`);
    
  } catch (error) {
    console.error('Error fetching users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
