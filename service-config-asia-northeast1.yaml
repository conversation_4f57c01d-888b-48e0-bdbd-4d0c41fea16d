apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
  labels:
    cloud.googleapis.com/location: asia-northeast1
  name: zobutsusha
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '10'
        run.googleapis.com/client-name: gcloud
        run.googleapis.com/startup-cpu-boost: 'true'
      labels:
        run.googleapis.com/startupProbeType: Default
    spec:
      containerConcurrency: 80
      containers:
      - env:
        - name: DATABASE_URL
          value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
        - name: NEXTAUTH_SECRET
          value: gguuP9eT0lNi7JKWcq2TtyEqWVnU7lDS4squl96CqWU=
        - name: SETUP_SECRET_KEY
          value: il55587azrh0F0QlDWiPDSOHuy0uawa/UMr3plpaKmA=
        - name: NODE_ENV
          value: production
        - name: NEXTAUTH_URL
          value: https://www.zobutsusha.com
        - name: NEXT_PUBLIC_SITE_URL
          value: https://www.zobutsusha.com
        image: gcr.io/zobutsushacom/zobutsusha:latest
        name: zobutsusha-1
        ports:
        - containerPort: 8080
          name: http1
        resources:
          limits:
            cpu: '2'      # Updated to match cloudbuild.yaml
            memory: 4Gi   # Updated to match cloudbuild.yaml
        startupProbe:
          failureThreshold: 1
          periodSeconds: 240
          tcpSocket:
            port: 8080
          timeoutSeconds: 240
      timeoutSeconds: 300
  traffic:
  - latestRevision: true
    percent: 100 