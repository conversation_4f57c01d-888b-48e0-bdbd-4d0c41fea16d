// Script to update a user to be an admin
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Update the user with ID cmawoc8u30010h6cglj3lmjs1 to be an admin
    const updatedUser = await prisma.user.update({
      where: {
        id: 'cmawoc8u30010h6cglj3lmjs1', // Replace with the actual user ID
      },
      data: {
        isAdmin: true,
        profileImage: '/images/owl-icon.png',
        detailedProfile: 'システム開発、音楽、サッカー観戦などが趣味です。このサイトの管理者として、様々な記事を投稿していきます。',
      },
    });

    console.log('User updated successfully:', updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
